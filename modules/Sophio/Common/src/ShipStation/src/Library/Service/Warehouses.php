<?php

namespace Sophio\Common\ShipStation\src\Library\Service;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Sophio\Common\Models\FBS\Customer;
use Sophio\Common\Models\FBS\Store;
use Sophio\Common\Models\FBS\Supplier;
use Sophio\Common\ShipStation\src\Library\ShipStationManager;

class Warehouses
{
    protected ShipStationManager $shipstationManager;

    public function __construct(ShipStationManager $shipStationManager)
    {
        $this->shipstationManager = $shipStationManager;
    }

    public function getWarehouseById($warehouseId)
    {
        return $this->shipstationManager->getWarehouse($warehouseId);

    }

    public function createWarehouse(Store $store, $custtype, Customer|null $seller, Supplier|null $supplier)
    {
        //  dd('Trying to create a new warehouse, unlock me!');
        switch (true) {
            case $custtype === 'HD':
                $request = [
                    'warehouseName' => "homedepot.com",
                    'originAddress' => [
                        'name' => '(*************',
                        'company' => 'homedepot.com',
                        'street1' => '200 Enterprise Road',
                        'street2' => '',
                        'street3' => '',
                        "city" => "Somerville",
                        "state" => "TN",
                        "postalCode" => "38068",
                        "country" => "US",
                        "phone" => "(*************",
                        "residential" => false,
                    ],
                    'returnAddress' => [
                        'name' => '(*************',
                        'company' => 'homedepot.com',
                        'street1' => '200 Enterprise Road',
                        'street2' => '',
                        'street3' => '',
                        "city" => "Somerville",
                        "state" => "TN",
                        "postalCode" => "38068",
                        "country" => "US",
                        "phone" => "(*************",
                        "residential" => false,
                    ],
                ];
                break;
            case $custtype === 'ZOR':
                $request = [
                    'warehouseName' => "Zoro",
                    'originAddress' => [
                        'name' => 'Shipping Manager',
                        'company' => 'Zoro',
                        'street1' => '200 Enterprise Road',
                        'street2' => '',
                        'street3' => '',
                        "city" => "Somerville",
                        "state" => "TN",
                        "postalCode" => "38068",
                        "country" => "US",
                        "phone" => "(*************",
                        "residential" => false,
                    ],
                    'returnAddress' => [
                        'name' => 'Returns Manager',
                        'company' => 'Zoro',
                        'street1' => '200 Enterprise Road',
                        'street2' => '',
                        'street3' => '',
                        "city" => "Somerville",
                        "state" => "TN",
                        "postalCode" => "38068",
                        "country" => "US",
                        "phone" => "(*************",
                        "residential" => false,
                    ],
                ];
                break;
            case $custtype === 'NAT':

                $request = [
                    'warehouseName' => $seller->xml['FEDEXSHIPFROMCOMPANY']??*********,
                    'originAddress' => [
                        'name' => 'NETWORK PRODUCTS',
                        'company' => $seller->xml['FEDEXSHIPFROMCOMPANY']??*********,
                        'street1' => '200 Enterprise Road',
                        'street2' => '',
                        'street3' => '',
                        "city" => "Somerville",
                        "state" => "TN",
                        "postalCode" => "38068",
                        "country" => "US",
                        "phone" => "************",
                        "residential" => false,
                    ],
                    'returnAddress' => [
                        'name' => 'Returns Manager',
                        'company' => 'Sophio FBS',
                        'street1' => '200 Enterprise Road',
                        'street2' => '',
                        'street3' => '',
                        "city" => "Somerville",
                        "state" => "TN",
                        "postalCode" => "38068",
                        "country" => "US",
                        "phone" => "************",
                        "residential" => false,
                    ],
                ];
                break;
            case $custtype === 'WAL':
                if($supplier==null) {
                    $request = [
                        'warehouseName' => "Sophio Walmart",
                        'originAddress' => [
                            'name' => 'Walmart.com',
                            'company' => 'Walmart.com',
                            'street1' => '2301 Corporation Parkway',
                            'street2' => '',
                            'street3' => '',
                            "city" => "Waco",
                            "state" => "TX",
                            "postalCode" => "76712",
                            "country" => "US",
                            "phone" => "************",
                            "residential" => false,
                        ],
                        'returnAddress' => [
                            'name' => 'Returns Center',
                            'company' => 'Walmart.com',
                            'street1' => '2301 Corporation Parkway',
                            'street2' => '',
                            'street3' => '',
                            "city" => "Waco",
                            "state" => "TX",
                            "postalCode" => "76712",
                            "country" => "US",
                            "phone" => "************",
                            "residential" => false,
                        ],
                    ];
                }else{
                    if(isset($supplier->SETTINGS['TRACKINGNUMBERTYPE']) && $supplier->SETTINGS['TRACKINGNUMBERTYPE']==="SOPHIOFBS") {
                        $warehouseName= 'Sophio Walmart '.$supplier->PK;
                    }else{
                        $warehouseName ="Sophio Walmart";
                    }
                    $request = [
                        'warehouseName' => $warehouseName,
                        'originAddress' => [
                            'name' => 'Walmart.com - WAL',
                            'company' => 'Walmart.com',
                            'street1' => $supplier->SETTINGS['RETURNSADDRESS1'],
                            'street2' => $supplier->SETTINGS['RETURNSADDRESS2']??'',
                            'street3' => '',
                            "city" => $supplier->SETTINGS['RETURNSCITY']??'',
                            "state" => $supplier->SETTINGS['RETURNSSTATEPROVINCE']??'',
                            "postalCode" => $supplier->SETTINGS['RETURNSPOSTAL']??'',
                            "country" => "US",
                            "phone" => "************",
                            "residential" => false,
                        ],
                        'returnAddress' => [
                            'name' => 'Returns Center',
                            'company' => 'Walmart.com',
                            'street1' => '2301 Corporation Parkway',
                            'street2' => '',
                            'street3' => '',
                            "city" => "Waco",
                            "state" => "TX",
                            "postalCode" => "76712",
                            "country" => "US",
                            "phone" => "************",
                            "residential" => false,
                        ],
                    ];
                }
                break;
            case in_array($custtype, ['EB', 'AMZ', 'PP', 'ARS','MEM','GU']):
                $request = [
                    'warehouseName' => "FBS " . $custtype,
                    'originAddress' => [
                        'name' => 'Auto Parts Fulfillment Center',
                        'company' => 'Auto Parts Fulfillment Center',
                        'street1' => '200 Enterprise Road',
                        'street2' => '',
                        'street3' => '',
                        "city" => "Somerville",
                        "state" => "TN",
                        "postalCode" => "38068",
                        "country" => "US",
                        "phone" => in_array($custtype, ['EB', 'AMZ']) ? '************' : "************",
                        "residential" => false,
                    ],
                    'returnAddress' => [
                        'name' => 'Auto Parts Fulfillment Center',
                        'company' => 'Auto Parts Fulfillment Center',
                        'street1' => '200 Enterprise Road',
                        'street2' => '',
                        'street3' => '',
                        "city" => "Somerville",
                        "state" => "TN",
                        "postalCode" => "38068",
                        "country" => "US",
                        "phone" => "************",
                        "residential" => false,
                    ],
                ];
                break;
            case in_array($custtype, ['B2B', 'B2C', 'AF','UPS']):
                if ($seller != null) {
                    $request = [
                        'warehouseName' => "FBS " . $custtype . '-' . $seller->pk,
                        'originAddress' => [
                            'name' => (isset($seller->xml['SHIPFROMNAME'])  && $seller->xml['SHIPFROMNAME']!=="") ? $seller->xml['SHIPFROMNAME'] : 'FULFILLMENT MANAGER',
                            'company' => (isset($seller->xml['SHIPFROMCOMPANY'])  && $seller->xml['SHIPFROMCOMPANY']!=="") ? $seller->xml['SHIPFROMCOMPANY'] : 'FBS',
                            'street1' => (isset($seller->xml['SHIPFROMADDRESS']) && $seller->xml['SHIPFROMADDRESS']!=="")  ? $seller->xml['SHIPFROMADDRESS'] : '200 Enterprise Road',
                            'street2' => (isset($seller->xml['SHIPFROMADDRESS2']) && $seller->xml['SHIPFROMADDRESS2']!=="")  ? $seller->xml['SHIPFROMADDRESS2'] : '',
                            'street3' => '',
                            "city" => (isset($seller->xml['SHIPFROMCITY'])  && $seller->xml['SHIPFROMCITY']!=="") ? $seller->xml['SHIPFROMCITY'] : 'Somerville',
                            "state" => (isset($seller->xml['SHIPFROMSTATE']) && $seller->xml['SHIPFROMSTATE']!=="")  ? $seller->xml['SHIPFROMSTATE'] : 'TN',
                            "postalCode" => (isset($seller->xml['SHIPFROMPOSTAL'])  && $seller->xml['SHIPFROMPOSTAL']!=="") ? $seller->xml['SHIPFROMPOSTAL'] : '38068',
                            "country" => (isset($seller->xml['SHIPFROMCOUNTRY']) && $seller->xml['SHIPFROMCOUNTRY']!=="")  ? $seller->xml['SHIPFROMCOUNTRY'] : 'US',
                            "phone" => (isset($seller->xml['SHIPFROMPHONE'])  && $seller->xml['SHIPFROMPHONE']!=="") ? $seller->xml['SHIPFROMPHONE'] : '************',
                            "residential" => false,
                        ],
                        'returnAddress' => [
                            'name' => (isset($seller->xml['SHIPFROMNAME'])  && $seller->xml['SHIPFROMNAME']!=="") ? $seller->xml['SHIPFROMNAME'] : 'FULFILLMENT MANAGER',
                            'company' => (isset($seller->xml['SHIPFROMCOMPANY'])  && $seller->xml['SHIPFROMCOMPANY']!=="") ? $seller->xml['SHIPFROMCOMPANY'] : 'FBS',
                            'street1' => (isset($seller->xml['SHIPFROMADDRESS']) && $seller->xml['SHIPFROMADDRESS']!=="")  ? $seller->xml['SHIPFROMADDRESS'] : '200 Enterprise Road',
                            'street2' => (isset($seller->xml['SHIPFROMADDRESS2']) && $seller->xml['SHIPFROMADDRESS2']!=="")  ? $seller->xml['SHIPFROMADDRESS2'] : '',
                            'street3' => '',
                            "city" => (isset($seller->xml['SHIPFROMCITY'])  && $seller->xml['SHIPFROMCITY']!=="") ? $seller->xml['SHIPFROMCITY'] : 'Somerville',
                            "state" => (isset($seller->xml['SHIPFROMSTATE']) && $seller->xml['SHIPFROMSTATE']!=="")  ? $seller->xml['SHIPFROMSTATE'] : 'TN',
                            "postalCode" => (isset($seller->xml['SHIPFROMPOSTAL'])  && $seller->xml['SHIPFROMPOSTAL']!=="") ? $seller->xml['SHIPFROMPOSTAL'] : '38068',
                            "country" => (isset($seller->xml['SHIPFROMCOUNTRY']) && $seller->xml['SHIPFROMCOUNTRY']!=="")  ? $seller->xml['SHIPFROMCOUNTRY'] : 'US',
                            "phone" => (isset($seller->xml['SHIPFROMPHONE'])  && $seller->xml['SHIPFROMPHONE']!=="") ? $seller->xml['SHIPFROMPHONE'] : '************',
                            "residential" => false,
                        ],
                    ];
                } else {
                    $request = [
                        'warehouseName' => "FBS " . $custtype,
                        'originAddress' => [
                            'name' => 'FULFILLMENT MANAGER',
                            'company' => 'FBS',
                            'street1' => '200 Enterprise Road',
                            'street2' => '',
                            'street3' => '',
                            "city" => "Somerville",
                            "state" => "TN",
                            "postalCode" => "38068",
                            "country" => "US",
                            "phone" => "************",
                            "residential" => false,
                        ],
                        'returnAddress' => [
                            'name' => 'FBS',
                            'company' => 'FBS',
                            'street1' => '200 Enterprise Road',
                            'street2' => '',
                            'street3' => '',
                            "city" => "Somerville",
                            "state" => "TN",
                            "postalCode" => "38068",
                            "country" => "US",
                            "phone" => "************",
                            "residential" => false,
                        ],
                    ];
                }
                break;
            case $custtype === 'TRA':
                $request = [
                    'warehouseName' => "tractorsupply.com",
                    'originAddress' => [
                        'name' => 'TSC Distribution Center',
                        'company' => 'ATTN: Online Order',
                        'street1' => '100 Raines Drive',
                        'street2' => '',
                        'street3' => '',
                        "city" => "Franklin",
                        "state" => "KY",
                        "postalCode" => "42134",
                        "country" => "US",
                        "phone" => "(*************",
                        "residential" => false,
                    ],
                    'returnAddress' => [
                        'name' => 'TSC Distribution Center',
                        'company' => 'ATTN: Online Order',
                        'street1' => '100 Raines Drive',
                        'street2' => '',
                        'street3' => '',
                        "city" => "Franklin",
                        "state" => "KY",
                        "postalCode" => "42134",
                        "country" => "US",
                        "phone" => "(*************",
                        "residential" => false,
                    ],
                ];
                break;
            default:
                $request = [
                    'warehouseName' => $store->STORENAME . ' ' . $custtype,
                    'originAddress' => [
                        'name' => $store->STORENAME . ' ' . $custtype,
                        'company' => (isset($store->ADVSET['POADDRESS']) && !empty($store->ADVSET['POADDRESS'])) ? $store->ADVSET['POADDRESS'] : $store->STORENAME,
                        'street1' => (isset($store->ADVSET['POADDRESS']) && !empty($store->ADVSET['POADDRESS'])) ? $store->ADVSET['POADDRESS'] : '',
                        'street2' => '',
                        'street3' => '',
                        "city" => (isset($store->ADVSET['POCITY']) && !empty($store->ADVSET['POCITY'])) ? $store->ADVSET['POCITY'] : '',
                        "state" => (isset($store->ADVSET['POSTATE']) && !empty($store->ADVSET['POSTATE'])) ? $store->ADVSET['POSTATE'] : $store->STORESTATE,
                        "postalCode" => (isset($store->ADVSET['POZIP']) && !empty($store->ADVSET['POZIP'])) ? $store->ADVSET['POZIP'] : $store->STOREZIP,
                        "country" => (isset($store->ADVSET['POCOUNTRY']) && !empty($store->ADVSET['POCOUNTRY'])) ? $store->ADVSET['POCOUNTRY'] : $store->STORECNTRY,
                        "phone" => "(*************",
                        "residential" => false,
                    ],
                    'returnAddress' => [
                        'name' => $store->STORENAME . ' ' . $custtype,
                        'company' => (isset($store->ADVSET['POADDRESS']) && !empty($store->ADVSET['POADDRESS'])) ? $store->ADVSET['POADDRESS'] : $store->STORENAME,
                        'street1' => (isset($store->ADVSET['POADDRESS']) && !empty($store->ADVSET['POADDRESS'])) ? $store->ADVSET['POADDRESS'] : '',
                        'street2' => '',
                        'street3' => '',
                        "city" => (isset($store->ADVSET['POCITY']) && !empty($store->ADVSET['POCITY'])) ? $store->ADVSET['POCITY'] : '',
                        "state" => (isset($store->ADVSET['POSTATE']) && !empty($store->ADVSET['POSTATE'])) ? $store->ADVSET['POSTATE'] : $store->STORESTATE,
                        "postalCode" => (isset($store->ADVSET['POZIP']) && !empty($store->ADVSET['POZIP'])) ? $store->ADVSET['POZIP'] : $store->STOREZIP,
                        "country" => (isset($store->ADVSET['POCOUNTRY']) && !empty($store->ADVSET['POCOUNTRY'])) ? $store->ADVSET['POCOUNTRY'] : $store->STORECNTRY,
                        "phone" => "(*************",
                        "residential" => false,
                    ],
                ];
        }

        $response = $this->shipstationManager->createWarehouse($request);
        return $response->warehouseId;
    }

    public function getWarehouse($custtype, $custpk = '',Supplier $supplier)
    {

        if (!in_array($custtype, ['B2C', 'B2B', 'AF','MEM','GU','RC','UPS'])) {
            $custpk = '';
        }
        $warehouses = $this->shipstationManager->listWarehouses();
        $warehouse = null;

        if ($custpk != '') {

            foreach ($warehouses as $w) {
                if (Str::contains($w->warehouseName, 'FBS ' . $custtype . ' - ' . $custpk, true)) {
                    return $w;
                }
                if (Str::contains($w->warehouseName, $custpk, true)) {
                    $warehouse = $w;
                }
            }
            if ($warehouse) {
                return $warehouse;
            }
        } else {
            foreach ($warehouses as $w) {
                switch ($custtype) {
                    case 'TRA':
                        if (Str::contains($w->warehouseName, 'TRACTORSUPPLY', true)) {
                            return $w;
                        }
                        break;
                    case 'HD':
                        if (Str::contains($w->warehouseName, 'HOMEDEPOT', true)) {
                            return $w;
                        }
                        break;
                    case 'NAT':
                        if (Str::contains($w->warehouseName, ['AUTOZONE', '*********'], true)) {
                            return $w;
                        }
                        break;
                    case 'WAL':
                        if (Str::contains($w->warehouseName, 'WALMART', true)) {
                            if(isset($supplier->SETTINGS['TRACKINGNUMBERTYPE']) && $supplier->SETTINGS['TRACKINGNUMBERTYPE']=="SOPHIOFBS") {
                                if (Str::contains($w->warehouseName, (string)$supplier->PK, true)) {
                                    return $w;
                                }
                            }else{
                                return $w;
                            }
                        }
                        break;
                    case 'ZOR':
                        if (Str::contains($w->warehouseName, 'ZORO', true)) {
                            return $w;
                        }
                        break;

                    case 'PP':
                        if (Str::contains($w->warehouseName, 'PP', true)) {
                            return $w;
                        }
                        break;
                    case 'EB':
                    case 'ARS':
                        if (Str::contains($w->warehouseName, 'EBAY', true)) {
                            return $w;
                        }
                        break;
                    case 'B2B':
                        if (Str::contains($w->warehouseName, 'B2B', true)) {
                            return $w;
                        }
                        break;
                    case 'AMZ':
                        if (Str::contains($w->warehouseName, 'AMAZON', true)) {
                            return $w;
                        }
                        break;
                    case 'AF':
                        if (Str::contains($w->warehouseName, 'AF', true)) {
                            return $w;
                        }
                        break;
                    case 'MEM':
                        if (Str::contains($w->warehouseName, 'MEM', true)) {
                            return $w;
                        }
                        break;
                }
            }
            // just a fallback
            if($custtype==='WAL') {
                foreach ($warehouses as $w) {
                    if (Str::contains($w->warehouseName, 'WALMART', true)) {
                        return $w;
                    }
                }
            }
        }

        return null;
    }

    public function saveNewWarehouse($warehouseId, Supplier $supplier, $custtype, $custpk = '')
    {

        $setting_key = 'SHIPSTATIONWAREHOUSEID' . $custtype . '-' . $custpk;
        if (!in_array($custtype, ['B2C', 'B2B', 'AF','UPS']) || $custpk == '') {
            $setting_key = 'SHIPSTATIONWAREHOUSEID' . $custtype;
        }

        if (!isset($supplier->SETTINGS[$setting_key])) {

            $supplier->SETTINGS[$setting_key] = $warehouseId;

            $supplier->save();
        }

    }

    public function unsetWarehouse(Supplier $supplier, $custtype, $custpk = '')
    {
        $setting_key = 'SHIPSTATIONWAREHOUSEID' . $custtype . '-' . $custpk;
        if (in_array($custtype, ['B2C', 'B2B', 'AF','UPS'])) {
            $setting_key = 'SHIPSTATIONWAREHOUSEID' . $custtype;
        }
        if (isset($supplier->SETTINGS[$setting_key])) {
            unset($supplier->SETTINGS[$setting_key]);
            $supplier->save();
        }

    }

}