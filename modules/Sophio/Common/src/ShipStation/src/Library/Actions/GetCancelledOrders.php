<?php

namespace Sophio\Common\ShipStation\src\Library\Actions;

use App\Events\AdminQueueMailEvent;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Sophio\Common\Enums\SystemLogStatus;
use Sophio\Common\Models\FBS\Invoice;
use Sophio\Common\Models\FBS\Supplier;
use Sophio\Common\Models\SystemLog;
use Sophio\Common\Repository\Settings;
use Sophio\Common\ShipStation\src\Library\ShipStationManager;
use Sophio\FBSOrder\Library\Actions\CancelBySupplierFromShipstation;

class GetCancelledOrders
{
    protected Settings $settings;

    public function __construct(Settings $settings)
    {
        $this->settings = $settings;
    }

    public function __invoke(Supplier|null $supplier = null, $options = null)
    {
        $sys = SystemLog::create(['task' => 'shipstation_find_cancelled', 'Start', 0, SystemLogStatus::NEW]);
        $found = 0;
        if ($supplier == null) {
            $suppliers = Supplier::where('active', 1)->whereNotIn('PK', [753])->get();
        } else {
            $suppliers[] = $supplier;
        }
        if (!isset($options['modifyDateStart'])) {
            $options['modifyDateStart'] = Carbon::yesterday()->startOfDay()->toDateString();
        }
        if (!isset($options['modifyDateEnd'])) {
            $options['modifyDateEnd'] = Carbon::now();
        }
        if (!isset($options['pageSize'])) {
            $options['pageSize'] = 100;
        }

        foreach ($suppliers as $sup) {
            if (isset($sup->SETTINGS['TRACKINGNUMBERTYPE']) && in_array(strtoupper($sup->SETTINGS['TRACKINGNUMBERTYPE']), ['SHIPSTATION', 'SOPHIOFBS'])) {
                $ssManager = new ShipStationManager($this->settings);
                $ssManager->setSupplierCreds($sup);
                $ssManager->createClient();
                Log::channel('shipstation')->error('Check cancels for ' . $sup->NAME);
                $response = $ssManager->listOrders(['orderStatus' => 'cancelled', 'modifyDateStart' => $options['modifyDateStart']]);

                if ($response) {
                    foreach ($response->orders as $order) {
                        Log::channel('shipstation')->info(json_encode($order));
                        $invoice = Invoice::find($order->orderNumber);
                        if ($invoice) {
                           $upd =  (new CancelBySupplierFromShipstation())($invoice, $sup,$order,'3');
                           if($upd==true) {
                               $sys->message = $sys->message . "\n" . "Cancelled for invoice " . $invoice->pk.' Supplier '.$sup->PK;
                               $found++;
                               event(new AdminQueueMailEvent('QueueJobGeneral', config('sophio.admin.mail_senders.default_fbs'), [
                                   'subject' =>'Invoice '.$invoice->pk.' cancelled by supplier from Shipstation',
                                   'view' => 'admin.mail.shipstationcancel',
                                   'supplier' => $sup, 'invoice' => $invoice

                               ]));
                           }else{
                               $sys->message = $sys->message . "\n" . "Did not cancelled for invoice " . $invoice->pk.' Supplier '.$sup->PK;
                           }
                        }else{
                            $sys->message = $sys->message . "\n" . "Ignoring SS ID " . $order->orderId.' Supplier '.$sup->PK;
                        }
                    }
                }
            }

        }
        $sys->message = $sys->message . "\n" . "Found  " . $found . " invoices cancelled.\n End.";
        $sys->status = SystemLogStatus::SUCCESS;
        $sys->save();
    }
}