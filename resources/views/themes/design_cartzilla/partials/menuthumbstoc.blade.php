<section class="container">
    <?php $colnum = 0; $letter='';$letters=[]; ?>
    @foreach($props as $prop)
            <?php if(!in_array( mb_strtolower(mb_substr($prop->name, 0, 1)),$letters)) $letters[]= mb_strtolower(mb_substr($prop->name, 0, 1));  ?>
    @endforeach

    <ul class="nav justify-content-center mb-4 nav-pills ">
        @foreach($letters as $letter)
            <li class="nav-item  active">
                <a class="nav-link " href="#cat_{{$letter}}" onclick="    document.querySelector(this.getAttribute('href')).scrollIntoView({
            behavior: 'smooth'
        });">{{strtoupper($letter)}}</a>
            </li>
        @endforeach
    </ul>

    <div class="row row-cols-1 row-cols-sm-2 row-cols-md-4 row-cols-lg-6 gy-2">



        @foreach($props as $prop)
            @if(  mb_strtolower(mb_substr($prop->name, 0, 1))!==$letter)
                    <?php  $letter= mb_strtolower(mb_substr($prop->name, 0, 1)); ?>
                <div class="w-100 mt-0 border-bottom" id="cat_{{$letter}}">{{strtoupper($letter)}}</div>
                @endif
            <div class="col">
                <div class="hover-effect-scale {{$taxonomy['class_prefix']}}-col text-center">
                    <a href="{{$prop->link}}"
                       class="d-block bg-body-tertiary rounded p-4 mb-4 {{$taxonomy['class_prefix']}}-link"
                       title="{{$prop->name}}">
                        <div class="{{$taxonomy['class_prefix']}}-wrapper-img">
                            <div class="ratio" style="--cz-aspect-ratio: calc(184 / 258 * 100%); ">
                                @if(isset($prop['logo']) && trim($prop['logo'])!=="")
                                    <img style="object-fit: contain"
                                         src="@if(\Illuminate\Support\Str::contains($prop['logo'],'http')) {{$prop['logo']}} @else https://images-us1.sophio.com{{$prop['logo']}} @endif"
                                         alt="{{$prop->name}}"
                                         class="hover-effect-target {{$taxonomy['class_prefix']}}-img">
                                @else
                                    <img src="{{getImagePath($prop->thumb)}}"
                                         style="object-fit: contain"
                                         alt="{{$prop->name}}"
                                         class="  hover-effect-target{{$taxonomy['class_prefix']}}-img"
                                         onerror="this.onerror=null; this.src='{{$taxonomy['noimage']}}'">
                                @endif
                            </div>
                        </div>  </a>
                    <h2 class="h6  w-100 pb-2 mb-1 mt-2  text-center">
                        {{$prop->name}}
                    </h2>


                </div>
            </div>

        @endforeach
    </div>
</section>
