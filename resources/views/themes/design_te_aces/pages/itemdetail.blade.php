@extends('theme::layouts.app')

@section('title',$part['mfg_name'].' '.$part['part_desc'])
@push('before-styles')
    <link rel="stylesheet" type="text/css" href="/assets/css/lightbox.css">
@endpush
@section('content')

    @include('theme::partials.breadcrumbs')
    @if(isset($part['amazon'])&& $part['amazon']!==null)
        @php $amazon = json_decode($part['amazon'],true) @endphp
    @endif
    <div class="row">
        <div class="col-md-12">
            <div id="parts-catalog" class="part-details vk" itemscope="" itemtype="http://schema.org/Product">


                <div id="box-product-image-buy">
                    <div class="row cc">
                        <div class="col-sm-5 col-sm-offset-1">


                            <div class="buy-panel-image">
                                @php

                                    $images = explode(';',$part['image_url'][0]);
                                    if(isset($amazon['images']) && isset($amazon['images'][0])&& isset($amazon['images'][0]['images']) && count($amazon['images'][0]['images'])>0) {
                                            $images[]= $amazon['images'][0]['images'][0]['link'];
                                    }
                                    $images=array_values(array_filter($images));

                                @endphp
                                <div id="p__{{$part['product_sku']}}_carousel" class="carousel slide">
                                    <!-- Indicators -->
                                    <ol class="carousel-indicators">
                                        @if(count($images)>0)
                                            @foreach($images as $i=> $image)
                                                @if($image<>"")
                                                <li data-target="#p__{{$part['product_sku']}}_carousel"
                                                    data-slide-to="{{$i}}" class="@if($i==0) active @endif"></li>
                                                @endif
                                            @endforeach
                                        @endif
                                    </ol>

                                    <!-- Content -->
                                    <div class="carousel-inner">
                                        @if(count($images)>0)

                                            @foreach($images as $i=> $image)
                                                @if($image<>"")
                                                    <div class="item @if($i==0) active @endif">
                                                        <a data-lightbox="lightbox" href="{{$image}}"
                                                           title="{{html_entity_decode($part['part_label'])}} | Part: {{$part['part_number']}}">
                                                            <img alt="{{html_entity_decode($part['part_label'])}}"
                                                                 src="@if(\Illuminate\Support\Str::contains($image,'http')){{$image}}@else https://images-us1.sophio.com{{$image}} @endif ">
                                                        </a>

                                                    </div>
                                                @else
                                                    <div class="item @if($i==0) active @endif">
                                                    <img src="{{config('app.coming_soon_image')}}"
                                                         data-lightbox="lightbox">
                                                    </div>
                                                @endif
                                            @endforeach
                                        @endif

                                    </div>
                                    <!-- Next/Prev buttons -->
                                    <a class="left carousel-control" href="#p__{{$part['product_sku']}}_carousel" role="button" data-slide="prev">
                                        <span class="glyphicon glyphicon-chevron-left" aria-hidden="true"></span>
                                        <span class="sr-only">Previous</span>
                                    </a>
                                    <a class="right carousel-control" href="#p__{{$part['product_sku']}}_carousel" role="button" data-slide="next">
                                        <span class="glyphicon glyphicon-chevron-right" aria-hidden="true"></span>
                                        <span class="sr-only">Next</span>
                                    </a>
                                </div>


                            </div>


                        </div>
                        <div class="col-sm-5 col-sm-offset-1">

                            <h2 itemprop="name" class="mb-0">@if(isset($amazon) && isset($amazon['attributes']) && isset($amazon['attributes']['item_name'])) A:{{$amazon['attributes']['item_name'][0]['value']}}
                                @else{{$part['mfg_name']}}  {{html_entity_decode($part['part_label'])}}@endif</h2>
                            <small>Part: {{$part['part_number']}}</small>
                            <meta itemprop="brand" content="Denso">
                            @if(count($part['image_url'])>0)
                                <meta itemprop="image" content="{{explode(';',$part['image_url'][0])[0]}}">
                            @endif
                            <ul class="part-attributes mt-5">
                                @if(isset($part['attributes']['FAB']))
                                    @foreach($part['attributes']['FAB'] as $value)
                                        <li>{{ strip_tags(htmlspecialchars_decode($value))}}</li>

                                    @endforeach
                                @elseif(isset($part['attributes']['BulletPoint']))
                                    @foreach($part['attributes']['BulletPoint'] as $value)
                                        <li>{{ strip_tags(htmlspecialchars_decode($value))}}</li>

                                    @endforeach
                                @elseif(isset($part['attributes']['Marketing Description']))
                                    @foreach($part['attributes']['Marketing Description'] as $value)
                                        <li>{{ strip_tags(htmlspecialchars_decode($value))}}</li>
                                    @endforeach
                                @elseif(isset($part['attributes']['Long Product Description']))
                                    @foreach($part['attributes']['Long Product Description'] as $value)
                                        <li>{{ strip_tags(htmlspecialchars_decode($value))}}</li>

                                    @endforeach
                                @endif

                            </ul>

                            <div class="buy-panel vk clearfix">
                                <form method="post" action="/cart/add"
                                      class="form-horizontal buy-panel-style1" itemscope=""
                                      itemtype="http://schema.org/Offer">
                                   @csrf
                                    <input type="hidden" name="part_id[]" value="{{$part['product_sku']}}">
                                    <input type="hidden" name="part_data[{{$part['product_sku']}}]" value="{{base64_encode(json_encode($part))}}">
                                    <ul class="list-unstyled" id="listprice_p__{{$part['product_sku']}}">

                                        @if(request()->website_parameters['hide_price']==0)
                                            @if($part['pricing']['landedprice']!==0)
                                                <li><h3 class="partsellprice text-danger"><small>Retail Price: ${{$part['pricing']['landedprice']}}</small></h3></li>

                                                @endif
                                            <li><h3 class="partsellprice text-danger"><small>Price:</small>
                                                    @if($part['pricing']['sellprice']==0)
                                                        N/A
                                                    @else
                                                        @if($part['pricing']['currency']==="USD")
                                                            $
                                                        @else
                                                            {{$part['pricing']['currency']}}
                                                        @endif

                                                        {{number_format($part['pricing']['sellprice'], 2)}}
                                                    @endif

                                                </h3></li>


                                            <li>
                                                @endif

                                            </li>

                                    </ul>
                                    <!-- buy button -->

                                    <div class="main-buy col-sm-9" style="padding-left: 0;">
                                        @if(request()->website_parameters['buy_button']=='b2c')
                                            <div class="first col-sm-3" style="padding:0;">
                                                <!--label for="txqty_p__{{$part['product_sku']}}" class="col-lg-2 control-label">Quantity:</label-->
                                                <input type="text" name="quantity[{{$part['product_sku']}}]"
                                                       class="form-control" value="1">
                                            </div>
                                            <div class="first1 col-sm-6">
                                                @else
                                                    <div class="first1 col-sm-9">
                                                        @endif
                                                        @include('theme::partials.buy_button_'.request()->website_parameters['buy_button'])
                                                    </div>

                                            </div>
                                    </div>

                                </form>


                            </div>


                        </div>
                    </div>
                </div>


                <div id="product-info-tabs" role="tabpanel">
                    <!-- Nav tabs -->
                    <ul class="nav nav-tabs" role="tablist">
                        <li role="presentation" class="active">
                            <a href="#desc" aria-controls="desc" data-toggle="tab" role="tab">
                                <i class="icon-list"></i> Description
                            </a>
                        </li>
                        @if(isset($part['fitment']) && count($part['fitment'])>0)
                            <li role="presentation">
                                <a href="#fitment" aria-controls="fitment" data-toggle="tab" role="tab">
                                    <i class="icon-cogs"></i> Fitments
                                </a>
                            </li>
                        @endif


                        <li role="presentation">
                            <a href="#warranty" aria-controls="warranty" data-toggle="tab" role="tab">
                                <i class="icon-shield"></i> Warranty
                            </a>
                        </li>
                        <!-- brand info-->

                    </ul>


                    <!-- Tab panes -->
                    <div class="tab-content">
                        <div role="tabpanel" class="tab-pane active" id="desc">
                            <div class="dl-horizontal">
                                <h2>Product Details</h2>
                                <dl>

                                    <dt>Manufacturer:</dt>
                                    <dd itemprop="manufacturer">
                                        {{$part['mfg_name']}}

                                    </dd>

                                    <dt>Part Number:</dt>
                                    <dd itemprop="sku">
                                        {{$part['part_number']}}
                                    </dd>


                                    <dt>Manufacturer Part Number:</dt>
                                    <dd itemprop="mpn">
                                        {{$part['part_number']}}
                                    </dd>


                                    <dt>Description:</dt>
                                    <dd itemprop="description">
                                        {{html_entity_decode ($part['part_label'])}}
                                    </dd>


                                    <dt>Category:</dt>
                                    <dd>
                                        {{$part['taxonomy']['category']['name']}}
                                    </dd>
                                    <dt>Subcategory:</dt>
                                    <dd>
                                        {{$part['taxonomy']['subcategory']['name']}}
                                    </dd>
                                    <dt>Part Type:</dt>
                                    <dd>
                                        @if(is_array($part['taxonomy']['parttype']))
                                            {{$part['taxonomy']['parttype']['name']}} ({{$part['partterminologyid']}})
                                        @endif
                                    </dd>

                                </dl>
                            </div>
                            <div class="dl-horizontal">
                                <h2>Technical Details</h2>
                                <dl>

                                    <dt>Condition:</dt>
                                    <dd itemprop="itemCondition">
                                        New
                                    </dd>
                                    <dt>Product Fit:</dt>
                                    <dd>
                                        Direct Fit
                                    </dd>
                                    <dt>Weight:</dt>
                                    <dd itemprop="weight">
                                        {{$part['packaging']['weight']}} <abbr
                                            title="pounds"> {{$part['packaging']['weight_uom']}}</abbr></dd>

                                    <dt>Height:</dt>
                                    <dd itemprop="height"> {{$part['packaging']['height']}}"</dd>

                                    <dt>Width:</dt>
                                    <dd itemprop="width"> {{$part['packaging']['width']}}"</dd>

                                    <dt>Length:</dt>
                                    <dd itemprop="depth"> {{$part['packaging']['length']}}"</dd>

                                    <dt><abbr title="Unit of Measure">UOM:</abbr></dt>
                                    <dd> {{$part['qty_uom']}}</dd>

                                    <dt>Quantity Sold:</dt>
                                    <dd>Sold individually</dd>

                                    <dt><abbr title="Suggested Replacement Quantity">Qty Per Application</abbr>:
                                    </dt>
                                    <dd> {{$part['qty_per_application']}}</dd>

                                    <dt><abbr title="Minimum order quantity.">Minimum Order Qty</abbr>:</dt>
                                    <dd> {{$part['min_order_qty']}}</dd>

                                    <dt><abbr title="Global Trade Identification Number">Item GTIN</abbr>:</dt>
                                    <dd itemprop="gtin13"> {{$part['gtin']}}</dd>

                                </dl>
                            </div>
                            <div class="dl-horizontal">
                                <h2>Specifications</h2>
                                <ul class="list-unstyled lh-base">

                                    @foreach($part['attributes'] as $key => $attributes)
                                        @foreach($attributes as $value)
                                            @if(  Illuminate\Support\Str::contains($key,'feature',true) )

                                                <li>{{html_entity_decode($value)}}</li>
                                            @endif
                                        @endforeach

                                    @endforeach
                                </ul>
                                <dl>


                                    @foreach($part['attributes'] as $key => $attributes)
                                        @foreach($attributes as $value)
                                            @if(! Illuminate\Support\Str::contains($key,'feature',true) )

                                                <dt>{{$key}}</dt>
                                                <dd>{{html_entity_decode($value)}}</dd>
                                            @endif
                                        @endforeach

                                    @endforeach

                                    @if(isset($amazon))

                                        @foreach($amazon['attributes'] as $attr_name=>$attribute)

                                            @if(!in_array($attr_name,['product_site_launch_date','externally_assigned_product_identifier','supplier_declared_dg_hz_regulation']))
                                                @if(count($attribute)==1 && isset($attribute[0]['value']) && $attribute[0]['value']===false)
                                                @else
                                                    @if(count($attribute)==1)

                                                        @if(isset($attribute[0]['value']))
                                                            <dt>A:{{Str::title(str_replace('_', ' ', $attr_name))}}:</dt>
                                                            <dd>
                                                                @if(isset($attribute[0]['unit']))
                                                                    {{$attribute[0]['value']??print_r($attribute,true)}} {{$attribute[0]['unit']}}
                                                                @else
                                                                    {{$attribute[0]['value']??print_r($attribute,true)}}
                                                                @endif
                                                            </dd>
                                                        @else
                                                            <dt>A:{{Str::title(str_replace('_', ' ', $attr_name))}}:</dt>
                                                            <dd>
                                                                @foreach($attribute as $val)
                                                                    @if(isset($val['value']))
                                                                        {{$val['value']}}<br>
                                                                    @else
                                                                        @foreach($val as $k=>$v)
                                                                            @if(is_array($v))
                                                                                @if(isset($v['unit']))
                                                                                    {{Str::title(str_replace('_', ' ', $k))}}
                                                                                    :   {{$v['value']??print_r($v,true)}} {{$v['unit']}}
                                                                                    <br>
                                                                                @else

                                                                                    {{Str::title(str_replace('_', ' ', $k))}}:
                                                                                @if(isset($v['value']))
                                                                                    {{$v['value']}}
                                                                                    @else

                                                                                        @foreach($v as $v2)
                                                                                            @if(isset($v2['value']))
                                                                                                {{$v2['value']}}
                                                                                            @else
                                                                                                @endif
                                                                                        @endforeach
                                                                                    @endif

                                                                                @endif

                                                                            @endif
                                                                        @endforeach
                                                                    @endif
                                                                @endforeach
                                                            </dd>
                                                        @endif
                                                    @else
                                                        <dt>A:{{Str::title(str_replace('_', ' ', $attr_name))}}:</dt>
                                                        <dd>
                                                            @foreach($attribute as $att)
                                                                @if(isset($att['value']))
                                                                    {{$att['value']}}
                                                                @else

                                                                    @foreach($att as $k=>$v)
                                                                        @if($k=='marketplace_id')
                                                                            @continue
                                                                            @endif
                                                                        @if(is_string($v))
                                                                        {{Str::title(str_replace('_', ' ', $v))}}
                                                                        @else
                                                                                -   {{Str::title(str_replace('_', ' ', implode(',',$v)))}}
                                                                            @endif
                                                                    @endforeach
                                                                        <br>
                                                                @endif

                                                            @endforeach
                                                        </dd>
                                                    @endif
                                                @endif
                                            @endif
                                        @endforeach

                                        @if(isset($amazon['images']) && isset($amazon['images'][0])&& isset($amazon['images'][0]['images']) && count($amazon['images'][0]['images'])>0)
                                            <dt>A:Image:</dt>
                                            <dd><img
                                                    src="{{$amazon['images'][0]['images'][0]['link']}}" width="400px">
                                            </dd>
                                        @endif
                                        @if((isset($amazon['asin'])))
                                                <dt>A:</dt>
                                                <dd><a target="_blank" href="https://www.amazon.com/dp/{{$amazon['asin']}}">{{$amazon['asin']}}</a> </dd>
                                        @endif
                                    @endif


                                </dl>
                            </div>
                            <p style="clear:both;">
                                {{html_entity_decode($part['part_desc'])}}
                            </p>
                        </div>
                        @if(isset($part['fitment']))

                            <div role="tabpanel" class="tab-pane" id="fitment">
                                <div class="pull-left text-success"><h4><i class="icon-ok-sign icon-large"></i>This
                                        part fits at least: {{count($part['fitment'])}} vehicles</h4></div>
                                <div class="pull-right"></div>
                                <div id="div-buyers-guide" class="clearfix"
                                     style="max-height: 400px;overflow-y: auto; width:100%">
                                    <table class="table table-striped" id="table-fitment1">
                                        <thead>
                                        <tr>
                                            <th>Year</th>
                                            <th>Make</th>
                                            <th>Model</th>
                                            <th>Engine</th>
                                            <th>Notes</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr style="display: table-row;">
                                        @if($part['fitment'])
                                            @foreach($part['fitment'] as $fitment)
                                                <tr style="display: table-row;">
                                                    <td nowrap="nowrap">{{$fitment['year']}}</td>
                                                    <td nowrap="nowrap">{{$fitment['make']}}</td>
                                                    <td nowrap="nowrap">{{$fitment['model']}}</td>
                                                    <td nowrap="nowrap">{{$fitment['engine']}}</td>
                                                    <td style="font-size:11px">{{$fitment['notes']}}</td>
                                                </tr>
                                            @endforeach
                                        @endif
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        @endif



                        <!-- brand info -->


                        <div role="tabpanel" class="tab-pane" id="warranty"><h1>Warranty Information</h1>


                            <p>New products purchased from our store are covered by a

                                manufacturer warranty. Other than applicable manufacturer warranties, or as

                                otherwise provided in these Terms &amp; Conditions, there are no warranties,

                                express or implied for any of our products, and we specifically disclaim any

                                warranty of merchantability or fitness for a particular purpose for tires,

                                aftermarket wheels, and tire &amp; aftermarket wheel packages.</p>


                            <p>For other parts, we provide a limited warranty of 90 days

                                from the date received by the original purchaser or installer, unless otherwise

                                specified, which covers the replacement of a defective part(s). The warranty

                                applies only to parts replacement with same or better quality. For such cases,

                                a full refund will be issued to the original purchaser. A refund or an exchange

                                of a defective part will be processed only if it is accompanied by the original

                                sales invoice. All items must be in their original condition and must not be

                                damaged due to incorrect installation upon return. The warranty is limited only

                                to the original purchaser and is non-transferable. </p>


                            <p>All items are given a limited 90-days warranty period by the

                                manufacturer. The manufacturer will allow the parts to be covered by their
                                warranty

                                provided that the part was not modified by the buyer. </p>


                            <p>Under NO circumstances will our liability exceed the amount

                                of the original sale.</p>


                            <p>Products damaged as a result of abuse, misuse, improper

                                repairs or maintenance, improper installation, alteration, or modification will

                                not be covered by the warranty.</p>


                            <p>The warranty does not cover airfreight charges, labor

                                expenses, towing expenses, or rental car expenses resulting from installation

                                errors. Return shipping is the responsibility of the buyer. We will not

                                reimburse any return shipping charges incurred.</p>


                            <p>All returned items must have the RA (Return Authorization)

                                number written outside the shipping box prior to return. The RA Numbers can be

                                obtained by creating a return from within your account. Returned items without

                                RA numbers will not be accepted. </p>


                            <p>Our liability does not cover lost or stolen packages mailed

                                back by the customer containing the parts being claimed under warranty. Because

                                the customer is the shipper, he/she is the only one who can file any lost claim

                                with the shipping company. Therefore, the claim must be coordinated and

                                processed through the shipping company by the customer.</p>

                        </div>


                    </div>
                </div>

                <hr>


                <div class="row">
                    <div class="col-sm-6">
                        <div class="text-center-left">

                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="text-center-right">

                        </div>
                    </div>
                </div>
                <hr>

                @include('catalog-2.components.bottom_search_links')

            </div>
        </div>
    </div>
    @include('catalog-2.components.productfitlocator')
    @include('catalog-2.components.questioner')
@endsection

@push('after-scripts')
    <script type="text/javascript" src="/assets/js/lightbox.js"></script>
    <script>
        @if(isset($amazon))
            amazon={!! $part['amazon'] !!};
            console.log(amazon);
            @endif
        $("img").bind("error", function () {
// Set the default image
            $(this).attr("src", "https://images-us1.sophio.com/vehicle/coming-soon.jpg");
        });

        @if (isset(request()->website_parameters['counterman_questions']) && request()->website_parameters['counterman_questions'] == "1" && isset($part['questions']))
            search_result.parts['{{$part['mfg_code']}}-{{$part['part_number_slug']}}'] = {'questions': {!! json_encode($part['questions']) !!}};
        @endif
        lightbox.option({
            'alwaysShowNavOnTouchDevices': 100,
            'albumLabel': "Image %1 of %2",
            'disableScrolling': false,
            'fadeDuration': 600,
            'fitImagesInViewport': true,
            'imageFadeDuration': 600,
            'maxWidth': 500,
            'maxHeight': 500,
            'positionFromTop': 50,
            'resizeDuration': 700,
            'showImageNumberLabel': true,
            'wrapAround': true
        });


        function checkGarage(cMake, cYear, cModel) {
            if (typeof window.SearchVM === "object") {
                var url = sophio.api_base_url + '?' + cMake + '&year=' + cYear + '&model=' + cModel;
                checkForFitment(url);
                return false;
            }
        }

        $(document).ready(function () {
            createPaginatedTable();
            window.questioner.submitCallback = function () {
                location.href = location.href + this.add_extras;
            }
            $('.list-grid-check-the-fit-link').on('click', function () {
                widget = new FitWidget({!! json_encode($part) !!}, this, function (cb) {

                    mygarage.resetVehicle(
                        veh = {
                            make: cb.make,
                            year: cb.year,
                            model: cb.model,
                            engine: cb.engine,

                            values: {}
                        }
                    );

                    var href = window.location.href;


                    var href = new URL(window.location.href);
                    href.searchParams.set('make', cb.make);
                    href.searchParams.set('model', cb.model);
                    href.searchParams.set('year', cb.year);
                    location.href = href;
                    location.href = "/" + sophio.routes['catalog.itemdetail.mym'].replace('{mfg}', '{{$part['mfg_name_slug']}}').replace('{sku}', '{{$part['part_number_slug']}}')
                        .replace('{year}', cb.year).replace('{make}', cb.make).replace('{model}', cb.model).replace('{engine?}', cb.engine);


                }, {'questions': {{request()->website_parameters['counterman_questions']!=""?request()->website_parameters['counterman_questions']:'0'}}});

                widget.initFitWidget();
            });


        });

        $("select.branchid").on("change", function () {
            $(".brancherror").css("visibility", "hidden");
            $('#p__{{$part['product_sku']}}_carousel').carousel();
        });

    </script>

@endpush
@push('after-styles')
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.3/css/select2.min.css" rel="stylesheet"/>

    <style>
        .first1 .btn.btn-primary {
            padding: 6px 0;
        }

        select.branchid {
            width: 100%;
            border: 1px solid #000;
            border-radius: 5px;
            padding: 3px 10px 0;
            background: none;
            font-size: 13px;
            height: 34px;
            margin-bottom: 3px;
            margin-top: 0px;
        }

        .brancherror {
            visibility: hidden;
            font-size: 16px;
            padding: 5px;
            margin-bottom: 0;
            text-align: center;
        }

        .main-buy {
            margin-top: 3px;
        }

        .buy-panel {
            border-bottom: 0;
        }

        .partinstock {
            padding-left: 5px;
        }

        .btn.btn-primary.btn-block.buy-button {
            background-color: #FF8900;
        }

        .btn.btn-primary.btn-block.buy-button:hover {
            background-color: #f18911;
            border-color: transparent;
        }

        .btn.btn-danger:active, .btn.btn-danger:focus {
            background-color: #444;
            border-color: transparent;
        }
    </style>
    <style>
        .radio .delivery {
            display: block;
            margin-left: -24px !important;
        }

        .buy-panel-image .carousel-inner > .item > a > img {
            max-height: 350px;
        }

        .part-attributes li {
            line-height: 1.5;
        }

        .fit-check {
            margin-top: 10px;
        }

        .noimage {
            display: none;
        }

        #desc dl h2 {
            margin-top: 0;
        }

        #desc .dl-horizontal {
            min-width: 250px;
            float: left;
            margin-left: 40px;
            width: 30%;
            padding-bottom: 0.5em;
        }

        #desc ul li {
            padding-bottom: 0.5em;

        }

        #desc dl:first-child {
            margin-left: 0;
        }

        #desc dl dt {
            white-space: normal;
        }

        @media screen and (max-width: 1000px) {
            #desc dl {
                display: block;
                float: none;
                margin-left: 0;
                width: 100%;
                min-width: unset;
            }
        }
    </style>
    <style>
        .skuFitsYes {
            display: none;
        }

        .skuFitsNot {
            display: none;
        }

        .skuFits {
            display: none;
        }

        .PartCheckFitOK .skuFitsYes {
            display: block;
        }

        .PartCheckFitOK .skuFitsNot {
            display: none;
        }

        .PartCheckFitOK .skuFits {
            display: none;
        }

        .PartCheckFitNOK .skuFitsYes {
            display: none;
        }

        .PartCheckFitNOK .skuFitsNot {
            display: block;
        }

        .PartCheckFitNOK .skuFits {
            display: none;
        }

        .PartCheckFit .skuFitsYes {
            display: none;
        }

        .PartCheckFit .skuFitsNot {
            display: none;
        }

        .PartCheckFit .skuFits {
            display: block;
        }
    </style>
@endpush
