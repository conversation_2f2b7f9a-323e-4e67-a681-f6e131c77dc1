@if(count($question)>0)

        <div class="panel panel-default">
            <div class="panel-heading">
                <h6 class="panel-title">
                    <a data-toggle="collapse" href="#facet-engine-list">
                      Question: {{ucwords($question['Text'])}} <i class="icon icon-caret-right pull-right"></i>
                    </a>
                </h6>
            </div>

            <div id="facet-engine-list" class="panel-collapse collapse in">
                <div class="panel-body ">

                        @foreach($question['Answer'] as $value)

                            <label class="checkbox">
                                <a class="name facetsearchlink" data-type="engine" data-id="{{$value['Id']}}"
                                   title="{{$value['Value']}}"
                                   href="{{request()->fullURlWithQuery([rtrim($question_id)=>$value['Id'],'_url'=>null])}}"
                                   rel="nofollow">
                                    <span class="icons"><span class="first-icon icon icon-check-empty"></span><span
                                                class="second-icon icon icon-check"></span></span>
                                    <input type="checkbox" value="" data-toggle="checkbox"

                                           @if (isset($parameters[rtrim($question_id)]) &&  $value['id']==$parameters[rtrim($question_id)]) checked @endif >
                                    {{$value['Value']}}
                                </a>
                            </label>

                        @endforeach




                </div>
            </div>

        </div>

@endif
