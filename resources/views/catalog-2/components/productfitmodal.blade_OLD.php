<div class="checkFitResponseLog" style="display:none"></div>
<!-- Modal -->
<div class="modal fade" id="widget-product-fit-locator" tabindex="-1" role="dialog"
     aria-labelledby="widget-product-fit-locatorModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">×</span></button>
                <h4 class="modal-title part-title mb-3">Select a Vehicle</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-7">
                        <div class="alert alert-info notification-fit-locator "style="display:none" id="fit-locator-choose-vehicle">
                            <h6 class="alert-heading"><i class="icon icon-exclamation"></i> Click on a vehicle from your garage or
                                select a new vehicle to check the fitment for the part below.</h6></div>
                        <div id="fit-locator-cars"></div>

                        <div class="alert alert-info notification-fit-locator" style="display:none"id="fit-locator-no-vehicle">
                        	<h6 class="alert-heading"><i class="icon icon-wrench"></i> Garage</h6>
                                	<p>You have no vehicles in your online garage. As you search, vehicles will automatically be added to your garage. Get started by selecting a vehicle.</p>
			</div>

                    </div>
                    <div class="col-sm-5">
                        <legend>Choose Vehicle</legend>
                        <div id="quick-parts-selector">
                            <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.3/css/select2.min.css"
                                  rel="stylesheet">


                            <form method="get" id="frm-vehicle-locator-afmkt" class="vehicle-locator-afmkt-vertical"
                                  action="/catalog-2/vehicle">
                                <fieldset>
                                    <div class="form-group">
                                        <select class="form-control" data-bind="options: makes, optionsCaption: makeSelectCaption(),
					 optionsValue: function(item) { return item.id; },
					 optionsText: function(item) { return item.name; }, value: selectedMake,
					 valueUpdate: 'change',  enable: makes().length" id="afmkt-make" name="make">
                                            <option value="">Choose A Make...</option>
                                            <option value="acura">Acura</option>
                                            <option value="alfa-romeo">Alfa Romeo</option>
                                            <option value="aston-martin">Aston Martin</option>
                                            <option value="audi">Audi</option>
                                            <option value="bentley">Bentley</option>
                                            <option value="bmw">BMW</option>
                                            <option value="buick">Buick</option>
                                            <option value="cadillac">Cadillac</option>
                                            <option value="chevrolet">Chevrolet</option>
                                            <option value="chrysler">Chrysler</option>
                                            <option value="daewoo">Daewoo</option>
                                            <option value="dodge">Dodge</option>
                                            <option value="eagle">Eagle</option>
                                            <option value="ferrari">Ferrari</option>
                                            <option value="fiat">Fiat</option>
                                            <option value="ford">Ford</option>
                                            <option value="geo">Geo</option>
                                            <option value="gmc">GMC</option>
                                            <option value="honda">Honda</option>
                                            <option value="hummer">Hummer</option>
                                            <option value="hyundai">Hyundai</option>
                                            <option value="infiniti">Infiniti</option>
                                            <option value="isuzu">Isuzu</option>
                                            <option value="jaguar">Jaguar</option>
                                            <option value="jeep">Jeep</option>
                                            <option value="kia">Kia</option>
                                            <option value="lamborghini">Lamborghini</option>
                                            <option value="land-rover">Land Rover</option>
                                            <option value="lexus">Lexus</option>
                                            <option value="lincoln">Lincoln</option>
                                            <option value="lotus">Lotus</option>
                                            <option value="maserati">Maserati</option>
                                            <option value="mazda">Mazda</option>
                                            <option value="mercedes-benz">Mercedes-Benz</option>
                                            <option value="mercury">Mercury</option>
                                            <option value="mg">MG</option>
                                            <option value="mini">Mini</option>
                                            <option value="mitsubishi">Mitsubishi</option>
                                            <option value="nissan">Nissan</option>
                                            <option value="oldsmobile">Oldsmobile</option>
                                            <option value="peugeot">Peugeot</option>
                                            <option value="plymouth">Plymouth</option>
                                            <option value="pontiac">Pontiac</option>
                                            <option value="porsche">Porsche</option>
                                            <option value="ram">Ram</option>
                                            <option value="renault">Renault</option>
                                            <option value="rolls-royce">Rolls Royce</option>
                                            <option value="rover">Rover</option>
                                            <option value="saturn">Saturn</option>
                                            <option value="scion">Scion</option>
                                            <option value="shelby">Shelby</option>
                                            <option value="smart">Smart</option>
                                            <option value="subaru">Subaru</option>
                                            <option value="suzuki">Suzuki</option>
                                            <option value="tesla">Tesla</option>
                                            <option value="toyota">Toyota</option>
                                            <option value="volkswagen">Volkswagen</option>
                                            <option value="volvo">Volvo</option>
                                            <option value="workhorse">Workhorse</option>
                                            <option value="yugo">Yugo</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <select class="form-control" data-bind="options: years, optionsCaption: yearSelectCaption(),
					 optionsValue: function(item) { return item.id; },
					 optionsText: function(item) { return item.name; }, value: selectedYear,
				valueUpdate: 'change', enable: (selectedMake &amp;&amp; years().length)" id="afmkt-year" name="year"
                                                disabled="">
                                            <option value="">Choose A Year...</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <select class="form-control" data-bind="options: models, optionsCaption: modelSelectCaption(),
					 optionsValue: function(item) { return item.id; },
					 optionsText: function(item) { return item.name; }, value: selectedModel,
				valueUpdate: 'change', enable:(selectedYear &amp;&amp; models().length)" id="afmkt-model" name="model"
                                                disabled="">
                                            <option value="">Choose A Model...</option>
                                        </select>
                                    </div>
                                    <!-- moved by mikeb on 10/20/17 -->
                                    <div class="form-group" data-bind="visible:hasSubmodels">
                                        <select class="form-control" data-bind="options: submodels, optionsCaption: submodelSelectCaption(),
					 optionsValue: function(item) { return item.id; },
					 optionsText: function(item) { return item.name; }, value: selectedSubModel,
				valueUpdate: 'change', enable:(selectedEngine &amp;&amp; submodels().length)" id="afmkt-submodel"
                                                name="submodel" disabled="">
                                            <option value="">Choose A Submodel...</option>
                                        </select>
                                    </div>

                                    <div class="form-group" data-bind="visible:hasEngines">
                                        <select class="form-control" data-bind="options: engines, optionsCaption: engineSelectCaption(),
					 optionsValue: function(item) { return item.id; },
					 optionsText: function(item) { return item.name; }, value: selectedEngine,
				valueUpdate: 'change', enable:(selectedModel &amp;&amp; engines().length)" id="afmkt-engine"
                                                name="engine" disabled="">
                                            <option value="">Choose An Engine...</option>
                                        </select>
                                    </div>
                                    <div class="form-group" data-bind="visible:hasPartTypes">
                                        <select class="form-control" data-bind="options: parttypes, optionsCaption: parttypeSelectCaption(),
					 optionsValue: function(item) { return item.id; },
					 optionsText: function(item) { return item.name; }, value: selectedPartType,
				valueUpdate: 'change', enable: (selectedSubModel &amp;&amp; parttypes().length)" id="afmkt-parttype"
                                                name="parttype" disabled="">
                                            <option value="">Choose A Part Type...</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <button type="button" id="afmkt-submit" class="btn btn-primary"
                                                disabled="disabled"
                                                data-bind="enable: selectedModel, click:showParts,text:submitButtonTitle">
                                            Show Parts
                                        </button>
                                    </div>
                                </fieldset>
                            </form>


                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-dismiss="modal">Close &amp; Return to Part listing
                </button>
            </div>
        </div>
    </div>
</div>

@push('after-styles')
<link rel="stylesheet" type="text/css" href="/assets/css/toastr.min.css" />
    <style>
        #widget-product-fit-locator #quick-parts-selector {
            background-color: white;
        }
         .skuFitsYes {
            display: none;
        }

        .skuFitsNot {
            display: none;
        }

        .skuFits {
            display: none;
        }

        .PartCheckFitOK .skuFitsYes {
            display: block;
        }

        .PartCheckFitOK .skuFitsNot {
            display: none;
        }

        .PartCheckFitOK .skuFits {
            display: none;
        }

        .PartCheckFitNOK .skuFitsYes {
            display: none;
        }

        .PartCheckFitNOK .skuFitsNot {
            display: block;
        }

        .PartCheckFitNOK .skuFits {
            display: none;
        }

        .PartCheckFit .skuFitsYes {
            display: none;
        }

        .PartCheckFit .skuFitsNot {
            display: none;
        }

        .PartCheckFit .skuFits {
            display: block;
        }
    </style>
@endpush
@push('after-scripts')
<script>
let widget = new FitWidget();
</script>
@endpush