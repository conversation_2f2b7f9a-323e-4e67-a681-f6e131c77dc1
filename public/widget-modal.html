<html>
<head>
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.4/css/bootstrap.min.css">
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.4/js/bootstrap.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.3/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.3/js/select2.min.js"></script>
    <style>
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
        }
        .full-height-container {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        #quick-parts-selector {
            padding: 20px;
        }
        .modal-dialog {
            width: 90%;
            max-width: 1200px;
        }
        .modal-content {
            height: 80vh;
        }
        .modal-body {
            height: calc(80vh - 120px);
            padding: 0;
        }
        .modal-body iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .parts-widget-container {
            max-width: 500px;
            width: 100%;
        }
    </style>
</head>
<body>
<div class="container-fluid"><div class="row"><div class="col-sm-4">
    <div class="row">
        <div class="col-sm-12">
            <div class="parts-widget-container">
                <div id="quick-parts-selector" class="bg-primary">
                    <h2>Buy Auto Parts</h2>
                    <form id="frm-vehicle-locator-af" class="vehicle-locator-af-vertical" method="get" action="https://www.sophio.com/catalog-2/vehicle">
                        <fieldset>
                            <div class="form-group">
                                <select class="form-control" data-bind="options: years, optionsCaption: yearSelectCaption(),
                                     optionsValue: function(item) { return item.id; },
                                     optionsText: function(item) { return item.name; }, value: selectedYear,
                                valueUpdate: 'change', enable: ( years().length)" id="af-year" name="year"></select>
                            </div>
                            <div class="form-group">
                                <select class="form-control" data-bind="options: makes, optionsCaption: makeSelectCaption(),
                                     optionsValue: function(item) { return item.id; },
                                     optionsText: function(item) { return item.name; }, value: selectedMake,
                                     valueUpdate: 'change',  enable: (selectedYear && makes().length)" id="af-make" name="make">
                                </select>
                            </div>
                            <div class="form-group">
                                <select class="form-control" data-bind="options: models, optionsCaption: modelSelectCaption(),
                                     optionsValue: function(item) { return item.id; },
                                     optionsText: function(item) { return item.name; }, value: selectedModel,
                                valueUpdate: 'change', enable:(selectedMake && models().length)" id="af-model" name="model"></select>
                            </div>
                            <div class="form-group" data-bind="visible:hasSubmodels">
                                <select class="form-control" data-bind="options: submodels, optionsCaption: submodelSelectCaption(),
                                     optionsValue: function(item) { return item.id; },
                                     optionsText: function(item) { return item.name; }, value: selectedSubModel,
                                valueUpdate: 'change', enable:(selectedEngine && submodels().length)" id="af-submodel" name="submodel"></select>
                            </div>
                            <div class="form-group" data-bind="visible:hasEngines">
                                <select class="form-control" data-bind="options: engines, optionsCaption: engineSelectCaption(),
                                     optionsValue: function(item) { return item.id; },
                                     optionsText: function(item) { return item.name; }, value: selectedEngine,
                                valueUpdate: 'change', enable:(selectedModel && engines().length)" id="af-engine" name="engine"></select>
                            </div>
                            <div class="form-group" data-bind="visible:hasPartTypes">
                                <select class="form-control" data-bind="options: parttypes, optionsCaption: parttypeSelectCaption(),
                                     optionsValue: function(item) { return item.id; },
                                     optionsText: function(item) { return item.name; }, value: selectedPartType,
                                valueUpdate: 'change', enable: (selectedSubModel && parttypes().length)" id="af-parttype" name="parttype"></select>
                            </div>

                            <div class="form-group">
                                <button type="button" id="af-submit" class="btn btn-default btn-block" disabled="disabled" data-bind="enable: selectedModel, click:showParts,text:submitButtonTitle">Show Parts</button>
                            </div>
                        </fieldset>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap Modal for Parts Results -->
<div class="modal fade" id="partsModal" tabindex="-1" role="dialog" aria-labelledby="partsModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="partsModalLabel">Auto Parts Results</h4>
            </div>
            <div class="modal-body">
                <iframe id="partsIframe" src="" frameborder="0"></iframe>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
    var onlymakes_string = 'afla-romeo@@aston-martin@@bentley@@daewoo@@acura@@audi@@bmw@@buick@@cadillac@@chevrolet@@chrysler@@dodge@@eagle@@ferrari@@fiat@@ford@@geo@@gmc@@honda@@hummer@@hyundai@@infiniti@@isuzu@@jaguar@@jeep@@kia@@land-rover@@lexus@@lincoln@@lamborghini@@lotus@@maserati@@mercedes-benz@@mazda@@mercury@@mg@@mini@@mitsubishi@@nissan@@oldsmobile@@peugeot@@plymouth@@pontiac@@porsche@@ram@@renault@@rolls-royce@@rover@@saturn@@scion@@shelby@@smart@@subaru@@suzuki@@tesla@@toyota@@volkswagen@@volvo@@yugo@@workhorse';

    $(document).ready(function () {
        $("#af-parttype").select2({
        });

        const url_params = new URLSearchParams(window.location.search);
        window.SearchVM = new SearchViewModel();
        window.SearchVM.baseAjaxURL =  "https://faceted-catalog-api-us4.sophio.com/api/v1/c36e9644-3a6d-4b03-907f-2515145e1b52/catalog-2/vehicle?clientId=10";
        for (const [key, value] of url_params){
            window.SearchVM.baseAjaxURL=window.SearchVM.baseAjaxURL+"&"+key+"="+value;
        }
        window.SearchVM.baseSubmitURL = "https://tsc1.sophio.com/vehicle";
        window.SearchVM.loadYears({'smbeng':'submodels','usestatic':(url_params.get('usestatic')!=null?(url_params.get('usestatic').toLowerCase() === "true"):true),'onlymake':onlymakes_string});
        window.SearchVM.showParts = function() {
            var partsUrl = window.SearchVM.baseSubmitURL + "/" + window.SearchVM.selectedMake() + "/" + window.SearchVM.selectedYear() + "/" + window.SearchVM.selectedModel() + (typeof window.SearchVM.selectedPartType() !== "undefined" ? "/" + window.SearchVM.selectedPartType() : "") +
                "?" + jQuery.param({
                    "engine": window.SearchVM.selectedEngine(),
                    "submodel": window.SearchVM.selectedSubModel(),
                    "showas":"grid"
                }) + window.SearchVM.getMoreFields();

            // Set the iframe source and show the modal
            $('#partsIframe').attr('src', partsUrl);
            $('#partsModal').modal('show');
        }

        ko.applyBindings(window.SearchVM, document.getElementById("quick-parts-selector"));

        $( '#frm-vehicle-locator-af' ).bind('keypress', function(e){
            if ( e.keyCode == 13 ) {
                if ( $( '#af-submit' ).is(':enabled') ) {
                    $( '#af-submit' ).trigger('click');
                }
                return false;
            }
        });

        // Clear iframe when modal is closed to improve performance
        $('#partsModal').on('hidden.bs.modal', function () {
            $('#partsIframe').attr('src', '');
        });
    });
</script>

<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/knockout/3.3.0/knockout-min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/knockout.mapping/2.4.1/knockout.mapping.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/knockout-validation/2.0.2/knockout.validation.min.js"></script>
<script type="text/javascript" src="https://michael.sophio.com/sophio-shared-assets/data/aces-catalog-2-make-year.js"></script>
<script type="text/javascript" src="https://michael.sophio.com/sophio-shared-assets/data/aces-catalog-2-models.js"></script>
<script type="text/javascript" src="https://stagingte.sophio.com/assets/js/ko/ko-postbox.js"></script>
<script type="text/javascript" src="https://stagingte.sophio.com/assets/js/ko/ko-ajax-aces-vehicle-lookup.js"></script>

</body>
</html>
