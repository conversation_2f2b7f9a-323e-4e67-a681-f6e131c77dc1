/* Mega Menu 1 Styles */
.navbar-toggle{display:none;}

.megamenu a {color:#fff;}
.menu > ul > li:hover a {color:#444;}
.menu-mobile {
  display: none;
  padding: 20px;
}
.menu-mobile:after {
  content: "\f0c9";
  font-family:fontawesome;
  font-size: 1.5rem;
  padding: 0;
  float: right;
  position: relative;
  top: 50%;
  -webkit-transform: translateY(-25%);
          transform: translateY(-25%);
}


.menu-dropdown-icon:before {
  content: "\f067";
  font-family: "fontawesome";
  display: none;
  cursor: pointer;
  float: right;
  padding: .5em .6em;
  color: #fff;
}
.menu-dropdown-icon:hover:before {
  color: #444;
}
.menu > ul {
  margin: 0 auto;
  width: 100%;
  list-style: none;
  padding: 0;
  position: relative;
  /* IF .menu position=relative -> ul = container width, ELSE ul = 100% width */
  box-sizing: border-box;
}
.menu > ul:before,
.menu > ul:after {
  content: "";
  display: table;
}
.menu > ul:after {
  clear: both;
}
.menu > ul > li {
  float: left;
  padding: 0;
  margin: 0;
  position:initial;
  border-top:solid 3px transparent;
  border-bottom:solid 3px transparent;
}
.menu > ul > li a {
  text-decoration: none;
  padding: 10px;
  display: block;
}
.menu > ul > li:hover {
  background: #fff;
  border-top: solid 3px #fee26b;
}
.menu > ul > li > ul {
  display: none;
  width: 100%;
  padding: 20px;
  position: absolute;
  z-index: 99;
  left: 0;
  margin: 0;
  list-style: none;
  box-sizing: border-box;
  background:#fff;
  box-shadow: 1px 1px 5px #888888;
}
.menu > ul > li > ul:before,
.menu > ul > li > ul:after {
  content: "";
  display: table;
}
.menu > ul > li > ul:after {
  clear: both;
}
.menu > ul > li > ul > li {
  margin: 0;
  padding-bottom: 0;
  list-style: none;
  width: 33%;
  background: none;
  float: left;
}
.menu > ul > li > ul > li a {
  color: #777;
  padding: 0.2em 0;
  width: 95%;
  display: block;
  border-bottom: 1px solid #ccc;
}
.menu > ul > li > ul > li > ul {
  display: block;
  padding: 0;
  margin: 10px 0 0;
  list-style: none;
  box-sizing: border-box;
}
.menu > ul > li > ul > li > ul:before,
.menu > ul > li > ul > li > ul:after {
  content: "";
  display: table;
}
.menu > ul > li > ul > li > ul:after {
  clear: both;
}
.menu > ul > li > ul > li > ul > li {
  float: left;
  width: 100%;
  padding: 0;
  margin: 0;
}
.menu > ul > li > ul > li > ul > li a {
  border: 0;
  padding:0 5px;
  border-bottom:dashed 1px #eee;
}
.menu > ul > li > ul > li > ul > li a:hover {
  background:rgba(36, 89, 160, 1);
  color:#FFF;
}
.menu > ul > li > ul.normal-sub {
  width: 300px;
  left: auto;
  padding: 10px 20px;
}
.menu > ul > li > ul.normal-sub > li {
  width: 100%;
}
.menu > ul > li > ul.normal-sub > li a {
  border: 0;
  padding: 1em 0;
}
/* ––––––––––––––––––––––––––––––––––––––––––––––––––
Mobile style's
–––––––––––––––––––––––––––––––––––––––––––––––––– */
@media only screen and (max-width: 959px) {
.menu-dropdown-icon{border-top:solid 1px #efd89e !important;}
.menu-dropdown-icon a i{display:none;}
  .menu-container {
    width: 100%;
  }
  .menu-mobile {
    display: block;
  }
  .menu-dropdown-icon:before {
    display: block;
  }
  .menu > ul {
    display: none;
  }
  .menu > ul > li {
    width: 100%;
    float: none;
    display: block;
  }
  .menu > ul > li a {
    padding: .5em;
    width: 100%;
    display: block;
  }
  .menu > ul > li > ul {
    position: relative;
  }
  .menu > ul > li > ul.normal-sub {
    width: 100%;
  }
  .menu > ul > li > ul > li {
    float: none;
    width: 100%;
    margin-top: 20px;
  }
  .menu > ul > li > ul > li:first-child {
    margin: 0;
  }
  .menu > ul > li > ul > li > ul {
    position: relative;
  }
  .menu > ul > li > ul > li > ul > li {
    float: none;
  }
  .menu > ul > li > ul > li > ul > li img {
    width:100%;
  }
  .menu .show-on-mobile {
    display: block;
  }
}
/* Mega Menu Styles END */