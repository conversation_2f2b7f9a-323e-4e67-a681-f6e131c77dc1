*{
	font-family: <PERSON><PERSON>, "Times New Roman", Times, serif;
}
body, .form-control{
	font-size:16px;
}
a, a:hover, a:visited, a:active{
	color: #f58220;
}
.filter-breadcrumbs a{
	color:#fff;
}
.alert a, .alert a:hover, .alert a:visited, .alert a:active{
	color: #337ab7;
}
.container{
	width:100%;
	max-width:1600px;
}
.dl-horizontal dt { text-align: left; }
.card{
	border:none;
}
.header-title img{
	max-width: 434px;
}
.pagination>.active>a, .pagination>.active>a:focus, .pagination>.active>a:hover, .pagination>.active>span, .pagination>.active>span:focus, .pagination>.active>span:hover{
	background-color: #f58220;
    border-color: #f58220;
}
.list-grid-item {
    height: auto !important;
}
.grid-buy div {
    margin-bottom: 1rem;
}
.btn-primary, .btn-primary:active, .btn-primary:focus, .btn-primary:visited {
    color: #fff;
    background-color: #25408f;
    border-color: #25408f;
}
.btn-primary:hover,.btn-primary:active:focus {
    color: #fff;
    background-color: #1f3169;
    border-color: #1f3169; 
}
.btn-primary.webshop-button, .btn-primary.webshop-button:active, .btn-primary.webshop-button:focus, .btn-primary.webshop-button:visited {
    color: #fff;
    background-color: #10a0c6;
    border-color: #10a0c6;
}
.btn-primary.webshop-button:hover,.btn-primary.webshop-button:active:focus {
    color: #fff;
    background-color: #0e90b3;
    border-color: #0e90b3; 
}
.bg-primary {
    color: #fff;
    background-color: #25408f;
}
	.btn-green{
		background-color: #3cae49;
		border-color: #3cae49;
		color:#fff;
		padding: 5px;
		font-size: 18px;
	}
	.btn-green:focus, .btn-green:active, .btn-green:hover{
		background-color: #36993d;
		border-color: #36993d;
		color:#fff;
	}
	
	.bg-orange{
		background:#f58220;
		color:#fff;
	}
	.bg-theme{
		background:#25408f;
		color:#fff;
	}
	.bg-e{
		background:#eeeeee;
	}
	.bg-4{
		background:#444444;
		color:#fff;
	}
	.bg-4 a{
		color:#fff;
	}
	.bg-4 a:hover{
		color:#fff;
		text-decoration:underline;
	}
	.home-cat{
		height:330px;
		display: flex;
		align-items: flex-end;
		margin: 20px 0;
		justify-content: center;
	}
	.item-mfg-image{
		max-height: 30px;
		max-width: 100%;
	}
	
#SubCategoryTitle{
	border-radius:5px;
	font-family: fantasy;
}
	
.lh-2{
	line-height:2;
}
.d-none{
	display:none;
}
.d-block{
	display:block;
}
.flex-center{
	display: flex;
	align-items: center;
}
.flex-end{
	display: flex;
	align-items: flex-end;
}

	#top-store-links .nav li:not(:last-child) a{
		border-right:solid 1px #fff;
	}
	.bg-garage .display-1{
		padding: 40px 0;
		font-family: Impact, Haettenschweiler, "Franklin Gothic Bold", "Arial Black", "sans-serif";
		color: #000;
		font-size: 150px;
		text-shadow:
		-2px -2px 0 #fff,  
			2px -2px 0 #fff,
			-2px 2px 0 #fff,
			2px 2px 0 #fff;
		
	}
	#shop-by-category{margin-top: -30px;}
	#shop-by-category .categories-header {
		font-size: 32px;
		position: relative;
		text-align: center;
		margin: 0;
	}

	.footer-10 a{
		color:#ffffff;
	}
	.footer-10 h3{
		border-bottom:solid 1px #fff;
	}
	.footer-10 h3.pdf-title, .footer-10 .footerCompanyInfo_header h3{
		border-bottom:none;
	}
	.footer-10 .paytypes i{
		font-size:22px;
	}
	@media screen and (max-width:768px){
		.footer-10 .footerCompanyInfo, .footer-10 .paytypes, .footer-10 .catalog-section{
			border-top: solid 1px #fff;
			padding-top: 20px;
			margin-top: 20px;
		}
		.home-cat{
			height:unset;
		}
	}
	
	#top-store-links .nav>li>a{
		padding:0 15px;
		border-radius: 0;
		color: #fff;
	}
	#top-store-links .nav>li>a:focus, #top-store-links .nav>li>a:hover{
		background-color: transparent;
		text-decoration: underline;
	}
	#features .item-image{
		height:150px;
	}
	#features .item-image img{
		max-height:150px;
		display:inline-block;
	}
	#features>.row>div{
		border-left:solid 1px #ccc;
		margin-bottom: 60px;
	}
	#features>.row>div:nth-of-type(3n+0){
		border-right:solid 1px #ccc;
	}
	#features .item-description a{
		color:#f58220;
		font-weight: bold;
		font-style: italic;
	}
	span.new {
    	background: #da291c;
    	border-radius: 100%;
    	padding: 12px 7px 11px 6px;
    	color: #fff;
    	font-size: 12px;
    	position: absolute;
    	right: 40px;
	}
	span.promo {
    	background: #da291c;
    	border-radius: 100%;
    	padding: 22px 9px 21px 8px;
    	color: #fff;
    	font-size: 12px;
    	position: absolute;
    	left: 0;
    	bottom: -8px;
	}
	.free-promo span.promo {
    padding: 22px 18px 21px 17px;
}
	#features .item-promo {
    	width: fit-content;
		position: relative;
		margin-top: 10px;
}
	#features .item-promo-description {
	    background: #40698a;
    	border-top-right-radius: 50px;
    	border-bottom-right-radius: 50px;
    	font-size: 12px;
    	color: #fff;
    	padding: 5px 15px 5px 35px;
    	margin-left: 35px;
		margin-bottom: 15px;
		min-height: 44px;
	}
	#features .free-promo .item-promo-description {
    min-height: 44px;
    align-items: center;
    display: flex;
}
	#features .item-price{
		font-size: 24px;
    	color: #3dae2b;
		font-family: fantasy;
		padding-left: 0 !important;
	}
	.header-title{
		font-family: Impact, Haettenschweiler, "Franklin Gothic Bold", "Arial Black", "sans-serif";
		color: #000;
		font-size: 22px;
	}
	.breadcrumb {
		background-color: transparent;
	}
	.pagination{
		margin-top:0;
	}
	
	
	@media screen and (max-width:1199.98px){
		span.promo, .free-promo span.promo{
			position: relative;
    		padding: 3px 10px 2px;
    		border-radius: 0;
    		margin-bottom: 5px;
    		display: inline-block;
    		bottom: 0;
		}
		.free-promo span.promo{
			margin-bottom:0;
			margin-right: 5px;
		}
		#features .item-promo-description{
			padding: 10px 30px;
    		margin-left: 0px;
    		border-radius: 10px;
		}
		.bg-garage{
			background-size: contain;
    		
		}
		.bg-garage .display-1{
    		font-size: 80px;
		}
	}
	@media screen and (max-width:1024px){
		.pagination{
			display: block;
			clear: both;
		}
	}
	@media screen and (max-width:767.98px){
		#features>.row {
		    border-bottom: 0;
		}
		#features>.row>div:nth-of-type(odd) {
		    border-right: 0;
		}
		#features>.row>div{
			border-bottom: solid 1px #ccc;
		}
		.bg-garage{
    		height: auto;
		}
		.header-title{
			text-align: center;
		}
		
	}
	@media screen and (max-width:450px){
		.bg-garage .display-1{
    		font-size: 50px;
			padding: 20px 0;
		}
		.domain-name{
			text-align: center;
		}
		.domain-name p{
			position:relative;
			font-size: 20px;
			right: 0;
    		top: 0;
		}
	}
	@media screen and (max-width: 767px) and (min-width: 320px){
		header{
			background:none;
			padding: 0;
		}
		header .icon-shopping-cart{
			font-size:16px;
			position: relative;
			right: 0;
		}
		#dynamic-content-region:last-child h1{
			display: block;
		}
		#parts-catalog .list-item-wrapper-right .buy-button{
			font-size:16px;
		}
		#parts-catalog .toolbar{
			text-align:center;
		}
		.pagination{
			display:inline-block;
			margin: 5px !important;
		}
	}
