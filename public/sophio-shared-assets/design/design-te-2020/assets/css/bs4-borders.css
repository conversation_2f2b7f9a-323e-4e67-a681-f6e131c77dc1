.border         { border: 1px solid #dee2e6 !important; }
.border-top     { border-top: 1px solid #dee2e6 !important; }
.border-right   { border-right: 1px solid #dee2e6 !important; }
.border-bottom  { border-bottom: 1px solid #dee2e6 !important; }
.border-left    { border-left: 1px solid #dee2e6 !important; }

.border-0        { border: 0 !important; }
.border-top-0    { border-top: 0 !important; }
.border-right-0  { border-right: 0 !important; }
.border-bottom-0 { border-bottom: 0 !important; }
.border-left-0   { border-left: 0 !important; }

.rounded {
  border-radius: .25rem !important;
}
.rounded-top {
  border-top-left-radius: .25rem !important;
  border-top-right-radius: .25rem !important;
}
.rounded-right {
  border-top-right-radius: .25rem !important;
  border-bottom-right-radius: .25rem !important;
}
.rounded-bottom {
  border-bottom-right-radius: .25rem !important;
  border-bottom-left-radius: .25rem !important;
}
.rounded-left {
  border-top-left-radius: .25rem !important;
  border-bottom-left-radius: .25rem !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.rounded-0 {
  border-radius: 0 !important;
}