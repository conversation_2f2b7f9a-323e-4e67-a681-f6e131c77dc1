{"name": "cleave.js", "title": "cleave.js", "description": "JavaScript library for formatting input text content when you are typing", "keywords": ["cleave", "javascript", "html", "format", "form", "input", "input-mask", "mask", "formatter", "filter", "vanilla", "react"], "version": "1.6.0", "files": ["src", "dist", "react.js"], "author": {"name": "<PERSON>", "url": "http://github.com/nosir", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": "https://github.com/nosir/cleave.js/issues", "homepage": "https://github.com/nosir/cleave.js", "docs": "https://github.com/nosir/cleave.js/blob/master/README.md", "main": "dist/cleave.js", "module": "dist/cleave-esm.js", "repository": {"type": "git", "url": "https://github.com/nosir/cleave.js.git"}, "scripts": {"test": "gulp test", "build": "gulp build"}, "devDependencies": {"babel-core": "^6.11.4", "babel-eslint": "^6.0.5", "babel-loader": "^6.2.4", "babel-preset-es2015": "^6.9.0", "babel-preset-react": "^6.5.0", "babel-preset-stage-0": "^6.5.0", "babelify": "^7.3.0", "browserify": "^13.0.1", "chai": "^3.5.0", "create-react-class": "^15.5.2", "del": "^3.0.0", "errorify": "^0.3.1", "eslint-plugin-react": "^5.2.2", "gulp": "^3.9.1", "gulp-concat": "^2.6.0", "gulp-eslint": "^2.0.0", "gulp-header": "^1.8.3", "gulp-mocha": "^2.2.0", "gulp-mocha-phantomjs": "^0.12.0", "gulp-rename": "^1.2.2", "gulp-rimraf": "^0.2.0", "gulp-sync": "^0.1.4", "gulp-uglify": "^1.5.3", "mocha": "^3.1.2", "react": "^15.1.0", "react-dom": "^15.1.0", "require-dir": "^0.3.0", "rollup": "^1.7.0", "rollup-plugin-commonjs": "^9.2.1", "rollup-plugin-terser": "^4.0.4", "should": "^9.0.0", "underscore": "^1.8.3", "vinyl-source-stream": "^1.1.0", "watchify": "^3.7.0", "webpack": "^1.13.1", "webpack-stream": "^3.2.0"}}