/*DElete*/
.rating-star {
    display: inline-block;
    margin: 10px auto 0;
    width: 72%;
}
.grid-buy .col-sm-3.qty .form-group select {
    padding: 6px 0 6px 12px !important;
}
.list-grid-item#two .all-radio{
	height:95px;
}
.list-parts-grid .list-grid-item#two {
    min-height: 400px !important;
	height:490px !important;
}

.catalog-7 .list-grid-item.cat7 img {
    transform: translate(-50%,2%) !important;
    width: 80% !important;
}

.list-parts-grid .list-grid-item#zero {
    min-height: 460px !important;
}
.list-grid-item#zero {
    height: 460px !important;
}
.row-eq-height {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display:  flex;
}
.grid-buy .col-sm-3.qty .form-group {
    display: block;
    margin: 9px 0 9px;
    width: 93%;
}
ul.list-grid-item-price-block.list-unstyled li.item-price h5,
ul.list-grid-item-price-block.list-unstyled li.availability-check h5 {
    text-align: center !important;
    width: 100%;
    display: block;
    float: left;
}
ul.list-grid-item-price-block.list-unstyled li.availability-check h5 {
    padding-bottom: 10px;
}
.rw-ui-container.rw-ui-star.rw-size-medium.rw-dir-ltr.rw-halign-right.rw-valign-middle.rw-style-oxygen.rw-active {
     display: none !important;
}
.rating-star .rw-ui-container.rw-ui-star.rw-size-medium.rw-dir-ltr.rw-halign-right.rw-valign-middle.rw-style-oxygen.rw-active {
    display: block !important;
}

.table-view-all .rw-ui-container.rw-size-medium .rw-ui-info-inner-container span {
  font-size: 9px;
}
.table-view-all td:last-child br {
    display: none !important;
}
.table-view-all .rw-ui-star.rw-size-medium .rw-ui-stars li {
    margin: 0 !important;
}

.alert.notification-alternate-lookup{ padding: 6px}
.alert.notification-alternate-lookup .alert-heading{margin-bottom: 0}
.alert.notification-alternate-lookup  .nav>li>a {padding: 4px 16px;}

.list-unstyled.item-condition li, .list-unstyled.item-info li, .item-info li {
    padding: 1px 0;
    border-bottom: 1px solid #fafafa;
}

.notification-filter-parts, .alert.notification-filter-parts{padding:4px 15px;}


.makename-img { text-align: center;}

.makename-img img {display: inline-block; max-width: 66px; max-height: 66px}

h5.makename {
    bottom: 0;
    left: 0;
    margin-left: auto;
    margin-right: auto;
    position: static;
    right: 0;
}
.modelcolumn-col-img {
  max-height: 100% !important;
}

.modelcolumn-col-wrapper-img {
  padding: 0 !important;
  background: transparent !important;
  border-width: 0 !important;
  border-radius: 0 !important;
}

/*Helper Classes*/

.text-center-right, .text-center-left {
	text-align: center;
}

.text-left-right {
	text-align: left;
}

.text-right-left {
	text-align: right;
}


.list-grid-item {
    border: 1px solid #eee;
    border-radius: 6px;
    box-shadow: none;
    padding: 3px;
	float:left;
	width:100%;
}

.list-parts-grid .col-sm-2.col-lg-2 .list-grid-item .list-grid-item-part-number, .list-parts-grid .col-sm-2.col-lg-2 .list-grid-item .list-grid-item-part-price {
 text-align: center;
}

.list-parts-grid .col-sm-2.col-lg-2 {
    padding: 0 2px;
}

.list-parts-grid .list-grid-item .btn-group-justified a {
    border: 0 none;
    font-size: 12px;
    padding: 2px 0;
}

.list-parts-grid .col-sm-2.col-lg-2 .list-grid-item .btn-group{
    display: block !important;
    width: 100% !important;
}

.list-grid-item .list-grid-item-part-title {
    color: #444;
    font-size: 20px;
    height: 68px;
    text-align: center;
}

.list-parts-grid .list-grid-item .btn.btn-primary.btn-block.buy-button {
    float: left;
    font-size: 12px;
    margin-bottom: 3px;
    margin-top: 9px !important;
    padding: 7px 0 !important;
}
.btn.btn-primary.btn-block.buy-button {
    margin:0 !important;
}
.btn-danger {
    display: inline-block  !important;
    margin-bottom: 6px  !important;
}
.list-parts-grid .list-grid-item .rw-ui-container {
 display: block !important;
 float: none !important;
 margin: 0 auto !important;
 text-align: center !important;
 width: 68% !important;
}
.list-parts-grid .list-grid-item .btn-group-justified {
    padding-bottom: 6px;
}
.list-grid-item .btn-group a {
    font-size: 10px;
}

.row.buy-panel {
    margin: 0 !important;
}

.list-unstyled.item-condition li {
    line-height: 20px !important;
}



/* Boxes Margins And Paddings */

.toolbar, .paging-layout-selector, #box-notification-and-garage, #box-product-image-buy, #filter-box, .makecolumn-col, .widget-sorting, #social-links, #yearcolumn, #modelcolumn, .parttypecolumn-col, .parttypes-with-facets-noimage-buttons, .list-grid-item-no-image, .list-grid-item-image, .list-grid-item , #quick-parts-selector {
	margin-bottom: 20px;
}

#box-product-image-buy {
	margin-top: 20px;
}

.pagination {
	margin-top: 0 !important;
}

.pagination-text {
	margin: 10px 0;
}

#quick-parts-selector {
	padding: 20px;
}

/* Grid */
.list-grid-item-no-image, .list-grid-item-image {
    width: 160px;
    height: 136px;
    margin: 0px auto 0 !important;
    position: relative;
    overflow: hidden;

}
.list-grid-item-image a img {
    margin: 0px!important;
	display:inline;
}

.list-grid-item-no-image h4 {
	margin-top: 35px;
}

/* List */
.list-col {
	margin-bottom: 20px;
}

.list-col-wrapper-img {
  border-radius: 4px;
  max-height: 300px;
  line-height: 300px;
  overflow: hidden;
}
.list-col-img {
	margin: 0 auto;
}

#categorycolumn .list-col-img {
  max-height: 280px;
  max-width: 200px;
  display: inline-block;
  vertical-align: middle;
  line-height: 15px;
}

#makecolumn .list-col-img {
  max-height:90px;
  min-height: 66px;
  max-width: 100px;
  display: inline-block;
  vertical-align: middle;
  line-height: 15px;
}

#makecolumn .makecolumn-wrapper-img {
    height: 100px;
}
#modelcolumn .list-col-img {
  max-height: 280px;
  display: inline-block;
  vertical-align: middle;
  line-height: 15px;
}

#modelcolumn .makecolumn-wrapper-img {
    height: 220px;
}
.list-item .product-image {
  max-height: 260px;
}

/* Carousel Fixes */
.carousel-inner > .item > img {
  position: absolute;
  top: 0;
  left: 0;
  min-width: 100%;
}
.carousel-inner > .item > img{
	height:auto!important;
	position:relative!important;
}
.carousel .item{
	overflow:hidden!important;
	height:auto;
}
.carousel-inner {
	position: relative;
	width: 100%;
	overflow: hidden;
}


/* ioslist jquery.list.js plugin */
.ioslistcontainer {
	position: relative;
}
.ioslist {
	border: 1px solid #2683BD;
	height: 550px;
	margin: 0;
	padding: 0;
	overflow: hidden;
	position: relative;
	font-family: sans-serif;
	font-size: 1em;
	margin-bottom: 20px;
}
.iolist:focus {
	opacity:1;
}
.ioslist dt {
	margin: 0;
	background: #2683BD url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAAD6CAYAAABXjuJnAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBNYWNpbnRvc2giIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6MzA4NDJBODU1QUE1MTFFMDg3QzNCOEZFNDlBQzQwQUEiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6MzA4NDJBODY1QUE1MTFFMDg3QzNCOEZFNDlBQzQwQUEiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDozMDg0MkE4MzVBQTUxMUUwODdDM0I4RkU0OUFDNDBBQSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDozMDg0MkE4NDVBQTUxMUUwODdDM0I4RkU0OUFDNDBBQSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PgIlVekAAAIxSURBVHja7NzRjcJADABRFiLqoiQaoj1EDyQxUAB8RETyet9I14BHYyfRiRYRlwPSML3/whgIwReORqAQEEIICCEEOwlZjUEhIIQQuCEKgUIUAoUoBAqBQhQChSgEClEICLGyoBAoRCFQiEKgEIVAIQqBQqAQhYAQQuCGKAT70yL4yIRfciAEhBACQgo99l6NwYshvBi6IbCyrCwoRCFQyNhCFmOwsmBlEQI3xA3BDkJmY1AIFKIQKEQhUIhCoJCxhfiWlUyIb1lWFhx1hcBRd9ShEDcECnFDoBC4IQqBQioI8S3LygIhbggUQggIIQSEDIwfn1EICLGyQEihlXU2hlxCVEIICCEEW4VMxqAQENKPkGYMbgisLIVAITWEnIxBIXBDFAI3RCFwQ+oL8S1LIXDUHXVYWY46FKIQKEQhUAgUohAopIIQ37KSCfEtixC4IW4IrCwrCwpRCBQyMGRYWbCyOqJFxN0Y3BAQQggIIQSEEAJCCAEhIIQQEEIICCEEhBACQkAIISCEEBBCCAghBISAEEJACCEghBAQQggIASGEgBBCQAghIIQQEAJCCAEhHfL5ib+bMeQSshpDLiFhDG4IrCwrCxuFLMagELghVhasLCsLhNQXMhtDHnw6SVjI0xg8ZUEhbggUUkPIwxi8h8BTVj9CfO1NJsS3LIWAEEcdHnsddbghbgjcEDcEhIAQNwQK8R4C7yFWFghxQ6AQEEIICPEegv/hv9+tLPziJcAA6DOUz/5YVlIAAAAASUVORK5CYII=) repeat-x 0 -50px;
	font-weight: bold;
	text-shadow: -1px -1px 0px rgba( 0, 0, 0, 0.2 ), 1px 1px 0px rgba( 255, 255, 255, 0.3 );
	color: white;
	padding: 8px 5px;
	border-bottom: 1px solid #1D6FA2;
}
.ioslist dd {
	padding: 5px;
	margin: 0;
	background-color: #fff;
	border-bottom: 1px solid #F2F6FB;
	font-size: 16px;
	color: #2A5673;
}
.iosbuttons {
	margin: 5px;
}
.iossmartsearch {
	width: 24px;
	position: absolute;
	right: 54px;
	width: 32px;
	top: 20px;
	z-index: 2;
	text-align: center;
	height: auto;
	opacity: 0.3;
	margin:0
}
.iossmartsearch:hover {
	opacity: 1;
	background-color: #999
}
.iosbuttons button {
	float: none;
	margin-right:2px;
	width: 22px;
}
.iossmartsearch button{
	margin:0;
}

/* Typehead */

.twitter-typeahead {
	width:100%;
	display: table !important;
}

.twitter-typeahead .tt-query,
.twitter-typeahead .tt-hint {
  margin-bottom: 0;
}
.tt-menu, .tt-dropdown-menu {
  cursor: pointer;
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 160px;
  padding: 5px 0;
  margin: 2px 0 0;
  list-style: none;
  font-size: 14px;
  text-align: left;
  background-color: #ffffff;
  border: 1px solid #cccccc;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  background-clip: padding-box;
  width:100%;
}

.tt-suggestion {
   display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: normal;
  line-height: 1.42857143;
  color: #333333;
  white-space: nowrap;
}

.tt-suggestions .tt-suggestion:hover {
  color: #fff;
  background-color: #f5f5f5;
  background-image: -moz-linear-gradient(top, #f5f5f5, #DFDFDF);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#f5f5f5), to(#DFDFDF));
  background-image: -webkit-linear-gradient(top, #f5f5f5, #DFDFDF);
  background-image: -o-linear-gradient(top, #f5f5f5, #DFDFDF);
  background-image: linear-gradient(to bottom, #f5f5f5, #DFDFDF);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f5f5f5', endColorstr='#DFDFDF', GradientType=0)
}

.tt-suggestion.tt-is-under-cursor a {
  color: #fff;
}

.tt-suggestion p {
  margin: 0;
}

/*      Checkbox and radio         */
.checkbox,
.radio {
  margin-bottom: 12px;
  padding-left: 24px;
  position: relative;
  -webkit-transition: color 0.25s linear;
  transition: color 0.25s linear;
  font-size: 12px;
  font-weight: normal;
  line-height: 1.5;
  color: #333333;
}
.checkbox input,
.radio input {
  outline: none !important;
  display: none;
}
#frmGetFreight .checkbox input, #frmGetFreight .radio input {
    display: block!important;
}
.checkbox .icons,
.radio .icons {
  color: #dddddd;
  display: block;
  height: 16px;
  left: 0;
  position: absolute;
  top: 1px;
  width: 16px;
  text-align: center;
  line-height: 16px;
  font-size: 16px;
  cursor: pointer;
  -webkit-transition: color 0.2s linear;
  transition: color 0.2s linear;
}
.checkbox .icons .first-icon,
.radio .icons .first-icon,
.checkbox .icons .second-icon,
.radio .icons .second-icon {
  display: inline-table;
  position: absolute;
  left: 0;
  top: 0;
  background-color: transparent;
  margin: 0;
  opacity: 1;
  filter: alpha(opacity=100);
}
.checkbox .icons .second-icon,
.radio .icons .second-icon {
  opacity: 0;
  filter: alpha(opacity=0);
}
.checkbox:hover,
.radio:hover {
  -webkit-transition: color 0.2s linear;
  transition: color 0.2s linear;
}
.checkbox:hover .first-icon,
.radio:hover .first-icon {
  opacity: 0;
  filter: alpha(opacity=0);
}
.checkbox:hover .second-icon,
.radio:hover .second-icon {
  opacity: 1;
  filter: alpha(opacity=100);
}
.checkbox.checked,
.radio.checked {
  color: #2ca8ff;
}
.checkbox.checked .first-icon,
.radio.checked .first-icon {
  opacity: 0;
  filter: alpha(opacity=0);
}
.checkbox.checked .second-icon,
.radio.checked .second-icon {
  opacity: 1;
  filter: alpha(opacity=100);
  color: #2ca8ff;
  -webkit-transition: color 0.2s linear;
  transition: color 0.2s linear;
}
.checkbox.disabled,
.radio.disabled {
  cursor: default;
  color: #dddddd !important;
}
.checkbox.disabled .icons,
.radio.disabled .icons {
  color: #dddddd !important;
}
.checkbox.disabled .first-icon,
.radio.disabled .first-icon {
  opacity: 1;
  filter: alpha(opacity=100);
}
.checkbox.disabled .second-icon,
.radio.disabled .second-icon {
  opacity: 0;
  filter: alpha(opacity=0);
}
.checkbox.disabled.checked .icons,
.radio.disabled.checked .icons {
  color: #dddddd;
}
.checkbox.disabled.checked .first-icon,
.radio.disabled.checked .first-icon {
  opacity: 0;
  filter: alpha(opacity=0);
}
.checkbox.disabled.checked .second-icon,
.radio.disabled.checked .second-icon {
  opacity: 1;
  filter: alpha(opacity=100);
  color: #D9D9D9;
}

.card-refine .panel-scroll {
  height: 273px;
  overflow-y: scroll;
}

.checkbox.checked, .radio.checked {
  color: #78937C;
  font-weight: bold;
}
.checkbox.checked .second-icon, .radio.checked .second-icon {

  color: #78937C;
}

.card-refine .header {
  padding: 10px 15px 0px 15px;
}
.card-refine .header h4 {
  line-height: 40px;
}
.card-refine .content {
  padding: 0;
}


.card-refine .panel-group .panel-heading + .panel-collapse .panel-body {
  border: 0;
}

.panel-group .panel+.panel {
  margin-top: 15px;
}

.card-refine .panel-title a {
	font-weight: bold;
  font-size: 14px;
  color: #6E6E6E;
	text-decoration: none;
}

.card-refine .price-left {
  float: left;
}
.card-refine .price-right {
  float: right;
}
.card-refine .panel-scroll {
  height: 273px;
  overflow-y: scroll;
}

.card-refine .collapsed i {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2);
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

.navfilters label.checkbox,
.navfilters label.checkbox a.name
{white-space: nowrap; overflow: hidden; text-overflow:ellipsis;}

/* Old Sophio Styles */

#parts-catalog .navfilters .years .data, #parts-catalog .navfilters .models .data, #parts-catalog .navfilters .makes .data, #parts-catalog .navfilters .sections .data, #parts-catalog .navfilters .groups .data, #parts-catalog .navfilters .subgroups .data, #parts-catalog .navfilters .parttypes .data, #parts-catalog .navfilters .manufacturers .data {
	max-height: 200px;
	overflow-x: hidden;
	overflow-y: auto;
}
.list-grid-item {
    border-radius: 6px;
    box-shadow: 0 0 2px 1px #eee;
    padding: 6px;
	height: 520px;
	text-align: center;
}
.list-grid-item .text-warning.list-grid-item-part-number, .list-grid-item .text-danger.list-grid-item-part-price {
  font-size: 12px !important;
  font-weight: bold;
/*line-height: 0 !important;*/
}
.list-grid-item .list-grid-item-fotsmost small {
  font-size: 12px;
}
.list-grid-item .list-grid-item-part-title {
  color: #444444;
  font-size: 12px !important;
  height: 30px !important;
  line-height: 13px !important;
  text-align: center;
}
.list-grid-item .list-grid-item-fotsmost {
    line-height: 17px;
}
.list-grid-item .text-success.canadamessage {
    display: none;
}
.list-grid-item .btn-group a {
	background: #eeeeee none repeat scroll 0 0;
	border: 1px solid #cccccc !important;
	border-radius: 4px !important;
	font-size: 10px;
	margin-bottom: 7px;
	padding: 5px 0 !important;
}
.list-grid-item .special-order-else span {
  border-radius: 3px;
  border-style: solid;
  border-width: 1px;
  color: #ffffff;
  float: left;
  margin-bottom: 2px;
  margin-top: 4px;
  padding: 4px 0;
  width: 100%;
}
.list-grid-item .special-order-else span .btn-warning {
  padding: 5px 0;
}
.list-grid-item .thumbnail {
  margin-bottom: 0;
}
.list-grid-item .btn-group a:hover {
    background: #cccccc none repeat scroll 0 0;
}



.select2-selection__rendered {
  line-height: 44px !important;
}

.select2-selection {
  height: 46px !important;
}
.select2-container .select2-selection--single {
    height: 46px;
}
.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: black;
}
 .select2-container--classic .select2-selection--single:focus {
    border: 1px solid black;
	}
.select2-container--classic.select2-container--open .select2-selection--single {
  border: 1px solid black; }
.select2-container--classic .select2-selection--multiple:focus {
    border: 1px solid black; }
.select2-container--classic.select2-container--open .select2-selection--multiple {
  border: 1px solid black; }
.select2-container--classic.select2-container--open .select2-dropdown {
  border-color: black; }
#input-keyword-search, .tt-hint {
    padding: 14px 9px 10px!important;
    height: auto;
}
.sort-by .sortby {
    height: 33px;
}

.select2-container--classic .select2-results>.select2-results__options {
    color:#fff;
    background-color: black;

}
.select2-container--default .select2-results>.select2-results__options {
    /* color:#fff;
    background-color: black; */

}

.select2-container--classic .select2-results__option--highlighted[aria-selected] {
	/* background-color: red!important; */
	 background-color: #ccc !important;
    color: #111 !important;
}
.select2-container--default .select2-results__option--highlighted[aria-selected] {
	/* background-color: red!important; */
	background-color: #ccc !important;
	color: #111 !important;
}
 .select2-container--classic .select2-selection--single:focus {
    border: 1px solid red!important;
}
.select2-container--classic.select2-container--open .select2-selection--single {
	border: 1px solid red!important;
}
.select2-container--classic .select2-selection--multiple:focus {
    border: 1px solid red!important;
}
.select2-container--classic.select2-container--open .select2-selection--multiple {
	border: 1px solid red!important;
}
.select2-container--classic.select2-container--open .select2-dropdown {
	border-color: red!important;
}



.fitment-modal .table-fitment-one-vehicle {
	 margin: 0;
}
.fitment-modal .table-fitment-one-vehicle th {
	 color: #444;
	 border: 1px solid #ccc !important;
}
.fitment-modal .table-fitment-one-vehicle {
	 font-size: 11px !important;
}
.fitment-modal .table-fitment-one-vehicle td {
	 border: 1px solid #ccc;
	 padding: 5px !important;
	 color: #444;
}
.fitment-modal .modal-body .well {
	 margin: 0 !important;
	 padding: 0 !important;
}
.grid-buy a.btn.btn-warning.btn-block {
    margin: 9px 0;
    float: left;
}
.list-grid-item .list-item-fitment {
	 height: 25px;
}
.list-grid-item ul.list-grid-item-price-block li h4 {
	 min-height: 50px;
}
div#wrap {
    background: #fff;
}
.list-grid-item {
    margin-bottom: 30px !important;
    padding: 3px ;
 /*   height: 530px !important;*/
    box-shadow: 0 0 2px 1px #eeeeee;
}
.btn-group-justified > .btn, .btn-group-justified > .btn-group{
	padding-right:2px;
}
.list-grid-item .list-grid-fits-your-vehicle::before {
	 content: "\2713";
	 padding-right: 10px;
	 font-size: 15px;
}
.list-grid-item .list-grid-fits-your-vehicle {
	 color: green;
	 border: 1px solid green;
	 padding: 3px 0;
	 font-size: 12px;
	 font-weight: bold;
}
.list-grid-item .grid-buy {
/*	float: left;*/
	 width: 100%;
}
.list-grid-item .grid-buy .form-group {
	 float: left;
	 width: 54px;
	 margin-right: 6px;
	display: none;
}
.list-grid-item .grid-buy .myPleaseWait {
	 float: left !important;
	 width: 100%;
	 padding: 7px 3px !important;
	margin-top: 0 !important;
}
.list-grid-item .text-warning.list-grid-item-part-number, .list-grid-item .text-danger.list-grid-item-part-price {
	 float: left;
	 width: 100%;
	text-align: center;
}
.list-grid-item .list-grid-item-part-title {
	 min-height: 0%;
	 text-align: left;
	color: #8a6d3b;
}
.list-grid-item .list-grid-item-price-block .radio {
	 width: 100%;
	 margin: 0 auto;
	 display: block;

    height: 36px;
}
.list-grid-item .list-grid-item-price-block .radio label {
	 text-align: left;
	 width: 100%;
	 padding-left: 0;
	 font-weight: bold;
	color: #8a6d3b;
}
.list-grid-item .list-item-partwarranty {
	 color: #8a6d3b;
	 font-size: 12px;
	 font-weight: 100 !important;
	 text-align: left;
	margin: 0;
}
.list-grid-item .list-item-availability {
	 color: #8a6d3b;
	 font-size: 12px;
	 font-weight: 100 !important;
	 text-align: left;
	  margin: 3px 0 0;
}
.list-grid-item .list-item-partwarranty span {
	 font-weight: bold;
	 font-size: 12px;
}
.list-grid-item .list-item-availability span{
	color: #8a6d3b;
	 font-weight: bold;
	 font-size: 12px;
}
.list-grid-item .list-item-partsourcezipcode span{
	color: #8a6d3b;
	 font-weight: bold;
	 font-size: 12px;
}
.list-grid-item .list-item-partsourcezipcode {
  color: #8a6d3b;
  font-size: 12px;
  font-weight: 100 !important;
  margin: 3px 0 0;
  text-align: left;
}
.list-grid-item .all-radio h6 {
    position: absolute;
    z-index: 10;
    left: 0;
    right: 0;
    top: -7px;
    background: #fff;
    margin: 0 40px;
    color: #8a6d3b;
    letter-spacing: 0.5px;
    font-weight: bold;
	font-size:10px;
}
.list-grid-item .all-radio {
    height: 130px;
    margin: 6px 0 0;
    padding: 14px 4px 0;
    float: left;
    width: 100%;
    border: 1px solid #ccc;
    position: relative;
}
li.availability-check {
    height: 80px;
    float: left;
    width: 100%;
}
.list-grid-item .list-grid-item-price-block .radio .delivery {
	 display: block;
	 margin-left: -24px !important;
}
.list-grid-item .list-grid-item-part-number {
	 font-weight: 100 !important;
}
.list-grid-item .list-grid-item-part-number span {
	 font-weight: bold;
}
.list-grid-item .list-grid-item-part-price span {
	 color: #8a6d3b;
	 font-weight: normal !important;
}
.list-grid-item .text-danger.list-grid-item-part-price {
	 font-size: 21px !important;
	 font-weight: 100;
	text-align: left;
	 padding-bottom: 3px;
}
.col-item
{
    border: 1px solid #E1E1E1;
    border-radius: 5px;
    background: #FFF;
}
.col-item .photo img
{
    margin: 0 auto;

}

.col-item .info
{
    padding: 10px;
    border-radius: 0 0 5px 5px;
    margin-top: 1px;
}

.col-item:hover .info {
    background-color: #F5F5DC;
}
.col-item .price
{
    /*width: 50%;*/
    float: left;
    margin-top: 5px;
	overflow:hidden;
}

.col-item .price h5
{
    line-height: 20px;
    margin: 0;
}

.price-text-color
{
    color: #219FD1;
}

.col-item .info .rating
{
    color: #777;
}

.col-item .rating
{
    /*width: 50%;*/
    float: left;
    font-size: 17px;
    text-align: right;
    line-height: 52px;
    margin-bottom: 10px;
    height: 52px;
}

.col-item .separator
{
    border-top: 1px solid #E1E1E1;
}

.clear-left
{
    clear: left;
}

.col-item .separator p
{
    line-height: 20px;
    margin-bottom: 0;
    margin-top: 10px;
    text-align: center;
}

.col-item .separator p i
{
    margin-right: 5px;
}
.col-item .btn-add
{
    width: 50%;
    float: left;
}

.col-item .btn-add
{
    border-right: 1px solid #E1E1E1;
}

.col-item .btn-details
{
    width: 50%;
    float: left;
    padding-left: 10px;
}
.controls
{
    margin-top: 20px;
}
.img-carousel
{
    max-height: 160px;
    height: 160px;
}

[data-slide="prev"]
{
    margin-right: 10px;
}
#carousel-similar-controls left,right {
	border: 1px solid black
}

a.btn-black {
    color: black;
    background-color: #fff;
    border: 1px solid black;
}
a.btn-black:hover
 {
    color: white !important;
    background-color: black;
    border-color: black;
}
.modal .modal-warehouse {
 width:70%;
}
.modal-warehouse input, .modal-warehouse button {
display:inline;
}
.mobile-view, .mobile{
  display: none;
}

@media screen and (max-width: 1022px) and (min-width: 768px) {
	.list-grid-item {
		height: auto;
	}
}

@media (min-width: 768px) {
	.text-center-right, .text-left-right {
		text-align: right;
	}
	.text-center-left, .text-right-left {
		text-align: left;
	}
    .list-col-wrapper-img {
	  min-height:  15px;
    }
}

@media screen and (max-width: 767px) and (min-width: 240px) {
	.fitment-modal .table-fitment-one-vehicle {
		display: block;
		overflow: scroll;
	}
}

@media screen and (max-width: 767px) and (min-width: 320px) {
  .logo.cc a.brand.mobile-logo, .logo.cc a.brand.mobile-logo img {
    padding: 0 !important;
}

#modelcolumn .col-xs-3.col-sm-2{
 border: 1px solid #ddd;
    margin: -1px 0 0;
    text-align: left;
    width: 100%;
    padding: 0;
}
#modelcolumn .col-xs-3.col-sm-2 a{
  font-size: 13px;
    font-weight: 600;
    text-align: left;
    margin: 0;
        float: left;
    padding: 12px 10px;
    width: 100%;
}

  #modelcolumn .col-xs-3.col-sm-2 a h4{
    font-size: 13px;
    font-weight: 600;
    text-align: left;
    margin: 0;
}

.part-page a {
    text-transform: unset !important;
}
#modelcolumn .col-xs-3.col-sm-2 a h4::after {
    content: "\f105";
    float: right;
    font-family: FontAwesome;
}

    .list-grid-item {
            height: auto;
        }
    .page-header.catalog-title .thumbnail{
        float: left;
    }

    .page-header span.cat-head-veh-right{
        float: right;
    }

    .list-grid-item .list-item-fitment{
      height: auto !important;
    }
    .list-grid-item .list-grid-item-part-title{
      height: auto !important;
    }
    .list-parts-grid #zero li.availability-check{
      height: auto !important;
    }
    .grid-buy .col-sm-3.qty .form-group{
      width: 100%;
    }
    .list-parts-grid .list-grid-item .btn.btn-primary.btn-block.buy-button{
      margin-bottom: 0px 0px 9px 0px !important;

    }
    .list-parts-grid .list-grid-item#zero{
          height: 400px !important;
    }
    .list-item .product-image {
        max-height: 100px;
    }
    .row.breadcrumbs-widgets ul.breadcrumb {
        padding-right: 0;
        margin-bottom: 0;
    }
    .row.breadcrumbs-widgets ul.breadcrumb li a {
        font-size: 12px !important;
        font-weight: normal;
    }
    .row.breadcrumbs-widgets ul.breadcrumb li.active {
        font-size: 12px !important;
        font-weight: 500;
    }
    .page-header.catalog-title {
        padding-top: 0;
        margin-top: 0;
    }
    form#frm-catalog-search div#filter-form-group button#part-listing-keyword-search-in {
        font-size: 12px;
        padding: 6px;
    }
    form#frm-catalog-search div#filter-form-group button.btn.btn-primary {
        font-size: 12px;
        padding: 0;
    }
    form#frm-catalog-search div#filter-form-group input#input-keyword-search {
        font-size: 12px !important;
        padding-left: 0 !important;
        padding-top: 14px !important;
    }
    .list-parts-grid .list-grid-item .btn.btn-primary.btn-block.buy-button {
        margin-bottom: 10px !important;
    }
    .row.breadcrumbs-widgets ul.breadcrumb {
        padding-right: 0;
        margin-bottom: 0;
    }
    .row.breadcrumbs-widgets ul.breadcrumb li a {
        font-size: 12px !important;
        font-weight: normal;
    }
    .row.breadcrumbs-widgets ul.breadcrumb li.active {
        font-size: 12px !important;
        font-weight: 500;
    }
    .page-header.catalog-title {
        padding-top: 0;
        margin-top: 0;
    }
    form#frm-catalog-search div#filter-form-group button#part-listing-keyword-search-in {
        font-size: 12px;
        padding: 6px;
    }
    form#frm-catalog-search div#filter-form-group button.btn.btn-primary {
        font-size: 12px;
        padding: 0;
    }
    form#frm-catalog-search div#filter-form-group input#input-keyword-search {
        font-size: 12px !important;
        padding-left: 0 !important;
        padding-top: 14px !important;
    }
    div#product-info-tabs a {
        padding: 4px !important;
        font-size: 14px !important;
    }
    .list-item .col-sm-3 {
    height: auto !important;
    text-align: center;
}
.tab-content.liquid-container {
	margin: 0 -15px;
}
a#departments-dropdown, a#garage-dropdown {
	padding: 4px;
	font-size: 11px;
}
div#desc {
    padding-top: 10px;
}
.mobile-view .tab-content.liquid-container {
	margin: 0 !important;
}
#table-fitment1 tr th, #table-fitment1 tr td {
    border: 1px solid #ccc !important;
    padding: 7px;
    text-align: center;
}
.bigbtn .btn.btn-info {
    width: 100% !important;
    font-size: 10px;
    padding: 21px;
    margin-bottom: 4px;
}
.bigbtn a.btn.btn-danger {
    font-size: 10px;
    width: 100%;
}
.col-xs-11.addressdiv {
    padding-left: 0;
}
.bigbtn .btn.btn-info.firstbtn{
	margin-top: 12px;
}
.tabsnew .panel-heading {
    padding: 0;
    margin-bottom: 10px;
    border: 0;
	border-radius: 0;
        background: transparent;
}
.tabsnew .panel-body {
    padding: 5px 0 !important;
}
.tabsnew .panel-heading ul.nav.nav-tabs {
    border: 0;
	border-radius: 0;
}
.tabsnew .panel-heading ul.nav.nav-tabs li.active a {
    opacity: 1;
    color: #fff;
    background: transparent;
}
.tabsnew .panel-heading ul.nav.nav-tabs li {
    width: 50%;
    text-align: center;
    border-radius: 0;
    border-bottom: 3px solid transparent!important;
    background: #F3F3F3;
}

.tabsnew .panel-heading ul.nav.nav-tabs li.active {
    background: #f0ad4e;
    border-bottom: 3px solid #f0ad4e!important;
}
.tabsnew .panel-heading ul.nav.nav-tabs li a {
    margin: 0;
    opacity: 0.5;
	border-radius: 0;
    border: 0 !important;
}
.tabsnew.panel-primary {
    border-color: transparent!important;
}
#map{
    width: 100%;
     height: 300px;
    line-height: initial;
}
.no-padding{padding: 0;}

	.panel-primary {
		border-color: #000 !important;
		float: left;
		width: 100%;
	}
	.main-upper-div {
		float: left;
		width: 100%;
	}
    .map-location-list .listing {
        background: #F3F3F3;
        margin-bottom: 13px;
        padding: 10px;
        border: 0;
    }
	.right-all {
		float: none !important;
		margin: 0 !important;
	}
    .map-location-list {
        height: 260px;
        overflow: hidden;
        overflow-y: scroll;
    }
    .loctaion_mapping {
        float: left;
        width: 100%;
    }
    .loctaion_mapping .col-sm-4 form.top-map-search{
        background: #F3F3F3;
        border-bottom: 1px solid #ccc;
        margin-bottom: 20px;
        padding-top: 15px;
    }
    .loctaion_mapping .col-sm-4 fieldset {
        padding: 0 10px;
    }
    .loctaion_mapping .col-sm-4 fieldset input {
        border-radius: 0;
    }

	   .listing{border-bottom: 1px solid #ccc;
    display: inline-block;width:100%;
    padding: 10px 0;}
   .listing span.clr{ clear: both;
    display: block;}
	.listing address{margin-bottom:10px}
	.listing address small.strong{font-weight:bold}
		.listing .col-sm-4 button{  margin-top:17px}
		.listing .col-sm-4 button i{font-size:0}

			.listing .col-sm-4 button{font-size:0; background-color:#ededed !important;border:0}
			.listing .col-sm-4 button i{font-size:60px;color:#000;}
			.listing span{clear:both;display:block}
			.listing .col-sm-8{width:60%;float:left}
			.listing .col-sm-4{width:40%;float:left}.listing .col-sm-4 button {
    margin-top: 6px;
}

.list-item .col-sm-3 .buy-panel-image {
    position: static !important;
    text-align: center;
}
.modal-content .vehicle-locator-afmkt-vertical fieldset {
    float: left;
    margin-bottom: 30px;
    width: 100%;
}
.modal-content .vehicle-locator-afmkt-vertical li a {
    background: #eee none repeat scroll 0 0;
    font-size: 10px !important;
}
.modal-content .vehicle-locator-afmkt-vertical .form-group {
    margin-bottom: 5px;
}
.modal-content .vehicle-locator-afmkt-vertical select {
    border: 1px solid #eeee;
    font-size: 12px !important;
    text-transform: uppercase;
}
.modal-content .alert.alert-info {
    font-size: 10px !important;
    padding: 6px !important;
}
.modal-content .vehicle-locator-afmkt-vertical .nav.nav-pills.nav-stacked li {
    float: left !important;
    margin: 0 0 4px 3px !important;
}
#shipping-address-fields input, #shipping-address-fields select, #shipping-address-fields .form-group {
    padding-left: 10px;
    width: 100%;
}
#shoppingcart-with-checkout .cart-table tbody td, #cart-table .item-options {
    padding: 0 !important;
}
#shoppingcart-with-checkout .container {
    padding: 0;
}
#shoppingcart-with-checkout h3 {
    font-size: 17px;
}
#sophio-returns table {
    height: auto !important;
}
#parts-catalog .btn.btn-primary.btn-block.buy-button i {
    display: none;
}
#parts-catalog .btn.btn-primary.btn-block.buy-button {
    padding-bottom: 10px;
    padding-top: 10px;
    text-align: center;
}
#facet-category-parttype-list .list-group-item.active::after {
    content: none;
}
#facet-category-parttype-list .list-group .list-group-item::after {
    position: absolute;
    right: 10px;
}
.table.table-bordered.cart-table #intl-shoppingcart-description {
    color: #444;
    font-weight: bold;
}

header .smartsearch {
    padding-bottom: 9px;
}
header .smartsearch .btn-primary {
    border-radius: 0 5px 5px 0;
    padding: 4px 0;
    width: 100%;
}
header .dropdown {
    float: left;
    position: relative;
    top: 9px;
}
.gggggg .col-xs-8 {
    padding: 0 0 15px 0 !important;
}
.col-sm-5.gggggg {
    padding-bottom: 15px;
}
.fits{
	border: 1px solid #ccc;
    padding: 10px;
    border-radius: 5px;
}
li.freeshipping {
    padding-top: 10px;
}
.row.about {
    border: 1px solid #ccc;
    border-radius: 5px;
    margin: 0;
    padding: 0 10px;
}
.pics a {
    float: left;
    margin-bottom: 10px;
}
.bodytableinverse {
    display: none;
}
#desc dt {
    float: left;
    width: 50%;
}
#parts-catalog.part-details .buy-panel .list-unstyled .partsellprice h3 {
    position: relative;
    right: 7px;
}
.gggggg h1 {
    display: block !important;
}
.freeshipping .form-group {
    margin-bottom: 10px;
}
.fits a{
	text-decoration:none;
}
.fits i.fa-chevron-right.fa{
	float:right;
}
header .dropdown > button {
    background: transparent none repeat scroll 0 0;
    border: 0 none;
    padding: 0;
}
header .dropdown-menu {
    left: inherit !important;
    min-width: 139px !important;
    right: 0;
 }
 .ad {
    float: left;
    margin-bottom: 10px;
    width: 100%;
}
 .ad img {
    width: 100%;
}
header .dropdown > button i {
    border-radius: 100%;
    color: #fff;
    font-size: 15px;
    height: 25px;
    line-height: 25px;
    width: 25px;
}
header .cart-con b {
    color: #444;
    font-size: 13px;
    font-weight: bold;
    position: absolute;
    right: 13px;
    top: -7px;
}
header .icon-shopping-cart {
    font-size: 20px;
    position: absolute;
    right: 5px;
    top: 0;
}
#catalog-6 .list-item-wrapper-right img {
    width: 100%;
}
#catalog-6 .text-danger.buy-panel-sell-price {
    font-size: 13px;
    font-weight: bold;
    margin-left: -4px !important;
}
.desktop-view {
	display: block !important;
}
.brand img {
    padding-top: 7px;
    width: 110px !important;
}
.row.all-filter-result {
    background: #fafafa none repeat scroll 0 0;
    border-bottom: 1px solid #eee;
    border-top: 1px solid #eee;
    padding: 6px 0;
}
.row.all-filter-result .filterleft p {
    margin: 0;
    padding-top: 3px;
}
.row.all-filter-result .filteright .btn-group.widget-sorting {
    margin: 0;
}
.top-ad .col-sm-9 > p {
}
.top-ad .col-sm-3 {
    float: left;
    width: 30%;
}
.top-ad .col-sm-3 > img {
    vertical-align: top;
    width: 100%;
}
.header-17.index.sticky {
    display: block !important;
    padding: 0;
}
.col-sm-6.filteright {
    float: right;
}
.col-sm-6.filterleft {
    float: left;
}
#top-header .navbar-header.banner {
    background: #fff !important;
    padding-top: 10px;
    padding-left: 10px;
    padding-right: 0;
    padding-bottom: 0;
}
.header-17.index  .navbar-header.banner {
    padding-top: 10px;
    padding-left: 10px;
    padding-right: 0;
    padding-bottom: 0;
}

.container, .navbar-static-top .container, .navbar-fixed-top .container, .navbar-fixed-bottom .container {
    width: 100% !important;
}

.panel.panel-primary .panel-body .btn.btn-info {
    padding: 7px 4px !important;
}

.alert.alert-danger {
    display: none !important;
}
.table-bordered.cart-table{margin-top:25px !important;}
#top-header .container, .header-17.index .container {
    padding: 0 5px!important;
}
.part-page .list-group .list-group-item::after {
		content: "\f105";
		float: right;
		font-family: FontAwesome;
	}
.top-login .btn-group .btn.btn-primary {
    background: #232f3e !important;
}
.top-login .footerlink:hover {
    color: #fff;
}
.navbar-header .col-md-3.col-sm-3.col-xs-4 {
    padding: 0;
}
.navbar-header .col-md-3.col-sm-3.col-xs-2 {
    padding: 0;
}
#ajaxIndicator {
    padding: 6px !important;
    width: inherit !important;
}
#ajaxIndicator #ajaxIndicatorText{
	font-size: 10px !important;
}
.checkbox input, .radio input {
    display: block;
}
.part-page  a {
    color: #232f3e;
    font-size: 12px;
    text-transform: uppercase;
}
.part-page  a.list-group-item.active {
    background-image: none;
}
.part-page .groupsubgroups a.list-group-item.active {
  background-image: none!important;
  background-color: #232f3e!important;
  color: #fff!important;
}
.part-page  .groupsubgroups a.list-group-item {
  background-image: url("images/arrow-in-all-hover.png");
  background-position: right center !important;
  background-repeat: no-repeat !important;
}
.part-page  a.list-group-item:hover {
	background-color: transparent;
	text-decoration: underline;
	color: #000;
}
.part-page .groupsubgroups  a.list-group-item:hover {
	background-color: transparent;
	text-decoration: underline;
	color: #000;
	background-image: url("images/arrow-in-all-hover.png");
	background-position: right center !important;
	background-repeat: no-repeat !important;
}
.part-page  a  small {
    display: none;
}
.items-slider {
  float: left;
  width: 100%;
}
.items-slider .caption-price i {
  color: #b4b4b4;
}
.item-slider-title .control-box.pager a i {
  color: #000;
  font-size: 25px;
  font-weight: bold !important;
}
.item-slider-title .control-box.pager a {
  background: #d2d2d2 none repeat scroll 0 0;
  border-radius: 100%;
  height: 25px;
  line-height: 20px;
  padding:0;
  text-align: center;
  width: 25px;
}
.slider-title {
    height: 30px;
    overflow: hidden;
}
.caption p {
    color: #999;
    height: 35px;
    overflow: hidden;
}
.items-slider .caption-price span {
    color: #232f3e;
    font-weight: bold;
}
.items-slider .caption-price p {
  color: #444;
  font-weight: bold;
  margin: 0;
  text-decoration: line-through;
}
.items-slider .fff {
    border: 1px solid #232f3e;
    float: left;
    height: 354px;
    padding: 0 10px;
    width: 100%;
}
.items-slider .thumbnail {
    background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
    border: 0 none;
    border-radius: 0;
    margin-bottom: 0;
}
.items-slider .thumbnail {
  background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
  border: 0 none;
  border-radius: 0;
  margin-bottom: 0;
}
.control-box {
    margin: 0 !important;
    padding-right: 17px;
    text-align: right !important;
    width: 100%;
}
.item-slider-title {
    background: #232f3e;
    border: 1px solid #232f3e;
    border-radius: 4px;
    padding: 10px 0;
}
.control-box.pager.flex-direction-nav {
  padding: 0 10px;
}
.item-slider-title h2 {
  color: #fff;
  float: left;
  font-size: 10px;
  font-weight: bold;
  margin: 9px 0 0;
  padding-left: 16px;
  text-transform: uppercase;
}
.table.table-bordered {
    margin-left: -15px;
    margin-right: -15px;
}
/*====================================================================================================================*/
.flex-container a:hover,
.flex-slider a:hover,
.flex-container a:focus,
.flex-slider a:focus {
  outline: none;
}
.slides,
.slides > li,
.flex-control-nav,
.flex-direction-nav {
  margin: 0;
  padding: 0;
  list-style: none;
}
.control-box.pager.flex-direction-nav {
    display: block;
}
.flex-direction-nav, .flex-control-paging {
    display: none;
}
/* ====================================================================================================================
 * BASE STYLES
 * ====================================================================================================================*/
.flexslider {
  margin: 0;
  padding: 0;
}
.flexslider .slides > li {
  display: none;
  -webkit-backface-visibility: hidden;
}
.flexslider .slides img {
    display: block;
    height: 134px !important;
    padding: 10px;
    width: 100%;
}
.flexslider .slides:after {
  content: "\0020";
  display: block;
  clear: both;
  visibility: hidden;
  line-height: 0;
  height: 0;
}
html[xmlns] .flexslider .slides {
  display: block;
}
* html .flexslider .slides {
  height: 1%;
}
.no-js .flexslider .slides > li:first-child {
  display: block;
}
/* ====================================================================================================================
 * DEFAULT THEME
 * ====================================================================================================================*/
.flexslider {
  margin: 0 0 10px;
  background: #ffffff;
  border: 4px solid #ffffff;
  position: relative;
  zoom: 1;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  -webkit-box-shadow: '' 0 1px 4px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: '' 0 1px 4px rgba(0, 0, 0, 0.2);
  -o-box-shadow: '' 0 1px 4px rgba(0, 0, 0, 0.2);
  box-shadow: '' 0 1px 4px rgba(0, 0, 0, 0.2);
}
.flexslider .slides {
  zoom: 1;
}
.flexslider .slides img {
  height: auto;
}
.flex-viewport {
  max-height: 2000px;
  -webkit-transition: all 1s ease;
  -moz-transition: all 1s ease;
  -ms-transition: all 1s ease;
  -o-transition: all 1s ease;
  transition: all 1s ease;
}
.loading .flex-viewport {
  max-height: 300px;
}
.flex-direction-nav {
  *height: 0;
}


.flex-control-nav {
  width: 100%;
  position: absolute;
  bottom: -40px;
  text-align: center;
}
.flex-control-nav li {
  margin: 0 6px;
  display: inline-block;
  zoom: 1;
  *display: inline;
}


.flex-control-thumbs .flex-active {
  opacity: 1;
  cursor: default;
}
 /*--start-Mobile-device-only--*/
.pagination > .active > a, .pagination > .active > a:focus, .pagination > .active > a:hover, .pagination > .active > span, .pagination > .active > span:focus, .pagination > .active > span:hover {
 background-color: #232f3e;
    border-color: #232f3e;
}
#quick-parts-selector select:disabled {
    color: #ccc !important;
    opacity: 0.8;
}
#quick-parts-selector .btn:disabled {
    color: #ccc;
    opacity: 0.8;
}
.pagination > li > a, .pagination > li > span {
  color: #232f3e;
}
.pagination > li.disabled a {
    background: #eee none repeat scroll 0 0 !important;
}
.page-header.catalog-title .thumbnail {
    display: none;
}
#parts-catalog #incrementalsearch {
    display: none;
}
.footer-navigation li:last-child {
	border: 0;
}
footer .footer-navigation li {
    border-right: 1px solid #eee;
    float: left;
    line-height: 13px;
    list-style: outside none none;
    padding: 0 5px;
}
.logo h1 {
    font-size: 10px;
    margin-top: 17px;
}
.list-all-in span, .list-all-in strong {
    font-size: 10px;
}
.list-all-in ul {
    margin: 0;
}
#quick-parts-selector {
    background: #232f3e none repeat scroll 0 0;
}
.part-page .list-group .list-group-item.active {
  padding: 10px 15px;
  color:#fff !important;
  border-bottom: 1px solid #fff;
}

.list-group .list-group-item.active {
	background: #000 none repeat scroll 0 0;
    border-color: #000;
}
.list-group .list-group-item h4 {
	font-size: 15px;
}

.part-page a:hover::after {
    color: #fff;
}
.sections.well li a {
    padding: 4px 10px;
}
.sections.well li {
    width: 100%;
	border-left: 1px solid #eee;
	border-right: 1px solid #eee;
	border-top: 1px solid #eee;
	padding: 0;
	margin: 0;
}
.sections.well li a span{
    font-size: 13px;
}
.sections.well {
    background: rgba(0, 0, 0, 0) none repeat scroll center top;
    border: 0 none;
    padding: 0;
}
#parts-catalog .list-item-wrapper-right .btn.btn-mini.btn-inverse.btn-default.btn-block{
	display: none;
}
.iossmartsearch {
  height: auto;
  margin: 0 0px 10px 0;
  position: static;
  right: 0;
  text-align: center;
  width: 100%;
  opacity: 1;
}
.iossmartsearch:hover {
  opacity: 1;
  background: transparent;
}
.iosbuttons .btn {
    background: black none repeat scroll 0 0;
    color: #fff;
    padding: 0;
	border-radius: 100%;
}
.iosbuttons .btn.disabled {
  background: rgba(216, 0, 0, 1) none repeat scroll 0 0;
}
.iosbuttons .btn:hover {
    background: rgba(216, 0, 0, 1) none repeat scroll 0 0;
}
.ioslist dt {
    background: rgba(216, 0, 0, 1) none repeat scroll 0 0;
    border-color: rgba(216, 0, 0, 1);
	width:100%;
}
.ioslist {
    border-color: rgba(216, 0, 0, 1);
}
.ioslist dd a span {
    font-size: 13px;
}
.groupsubgroups .list-group a.active {
  background: #232f3e none repeat scroll 0 0;
  border-color: #232f3e;
}
.btn-group.pull-right a {
    color: #676767 !important;
    text-decoration: underline;
}
.rc-anchor-normal-footer {
  display: none !important;
}
.popover-title{
	color: rgba(216, 0, 0, 1);
}
.popover-content{
	color:#000
}
#quick-parts-selector h2 {
  color: #fff;
  font-size: 13px;
  font-weight: bold;
  margin: 4px 0 10px;
  text-align: center;
  text-transform: uppercase;
}
#quick-parts-selector .form-group {
  margin-bottom: 3px;
}
#quick-parts-selector .btn {
    background-color: #fff !important;
    border-color: #fff !important;
    color: #000;
    width: 100%;
}
/* #parts-catalog .list-item-wrapper-left {
    float: left !important;
    width: auto !important;
} */
#catalog-start  #frm-search-partnumber .btn{
	width: auto;
}
#catalog-start .btn:hover {
    opacity: 0.7 !important;
}
#catalog-start .panel-primary li a {
    color: #000;
    padding: 0;
}
#catalog-start .panel-primary li:hover {
    background: #000;
}
#catalog-start .panel-primary li {
    padding: 3px 11px;
}
#catalog-start .panel-primary li:hover a{
	color: #fff
}
#catalog-start .panel-primary li a:hover{
	background: transparent;
}
#catalog-start .panel-primary .panel-heading {
    background-color: #000;
    border-color: #000;
    color: #fff;
    text-align: center;
}
#catalog-start .panel-primary {
    border-color: #000;
}
#quick-parts-selector select {
  background: #fff none repeat scroll 0 0;
  border: 0 none;
  border-radius: 3px;
  margin: 2px 0;
  padding: 5px 1px;
  width: 100%;
  color: #000;
}
#quick-parts-selector {
	background: #000;
	padding: 10px 15px;
}
#catalog-start .well.well-small {
	display: none;
}
#accordion {
    display: none;
    float: left;
    position: fixed;
    top: 139px !important;
    width: 91%;
    z-index: 10;
}
/* #parts-catalog .list-item-wrapper-right {
    width: 100% !important;
} */
.table.table-bordered .thumbnail {
    max-height: 100% !important;
}
.btn-info {
    background-color: #232f3e;
    border-color: #232f3e;
    color: #fff;
}
#accordion .panel-heading:hover {
    background: #000 none repeat scroll 0 0 !important;
}
#accordion .panel.panel-default {
    margin: 0 !important;
    padding: 0 !important;
}
#accordion .panel-heading a {
    color: #fff;
}
#accordion .panel-heading {
    background: #232f3e!important;
    color: #fff !important;
	padding: 7px 12px;
}
.tab-content .well.well-small {
	background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
	border: 0 none;
	padding: 0;
}
.tt-dropdown-menu p {
	text-transform: capitalize !important;
	font-size: 12px !important;
	font-weight: normal !important;
	text-align: left !important;
	float: left !important;
	cursor: pointer;
	width:100%;
}
.tt-dropdown-menu p:hover {
  color:#cc0000;
}
#additional-form-optin .alert.alert-warning {
    z-index: 100;
    position: relative;
    float: left;
    margin: 0;
    padding: 7px;
}
#order-totals span a {
    margin-left: calc(-30vw + 50%);
    width: 99.4vw;
}
#order-totals tr.smalltext {
    display: none !important;
}
.search-top-all .well.well-small {
    padding: 0;
	border: 0 none;
}
#parts-catalog > #parttypesearch {
    display: none !important
}
.list-all-in .list-unstyled.item-info {
    display: none;
}
.list-item-wrapper-left .alert.alert-warning {
  display: none;
}
.inner-pages .filter-menu{
	background: #000;
    color: #fff;
    font-size: 11px;
    font-weight: bold;
    margin: 10px 0 6px;
    padding: 5px 9px;
    position: fixed;
    top: 44px;
	left: 15px;
    z-index: 10;
	opacity: 0.4;
}
.inner-pages .filter-menu:hover {
	background: #232f3e;
    color: #fff;
	opacity: 1;
}
.btn-primary.buy-button {
	color: #fff !important;
}
.btn-primary.buy-button:hover {
	color: #fff !important;
	opacity: 1 !important;
}
.panel-primary .panel-heading{
    background-color: black !important;
    border-color: black !important;
	color: #fff !important;
}
.btn-primary:hover{
	opacity: 0.7!important;
}
.shopping-main {
	float:left;
	width:100%;
}
.shopping-main .shopping-main-col span {
	color: #676767;
	font-size: 12px;
	text-transform: none;
}
.shopping-main .shopping-main-col {
	float: left;
	text-align: center;
	width: 100%;
	margin-bottom: 19px;
	border-bottom: 1px solid #eee;
	padding-bottom: 10px;
}
.shopping-main .shopping-main-col.last-all {
	border: 0;
}
.shopping-main  .shopping-main-col h2 {
	color: #000;
	font-size: 14px;
	font-weight: normal;
	text-transform: uppercase;
}
.shopping-main .shopping-main-col i {
	color: #232f3e;
	font-size: 45px;
	font-weight: normal;
}
.span12.col-md-12.col-sm-9.col-xs-12.right-all {
  float: left!important;
  width: 100%!important;
}
.span10.col-md-10.col-sm-9.col-xs-12.right-all {
  float: left !important;
  width: 100%;
}
.panel.panel-login .forgot-password {
	color: #000!important;
}
.panel.panel-login .forgot-password:hover {
	opacity: 0.7;
}

.panel.panel-login .btn {
    background: orange none repeat scroll 0 0;
    border-color: orange;
    font-weight: bold;
}
.panel.panel-login .form-group {
    margin-bottom: 5px;
}
.panel.panel-login input {
    font-size: 12px !important;
    height: auto !important;
    line-height: 18px;
    padding: 8px 9px !important;
}
#login-form label {
    color: #fff;
}
.rc-anchor-normal {
    width: 64%!important;
}
.panel.panel-login .btn:hover {
	opacity: 0.7;
}
.panel.panel-login .panel-heading a:hover {
	background: orange none repeat scroll 0 0 !important;
	color: #fff;
}
.panel.panel-login a {
  background: rgba(255, 255, 255, 0.4) none repeat scroll 0 0 !important;
  float: left;
  padding: 10px 0;
  width: 100%;
}
.panel.panel-login .panel-heading a.active {
    background: #fff none repeat scroll 0 0 !important;
    color: #232f3e;
    font-size: 15px;
}
.panel.panel-login .panel-heading a {
	color: #fff;
	font-weight: bold;
}
.panel.panel-login {
	background: #232f3e none repeat scroll 0 0;
	border: 0 none;
	box-shadow: none;
	margin-top: 20px;
}
.panel.panel-login  .panel-heading {
	background-color: transparent;
	border-color: transparent;
	color: #00415d;
	text-align: center;
}
#contact .controls .help-block {
	color: #fff;
}
#contact .controls {
    margin-top: 10px;
}
#contact .col-sm-12 {
    float: left;
    width: 100%;
}
#contact .col-sm-12 .form-group {
	display: block;
	float: none;
	margin: 0 auto 9px;
	width: 87px;
}
#contact .form-group #submit {
	background: #000 none repeat scroll 0 0;
	border-color: #444;
	font-size: 12px;
	font-weight: bold;
	text-transform: uppercase;
}
#contact .form-group #submit:hover {
	opacity: 0.7;
}
#contact {
	background:transparent none repeat scroll 0 0 !important;
}
#contact  legend {
    color: #000;
    font-size: 17px;
    padding: 10px;
    text-align: center;
}
#contact .form-group {
	margin-bottom: 5px;
	margin-left: 9px;
	margin-right: 0;
}
.twitter-typeahead {
    display: table !important;
    width: 100%;
}
#parttypes-with-facets .btn-primary:hover {
    color: #000;
}
#parttypes-with-facets {
    float: left;
    margin-top: 25px;
    width: 100%;
}

.b table .pull-left {
    font-size: 27px !important;
    height: auto;
    line-height: 5px!important;
    vertical-align: top;
}
.b .product-image.img-responsive {
    margin: 0 auto;
}
.b table .pull-left {
    font-size: 27px !important;
    height: auto;
    line-height: 5px!important;
    vertical-align: top;
}
.b #btnBack, .b #btnContinueShopping, .b #btnEmptyCart, .b #btnFreightEstimate, .b #btnCheckout{
	font-size:0;
}
.b #btnBack i, .b #btnContinueShopping i, .b #btnEmptyCart i, .b #btnFreightEstimate i, .b #btnCheckout i{
  font-size: 23px;
}

.b #btnBack:hover, .b #btnContinueShopping:hover, .b #btnEmptyCart:hover, .b #btnFreightEstimate:hover, .b #btnCheckout:hover{
  position: relative;
}
.b #btnBack:hover:after {
	content: "Back";
}
.b #btnContinueShopping:hover:after {
	content: "Continue Shopping";
}
.b #btnEmptyCart:hover:after {
	content: "Empty Cart";
}
.b #btnFreightEstimate:hover:after {
	content: "Freight Quote";
}
.b #btnCheckout:hover:after {
	content: "Checkout";
}

.b #btnBack:hover:after, .b #btnContinueShopping:hover:after, .b #btnEmptyCart:hover:after, .b #btnFreightEstimate:hover:after, .b #btnCheckout:hover:after{
font-size: 12px;
  padding: 4px 8px;
  color: #333;
  position: absolute;
  left: 0;
  top: 100%;
  white-space: nowrap;
  z-index: 20px;
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  -moz-box-shadow: 0px 0px 4px #222;
  -webkit-box-shadow: 0px 0px 4px #222;
  box-shadow: 0px 0px 4px #222;
  background-image: -moz-linear-gradient(top, #eeeeee, #cccccc);
  background-image: -webkit-gradient(linear,left top,left bottom,color-stop(0, #eeeeee),color-stop(1, #cccccc));
  background-image: -webkit-linear-gradient(top, #eeeeee, #cccccc);
  background-image: -moz-linear-gradient(top, #eeeeee, #cccccc);
  background-image: -ms-linear-gradient(top, #eeeeee, #cccccc);
  background-image: -o-linear-gradient(top, #eeeeee, #cccccc);
}
.search-top-all.icon-search .btn.btn-primary.btn-large.btn-catalog-search::before {
	content: "\f002";
    font-size: 14px;
}
.search-top-all.icon-search .btn.btn-primary.btn-large.btn-catalog-search {
    font-size: 0;
}
.search-top-all.icon-search::before {
    display: none;
}
#parttypes-with-facets .btn-primary {
	background-color: #232f3e !important;
    border-color: #232f3e !important;
	color: #676767;
	font-size: 15px;
	font-weight: bold;
	padding: 0;
	text-align: left;
}
.search-top-all form#frm-catalog-search {
    float: left;
    padding: 0;
    width: 100%;
}
.search-top-all .form-group {
    margin: 0;
}
.search-top-all {
  float: left;
  margin-top: 76px;
  width: 100%;
}
.secondary-nav {
  background: #f8f8f8 none repeat scroll 0 0;
  border-bottom: 1px solid #f0f0f0;
  float: left;
  padding-bottom: 10px;
  width: 100%;
  margin-top: 48px;
}
.secondary-nav h3 {
  font-size: 14px;
  margin: 10px 0 5px;
}
.secondary-nav li {
    float: left;
    list-style: outside none none;
    padding-right: 18px;
}
.secondary-nav a {
    color: #676767;
    font-size: 13px;
    font-weight: bold;
	text-decoration: none;
}
.secondary-nav a:hover {
    color: #000;
}
#header-area .top-login {
  list-style: outside none none;
  margin: 10px 0;
  padding: 2px 0;
  text-align: center;
}
.table.table-bordered {
    border: 0 none;
}
.table.table-bordered .input-append .input-mini {
	border: 1px solid #f98c8c;
	margin: 0 10px !important;
	text-align: center;
}
.table.table-bordered tbody td {
	background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
	font-size: 10px;
	padding: 0 2px;
}
.table.table-bordered tbody td {
	background: none;
}
.table.table-bordered tbody td img {
    border: 0 none !important;
    margin: 0;
}
.table-bordered thead th {
    border: 0 none !important;
    color: #fff;
    font-size: 10px;
    padding: 4px 0 !important;
    text-align: center;
    text-transform: uppercase !important;
}
.table-bordered thead {
    background: #232f3e none repeat scroll 0 0;
}
.table-bordered .dl-horizontal{
	font-size: 9px;
    text-align: center;
}
.cart-buttons .btn-info {
    background-color: #ee1313;
    border-color: #232f3e;
    color: #fff;
}
.cart-buttons .btn-info:hover {
    background-color: #232f3e;
    border-color: #232f3e;
    color: #fff;
}
.cart-buttons  .btn-primary {
    background-color: #970808;
    border-color: #970808;
    color: #fff;
}
.cart-buttons .btn-primary:hover {
    background-color: #232f3e;
    border-color: #232f3e;
    color: #fff;
}
.cart-buttons .icon-shopping-cart {
    color: #fff;
	padding-right: 0;
}
.inner-pages.a {
  background: #fff;
}
#dynamic-content-region:last-child h1 {
  display: none;
}
body {
    font-family: verdana,arial,helvetica,sans-serif !important;
}
body {
  /*background: rgba(0, 0, 0, 0) url("images/bkgd_global_2013.gif") repeat scroll 0 0;*/
}
header {
    background: #232f3e none repeat scroll 0 0;
    border: 0 none;
    float: left;
    padding: 20px 0 5px;
    position: static;
    width: 100%;
    z-index: 999999999;
}
header.sticky {
	float: left;
    width: 100%;
    position: fixed;
    top:0;
}
footer #custom-search-input {
  background-color: #fff;
  border: 1px solid #e4e4e4;
  border-radius: 6px;
  padding: 3px;
}
footer #custom-search-input input, footer #custom-search-input select {
  font-size: 11px;
  font-style: italic;
  height: 34px;
  padding-left: 3px !important;
  border: 0;
  box-shadow: none ;
}
.navbar-default {
    background-color: transparent!important;
    border-color: transparent!important;
}
footer {
	background: #fff;
    border-top: none;
	float: left;
padding:0;
	/*padding-top: 40px;*/
	width: 100%;
	/*padding-bottom: 40px;*/
}
footer .footer-navigation {
    border: medium none;
    float: left;
    margin-bottom: 0;
    padding-bottom: 0;
    text-align: left;
    width: 100%;
}
.footer-copyright h4 {
    color: #fff;
    font-style: normal;
    font-weight: 700;
    line-height: 54px;
    text-shadow: none;
	 font-size: 18px;
}
 .footer-copyright a {
    color:#fff !important;
 }
footer h4{font-size: 22px;
    color: #000;
    text-transform: uppercase;
	}
.footer-copyright{
    color: #fff;
}
.part-page hr {
    display: none;
}
footer .footer-navigation h3 {
    color: #42444b;
    font-size: 15px;
    font-weight: bold;
    letter-spacing: -1px;
    margin-top: 2px;
    padding-left: 5px;
    text-align: left;
    text-transform: uppercase;
}
footer .footer-navigation ul{
	margin:0;
	padding: 0;
}
footer .footer-navigation li {
    border: 0 none !important;
    list-style: outside none none;
    width: 100%;
	padding: 8px 0 !important;
}
footer .footer-navigation li a{
	color:#fff;
	text-decoration:none;
	font-weight: normal;
}
.footer-newsletter {
	float: left;
	width: 100%;
}
footer .footer-navigation li a:hover {
    color: orange;
}
footer .footer-newsletter h2 {
	font-size: 16px;
	font-weight: bold;
	letter-spacing: -1px;
	margin-top: 18px;
	text-transform: uppercase;
	color:#42444b;
}
footer .footer-newsletter p {
    color: #444;
    line-height: 24px;
    padding-top: 9px;
}
footer .footer-newsletter p span{
	color:#42444b;
	font-weight: bold;
}
footer .gateway-secured {
	border-bottom: 1px solid #d1d1d3;
	border-top: 1px solid #d1d1d3;
	float: left;
	margin: 20px 0;
	padding: 20px 0;
	width: 100%;
}
footer .gateway-secured .border-left {
	border-color: #d1d1d3;
}
footer .gateway-secured h3 {
  color: #42444b;
  float: left;
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  text-transform: uppercase;
  width: 100%;
}
footer .gateway-secured img {
  float: left;
  width: 100%;
}
footer .copyrights a {
    color: #232f3e;
}
footer .follow-us li {
	float: left;
	list-style: outside none none;
	padding: 0px 5px;
}
footer .follow-us {
  float: left;
  text-align: left;
  width: 100%;
}
footer .follow-us ul{
	margin: 0;
}

footer .follow-us li a {
	background: #828487 none repeat scroll 0 0;
	border-radius: 100%;
	color: #fff;
	float: left;
	height: 25px;
	line-height: 25px;
	text-align: center;
	width: 25px;
}
footer .follow-us li a:hover {
	background: #232f3e;
}
footer .follow-us h3 {
    color: #fff;
    float: left;
    font-size: 12px;
    margin: 5px 8px 0 0;
}
footer .copyrights {
    color: #fff;
    float: left;
    font-size: 10px;
    margin-bottom: 5px;
}
.alert.notification-alternate-lookup .nav > li > a {
  color: #444;
}
.alert {
  margin-top: 0;
}
#orderitems_section .table {
    display: block;
    overflow: scroll;
}
.part-details .list-unstyled {
  display: block !important;
}
.table > tbody > tr > td, .table > tbody > tr > th, .table > tfoot > tr > td, .table > tfoot > tr > th, .table > thead > tr > td, .table > thead > tr > th {
  line-height: normal;
  padding: 0;
  vertical-align: inherit;
}
#parts-catalog.part-details .buy-panel .list-unstyled  h3 {
  font-size: 20px;
  margin: 0;
  text-align: left;
}
#parts-catalog.part-details .btn-primary {
  background-color: #f9c250;
  border-color: #a68540;
  color: #444;
  text-align: left;
}
#parts-catalog.part-details .btn-primary:hover {
  background-color: #fcdca3;
}
#parts-catalog .list-col {
	border-left: 1px solid #eee;
	border-right: 1px solid #eee;
	border-top: 1px solid #eee;
	padding: 1px 12px;
	margin: 0;
}
#parts-catalog .list-col h4 {
    text-align: left;
	font-size: 12px;
}
#parts-catalog .list-col {
	border-left: 1px solid #eee;
	border-right: 1px solid #eee;
	border-top: 1px solid #eee;
	padding: 1px 12px;
	margin: 0;
}
#parts-catalog .list-col {
	border-left: 1px solid #eee;
	border-right: 1px solid #eee;
	border-top: 1px solid #eee;
	padding: 1px 12px;
	margin: 0;
}
#parts-catalog .list-col h4 {
    text-align: left;
}
#parts-catalog .list-col h5 {
    font-size: 12px;
	text-transform: uppercase;
}

/*--parts-catalog--*/
#parts-catalog .buy-panel br {
    display: none;
}
#parts-catalog .buy-panel .list-unstyled {
  display: none;
}
#parts-catalog .text-muted.list-item-oem {
    display: none;
}
#parts-catalog #product-info-tabs dl {
    font-size: 14px;
}
#parts-catalog #product-info-tabs .table {
    font-size: 10px;
}
#parts-catalog #product-info-tabs  table {
  font-size: 14px;
}
#parts-catalog  h1 {
    font-size: 15px !important;
}
#parts-catalog  .buy-panel h3 {
    margin: 0;
}
#parts-catalog #product-info-tabs .nav.nav-tabs a {
  padding: 7px 4px;
}
#parts-catalog #product-info-tabs  dt {
    float: left;
    margin-right: 5px;
}
.list-item-fitment, #parts-catalog .list-item-fitment-heading{
	display:none;
}
.list-item {
	padding-bottom: 10px;
	padding-top: 7px;
}

.toolbar{
	display: none;
}

.list-item-wrapper-left .part-title {
	font-size: 14px;
	margin-top: 0;
	margin-bottom: 0;
}
.list-item-wrapper-left .list-inline li h5 {
    margin: 0;
}
.list-item-wrapper-left .list-inline li {
	display: block;
}
.list-item-wrapper-left .alert-info{
	display: none;
}
.list-item-wrapper-right .btn-info{
	display: none;
}

/*--parts-catalog--*/
#parts-catalog .buy-panel br {
    display: none;
}
#product-info-tabs dl {
    font-size: 10px;
}
#product-info-tabs .table {
    font-size: 10px;
}
#product-info-tabs  table {
  font-size: 10px;
}
#parts-catalog  h1 {
    font-size: 15px !important;
}
.buy-panel h3 {
    margin: 0;
}
#product-info-tabs .nav.nav-tabs a {
  color: #444;
  font-size: 11px;
  padding: 7px 3px;
}
#parts-catalog #product-info-tabs  dt {
     float: left;
    margin-right: 5px;
	padding0bi
}
.list-item-fitment #parts-catalog .list-item-fitment-heading{
	display:none;
}
.list-item {
	border-bottom: 1px solid #cc0000;
	padding-bottom: 10px;
	padding-top: 7px;
	float: left;
	width: 100%;
}

.toolbar{
	display: none;
}
.part-title  a {
  color: #141214 !important;
  font-size: 12px;
  font-weight: bold;
}

.list-item-wrapper-left .part-title {
	font-size: 14px;
	margin-top: 0;
	margin-bottom: 0;
}
.list-item-wrapper-left .list-inline li h5 {
    margin: 0;
}
.list-item-wrapper-left .list-inline li {
	display: block;
}
.list-item-wrapper-left .alert-info{
	display: none;
}
.list-item-wrapper-right .btn-info{
	display: none;
}
/*--end-Mobile-device-only--*/

html body {
	font-size: 13px;
	font-family: Arial, Helvetica, sans-serif;
}
a {
	color: #232f3e;
}
a:hover {
	color: black ;
}
.catalog-vehicle ul li ul li a, .highslide-body ul li ul li a, #catalog-search-jobs ul li ul li {
	white-space: normal !important;
}
#dynamic-content-region h1 {
	font-size: 13px;
	padding: 0px 0px !important;
	margin: 0px !important
}
.navbar-inner {
	background-color: #FFBE00 !important;
	background-image: -moz-linear-gradient(center top, #FFC622, #FFBE00) !important;
	background-repeat: repeat-x;
	border-radius: 0px !important;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.25), 0 -1px 0 rgba(0, 0, 0, 0.1) inset;
	padding-left: 20px;
	padding-right: 20px;
}
.contenthighlight {
	color: #FF0000;
	font-weight: bold
}
#main-content-area, #pageheader-holder, .navbar-inner .container {
	margin: 0px auto !important;
}
.navbar .nav > li > a {
	color: #000000 !important;
	text-shadow: none !important
}
.navbar .nav .active > a, .navbar .nav .active > a:hover {
	color: #000000 !important
}
#top-page-region {
	height: 136px;
	background: url(images/bg_body.jpg) #FFDD33 repeat-x;
}
#pageheader-holder {
	margin: 0px auto;
}
#companylogo {
	height: 78px;
	width: 100%;
	margin-top: 58px;
	background: url(images/header.png) no-repeat;
}
#sidebar-1-region {
	min-height: 400px;
}
.sidemenu-sub-links {
    margin-left: 0;
    padding: 0;
}
.sidemenu-make:hover {
    text-decoration: none;
}

body {
    padding-left: 0 !important;
    padding-right: 0 !important;
}
.topmenulink:hover {
    background: #ebcf6e none repeat scroll 0 0 !important;
}
.right-all {
    float: right !important;
    margin: 0 !important;
}
.left-all {
    background: #f5f5f5 none repeat scroll 0 0;
    border: 1px solid #e3e3e3;
    float: left !important;
    margin: 0 !important;

}
img.sidebar-image {
    position: relative;
    bottom: 60px;
}
.googletranslate {
  float: left;
  margin-top: 14px;
  text-align: right;
  width: 40%;
}
.centeror {
    font-weight: 600;
    font-size: 17px;
}
#dynamic-content-region {
	margin-left: 20px;
	margin-bottom: 20px;
}
#pageheader-holder>.row{
	padding:0px;
	margin:0px;
}
#pageheader-holder>.row>div{
	padding:0px;
}
#footer-holder>.row{
	padding:0px;
	margin:0px;
}
#footer-holder>.row>div{
	padding:0px;
}
#checkout-dynamic-content-region {

}
.sidemenu-links {
	list-style: none;
	color: #CC0000;
	font-weight: bold;
	font-size: 13px;
	padding: 0px;
	margin: 0px
}
.sidemenu-links li {
    line-height: 30px;
    list-style: outside none none;
    padding-left: 5px;
}
.sidemenu-sub-links li:hover{
    background: #ebcf6e none repeat scroll 0 0;
}
.sidemenu-sub-links li {
	line-height: 22px
}
.sidemenu-make {
	color: #232f3e;
	font-weight: normal;
}
#footer-region {
	background: url(images/bg_bottom.jpg);
	height: 100px
}
#footer-holder {

	margin: 0px auto;
}
.footer {
  background: #f1f1f2 none repeat scroll 0 0;
  border-top: 2px solid #d9d9d9;
  line-height: 25px;
  margin-top: 10px;
  padding-top: 12px;
  text-align: center;
}
.footerlink {
    color: #232f3e;
    float: left;
    font-size: 11px;
    font-weight: bold;
    margin-right: 10px;
	 margin-top: 4px;
}
.footerlink:hover {
	text-decoration: none
}
.yearname{
	overflow: hidden;
    text-overflow: ellipsis;
}
.cart-con {
    border: 0 none;
    border-radius: 4px;
    display: inline-block;
    float: right;
    list-style: outside none none;
    margin: 0;
    padding: 0;
    position: relative;
    text-align: center;
    top: 13px;
    width: auto;
}
.cart-con > a > img {
    margin-right: 10px;
    width: 11%;
}
select#vehicle-make {
    height: 35px;
    margin: 0px 5px;
    font-size: 16px;
	font-weight:bold;
	width:25%;
}
select#vehicle-year {
    height: 35px;
    margin: 0px 5px;
    font-size: 16px;
	font-weight:bold;
	width:25%;
}
select#vehicle-model {
    height: 35px;
    margin: 0px 5px;
    font-size: 16px;
	font-weight:bold;
	width:25%;
}
form#frm-vehicle-locator {
    background-color: ;
    padding: 20px;
	border-radius:5px;
}
#frm-vehicle-locator legend {
  border-bottom: 0 none !important;
  float: left;
  font-size: 14px;
  margin-bottom: 0 !important;
  margin-top: 7px;
  text-transform: uppercase;
  width: auto;
}
#vehicle-submit {
    margin-top: -5px;
}
#frm-vehicle-locator select {
    box-shadow: 0 0 1px 0 #444;
    font-size: 16px;
    font-weight: normal !important;
    height: auto !important;
    margin: 0 5px;
    padding: 5px 4px;
    width: 20%;
}
/*.desktop-view{
	display: block !important;
}
.mobile-view{
	display: none !important;
}*/
#contact {
    float: left;
    width: 100%;
    background: rgba(216, 0, 0, 1) none repeat scroll 0 0;
    border-radius: 2px;
}
#contact .form-group input{
	width: 93%;
	border-radius: 3px;
    padding: 2px 3px;
}
#contact .form-group textarea{
	width: 93%;
	border-radius: 3px;
    padding: 2px 3px;
}
#contact  .form-group {
    float: left;
    width: 100%;
}
.form-inner {
    width: 100%;
}
#demo-b{
	display: none;
}
#demo-b input[type="search"] {
    color: transparent;
    cursor: pointer;
    padding-left: 27px;
    width: 18px;
	float: right;
}
#demo-b input[type=search]:focus {
    width: 130px;
    padding-left: 32px;
    color: #000;
    background-color: #fff;
    cursor: auto;
}
input[type=search]:focus {
    width: 130px;
    background-color: #fff;
    border-color: #6dcff6;
    -webkit-box-shadow: 0 0 5px rgba(109,207,246,.5);
    -moz-box-shadow: 0 0 5px rgba(109,207,246,.5);
    box-shadow: 0 0 5px rgba(109,207,246,.5);
}
input[type=search] {
    background: #ededed url(http://webdesignerwall.com/demo/expandable-search-form/images/search-icon.png) no-repeat 9px center;
    border: solid 1px #ccc;
    padding: 9px 10px 9px 32px;
    width: 55px;
    -webkit-border-radius: 10em;
    -moz-border-radius: 10em;
    border-radius: 10em;
    -webkit-transition: all .5s;
    -moz-transition: all .5s;
    transition: all .5s;
	background-position: right center !important;
}
.select2-search--dropdown .select2-search__field{
	width:100% !important;
}
.inner-pages .logo{
	float: left;
	display: none;
	width: 30%;
}
.inner-pages .logo img{
	width: 119px;
}
.navbar-header{
	width: 100%;
}
#header-area .nav i{
	display: none;
}
#header-area .nav i {
  background: rgba(216, 0, 0, 1) none repeat scroll 0 0;
  border-radius: 100%;
  color: #fff;
  height: 22px;
  margin-right: 6px;
  padding: 4px 0 0;
  text-align: center;
  width: 22px;
}
.tab-all-mobile{
	display: none;
}
.tab-all-desktop{
	display: block !important;
}
.tab-all-mobile {
  background: #232f3e;
  float: left;
  margin: 0 auto;
  width: 100%;
}
.tab-all-mobile .active a {
	background: #232f3e !important;
	border-radius: 0;
	color: #fff !important;
	font-weight: bold;
	border: 0 !important;
	margin:0;
}
.tab-all-mobile a {
  color: #ebcf6e;
  font-weight: bold;
  border: 0 !important;
  font-size: 12px;
  padding: 10px 9px !important;
}
.tab-all-mobile .nav-tabs li{
	width: 100%;
	text-align: center;
}
.tab-all-mobile .nav > li > a:focus, .tab-all-mobile .nav > li > a:hover {
  background-color: transparent;
  text-decoration: none;
}
.tab-all-mobile .nav.nav-tabs {
  border: 0;
}

/*-------------------------------------New---style-----CSS-------------------------------------*/

.mobile-view{
	display: none;
}
.home-page{
    background-color: #fff !important;
    background-size: cover;
	font-family: Open Sans;
}

.section1-content, .section2-content, .section3-content, .section4-content{
	float: left;
	 width: 100%;
}
#dynamic-content-region content .section1-content h1 {
    color: #fff;
    font-size: 37px;
    font-weight: bold;
    line-height: 55px;
    padding-bottom: 0;
    padding-top: 25px;
    text-align: center;
    text-shadow: 0 0 10px #000;
}
content .section1-content p {
    font-size: 20px;
    margin-bottom: 15px;
    margin-top: 5px;
	color: #fff;
    text-align: center;
    text-shadow: 0 0 10px #000;
	font-weight: 400;
    line-height: 1.3em;
}
content .section2-content {
    background-color: rgba(0, 0, 0, 0.55);
	padding: 24px;
}
content .section2-content h3{
    color: #fff;
    font-size: 18px;
	font-weight: bold;
}
content .section2-content #frm-vehicle-locator select:disabled {
    background-color: #eee;
    border-color: rgba(207, 207, 207, 0.5);
    opacity: 0.65;
}
content .section2-content #frm-vehicle-locator select option {
    font-weight: normal;
    text-transform: none;
}
content .section2-content #frm-vehicle-locator select:focus {
    border-color: #9bba59;
}
.section3-content a {
    color: orange;
}
content .section2-content #frm-vehicle-locator select {
    -moz-appearance: none;
    background: #fff url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAUCAMAAACzvE1FAAAADFBMVEUzMzMzMzMzMzMzMzMKAG/3AAAAA3RSTlMAf4C/aSLHAAAAPElEQVR42q3NMQ4AIAgEQTn//2cLdRKppSGzBYwzVXvznNWs8C58CiussPJj8h6NwgorrKRdTvuV9v16Afn0AYFOB7aYAAAAAElFTkSuQmCC") no-repeat scroll right 0.75rem center / 8px 10px;
	border: 3px solid #9bba59;
    border-radius: 3px;
    box-shadow: none;
    color: #55595c;
    font-size: 14px;
	text-transform: uppercase;
	font-weight: bold !important;
    height: auto;
    line-height: 1.5;
    margin: 5px 0;
    max-width: 100%;
    padding: 10px 3px;
    vertical-align: middle;
    width: 100%;
	-webkit-appearance: none;
	-moz-appearance:    none;
	appearance:         none;
}
content .section2-content select.focused {
    border: 3px solid #9bba59;
    border-radius: 4px;
}
content .section2-content select:not(.focused) {
    border: 3px solid rgba(207, 207, 207, 0.5);
}
content .section3-content{
    background-color: rgba(35, 47, 62, 0.9);
	padding: 24px;
	margin: 50px 0;
	text-align: center;
}
content .section4-content {
    background-color: rgba(0, 0, 0, 0.55);
	padding: 24px;
	/*margin-bottom: 40px;*/
	text-align: center;
	/*margin-top: 40px;*/
}
content .section3-content  p{
    margin: 12px;
	color: #fff;
	font-size: 17px;
}
content .section4-content h2{
	font-size: 26px;
	font-weight: 400;
	color: #fff;
	text-transform: uppercase;
}
content .section4-content .t-title {
    color: #fff;
    font-size: 40px;
	margin-top: 20px;
}
header.header-17 {
    background: #fff none repeat scroll 0 0;
    box-shadow: 0 1px 2px #000;
    margin-bottom: 10px;
    padding: 0 !important;
}
content .section4-content .search-title-light {
	font-weight: lighter;
    margin-top: 18px;
	color: #fff;
}
#top-header .list-inline > li{
	line-height:90px;
}
#widget-customer-location {
    color: #fff;
    line-height: 90px;
}
#widget-customer-location input {
    color: #000;
    line-height: 27px;
}

.footer-copyright {
    float: left;
    width: 100%;
	padding: 20px 0;
}
.footer-copyright .copyrights {
    color: #fff;
    margin: 0;
    padding-top: 8px;
}
.footer-copyright .follow-us {
    float: right;
}
.footer-copyright .follow-us li {
    float: left;
    list-style: outside none none;
    padding: 11px;
}
.footer-copyright .follow-us a {
    color: #fff;
    font-size: 16px;
}
footer .p-t-3, footer .p-y-3 {
    padding-top: 24px !important;
}
footer .p-t-1, footer .p-y-1 {
    padding-top: 8px !important;
}
footer.p-l-3, footer.p-x-3 {
    padding-left: 24px !important;
}
footer .p-b-2, footer .p-y-2 {
    padding-bottom: 12px !important;
}
footer .nav-footer-title {
    color: #e9edf2;
    font-size: 20px;
    text-transform: uppercase;
}
footer .nav-footer-title-underline {
    border-bottom: 1px solid #979797;
    padding-bottom: 5px;
	color: #e9edf2;
    font-size: 14px;
    text-transform: uppercase;
}
footer .list-inline, footer .list-unstyled {
    list-style: outside none none;
    padding-left: 0;
}
footer .p-b-1, .p-y-1 {
    padding-bottom: 8px !important;
}
footer a:link, footer a:visited {
    color: #e9edf2;
}
footer a:link {
    font-size: 13px;
    font-weight: 300;
    text-decoration: none;
}
footer .s-txt-footer-gray-light {
    color: #e9edf2;
}
.navbar-header .dropdown > button:hover {
    color: orange;
}
.navbar-header .dropdown > button {
    background: transparent none repeat scroll 0 0;
    border: 0 none;
    color: #fff;
    font-size: 15px;
    font-weight: bold;
}
.navbar-header .dropdown, .navbar-header .dropup {
    float: left;
}
.tab-content.liquid-container .tab-pane button:first-child::after {
    content: " ";
}
.navbar-header .dropdown .caret {
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 9px dashed;
    margin-left: 0;
	color: #92979c;
}
header .dropdown > button i {
    background-color: black;
}
.page-header.catalog-title {
    display: none;
}
.navbar-header .cart-con b {
    font-size: 20px;
    font-weight: bold;
    position: absolute;
    right: 34px;
    top: -10px;
	color: #444;
}
.navbar-header .icon-shopping-cart {
    color: #fff;
    font-size: 30px;
    position: absolute;
    right: 23px;
    top: 0px;
}
.copyrights > a {
		color: orange;
	}
		.foot-logo > img {
    width: 83%;
}
.part-page .toolbar .btn.btn-default {
    font-size: 10px !important;
}
.part-page .toolbar .btn-group.widget-sorting .dropdown-menu-right {
    right: -40px;
}
.part-page .toolbar .btn-group.widget-sorting .dropdown-menu-right a {
    text-transform: none;
}
.alert.alert-info {
    display: none !important;
}
#dynamic-content-region .page-header {
    padding: 0 !important;
    border: 0 !important;
}
.item-company-address p.mobile-mail, .item-company-address .mobile-phone{
    word-break: break-all;
}
.item-company-address p b{
    font-size: 11px;
    font-weight: 700;
}
#yearcolumn .col-xs-3.col-sm-2, #modelcolumn .col-xs-6.col-sm-4.col-md-3 {
    border: 1px solid #ddd;
    margin: -1px 0 0;
    text-align: left;
    width: 100%;
    padding: 0;
}
#yearcolumn .col-xs-3.col-sm-2 a, #modelcolumn .col-xs-6.col-sm-4.col-md-3 a {
    float: left;
    padding: 12px 10px;
    width: 100%;
}
#yearcolumn .col-xs-3.col-sm-2 a, #yearcolumn .col-xs-3.col-sm-2 a h4, #modelcolumn .col-xs-6.col-sm-4.col-md-3 h4, #modelcolumn .col-xs-6.col-sm-4.col-md-3 a {
    font-size: 13px;
    font-weight: 600;
    text-align: left;
    margin: 0;
}
#modelcolumn .col-xs-6.col-sm-4.col-md-3 img {
    display: none;
}
#yearcolumn .col-xs-3.col-sm-2 a::after, #modelcolumn .col-xs-6.col-sm-4.col-md-3 a::after {
    content: "\f105";
    float: right;
    font-family: FontAwesome;
}
#yearcolumn .col-xs-3.col-sm-2 a h4, #modelcolumn .col-xs-6.col-sm-4.col-md-3 a h4 {
    float: left;
}
#yearcolumn .col-xs-3.col-sm-2 a:hover, #modelcolumn .col-xs-6.col-sm-4.col-md-3 a:hover {
    background-color: #000;
    color: #fff;
}
#cstmtype {
    background: #eee none repeat scroll 0 0;
    border: 1px solid #ccc;
    border-radius: 4px;
    float: left;
    width: 100%;
}
#cstmtype .col-xs-10, #cstmtype .col-xs-2 {
    padding: 0;
}
#cstmtype .col-xs-10 input {
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
    padding: 14px 4px;
    width: 100%;
}
#cstmtype .col-xs-2 .icon-search.gobtn {
    cursor: pointer;
    line-height: 47px;
    padding: 18px;
    text-align: center;
}
.ui-menu.ui-widget.ui-widget-content.ui-autocomplete.ui-front {
    height: 200px !important;
    overflow-x: hidden !important;
    overflow-y: scroll !important;
    width: 288px !important;
}
.top-div-1 h1 {
		font-size: 20px !important;
	}
	.top-div-1 h1 b, .top-div-1 h1 b font{
		display:none !important;
	}
	.top-div-1 h4 {
		font-size: 14px !important;
		margin: 24px 0 !important;
	}
    .firstmain {
        padding-top: 10px;
        float: left;
        width: 100%;
    }
    .mainrow {
        float: left;
        padding-bottom: 10px;
        width: 100%;
    }
    .mainrow .col-xs-4 .input-group-btn {
        float: left;
        width: 23px;
    }
    .mainrow .col-xs-4 .input-group-btn {
        float: left;
    }
    .mainrow .col-xs-4 button {
        height: 29px !important;
        width: 23px !important;
    }
    .mainrow .col-xs-4 input {
        float: left;
        height: 29px !important;
        width: 20px !important;
    }
    .firstmain .col-xs-8 {
        overflow: hidden !important;
    }
    .firstmain .col-xs-4, .firstmain .col-xs-8 {
        padding: 0 6px;
    }
    .mainrow .col-xs-4 input .mainrow .col-xs-3 input, .mainrow .col-xs-5 input, .mainrow .col-xs-4 button, .mainrow .col-xs-3 button, .mainrow .col-xs-5 button {
        width: 100%;
    }
    .mainrow .col-xs-4, .mainrow .col-xs-3, .mainrow .col-xs-5 {
        padding: 0 5px;
    }
    .table.table-bordered.cart-table td {
        border-bottom: 1px solid #eee !important;
        border-top: 1px solid #eee !important;
        border-left: 0 !important;
        border-right: 0 !important;
    }
    .shopping-cart .unit-price span {
        color: #da291c;
        display: block;
        font-size: 13px;
        font-weight: bold;
        padding-top: 6px;
    }
    .shopping-cart .stock strong {
        color: green;
        font-size: 17px;
        font-weight: bold;
    }
    .shopping-cart .stock strong {
        color: green;
        font-size: 13px;
    }
    #facet-category-parttype-list #accordion .panel-heading a:focus, #facet-category-parttype-list #accordion .panel-heading a:focus::after {
        background: #000 none repeat scroll 0 0;
        color: #fff;
    }
    #facet-category-parttype-list #accordion .panel-body {
        background: #eee none repeat scroll 0 0;
        padding: 0;
    }
    #facet-category-parttype-list #accordion .panel-body .list-group-item::after{
        content: none !important;
    }
    #facet-category-parttype-list #accordion .panel-body a {
        background: transparent none repeat scroll 0 0;
        border: 0 none;
        color: #000;
        text-transform: none !important;
    }
    #accordion .list-group-item::after {
        transform: rotate(90deg);
    }
    #facet-category-parttype-list #accordion {
        display: block;
        position: static;
         margin: 0;
    }
    #facet-category-parttype-list #accordion .panel-heading a {
        color: #000;
    }
    #facet-category-parttype-list #accordion .panel-heading {
        border: 0 none;
        box-shadow: none;
        padding: 0;
    }
    #facet-category-parttype-list #accordion .panel-heading a {
        border: 0 none;
        color: #000;
    }
    #parts-catalog #filter-box {
        /* display: none; */
    }
	 #parts-catalog .select2.select2-container{
		 width:100% !important;
	 }
    #parts-catalog .part-page .list-group .list-group-item.active a {
        background: #eee none repeat scroll 0 0;
        text-transform: uppercase !important;
    }

    #facet-category-parttype-list .list-group {
        height: auto !important;
        margin-bottom: 0 !important;
        margin-top: 0; !important;
        padding: 0; !important;
    }
    #facet-category-parttype-list .list-group-item.active {
        background: transparent none repeat scroll 0 0 !important;
        margin: 0 !important;
        padding: 0 !important;
        border: 0 none !important;
    }
	#facet-category-parttype-list .list-group div {
		max-height: 100% !important;
        float: left;
        width: 100%;
	}
	#parttypes-with-facets .col-sm-12.text-right {
		float: left !important;
		margin: 0;
		padding: 0;
		width: 100% !important;
	}
	#order-totals .table .btn-info {
		font-size: 8px;
	}
	#order-totals .table td {
		float: left;
		padding: 0;
		width: 37%;
	}
	#order-totals .table {
		display: block;
		margin-bottom: 5px;
		overflow: scroll;
	}
	.summary-ordertotal {
		font-size: 15px;
	}
	#orderitems table {
		display: block;
		font-size: 8px;
		overflow: scroll;
	}
	.brand {
        font-size: 12px;
        color: #000;
        position: relative;
        top: 12px;
	}
	#parttypes-with-facets .col-sm-12.text-right a.btn.btn-primary {
		border-radius: 0;
		padding: 12px 10px !important;
		text-align: center !important;
	}
	.list-group {
		margin: 20px -15px;
		padding: 0;
	}
    .part-page .list-group .list-group-item.active, .part-page .list-group .list-group-item {
        color: black;
        font-size: 12.5px !important;
        padding: 13px 12px !important;
        text-transform: none !important;
        background-image: none !important;
        border-radius: 0 !important;
        font-weight: bold !important;
        text-transform: capitalize !important;
        font-family: verdana !important;
        border-width: 1px 1px 0;
        border-color: #000;
    }

	.part-page .list-group .list-group-item::after {
		content: "\f105";
		float: right;
		font-family: FontAwesome;
	}
	.buy-panel.single-product .partsellprice.text-danger, .buy-panel.single-product .partstock {
		text-align: left !important;
	}
	.buy-panel.single-product .partsellprice.text-danger small, .buy-panel.single-product .partstock small {
		display: none;
	}
	.buy-panel.single-product .partsellprice.text-danger {
		margin-left: -9px !important;
		margin-top: 10px !important;
	}
	#parts-catalog .list-item-wrapper-left .part-title {
		font-size: 15px;
		font-weight: bold;
		text-align: left;
        width: 100%;
        margin: 0 !important;
	}
	.remove .btn.btn-mini.btn-xs.btn-danger::after {
		content: "Delete";
	}
	#parts-catalog .buy-panel-image .thumbnail {
		border: 0 none !important;
	}
	.remove  .btn.btn-mini.btn-xs.btn-success, .remove .btn.btn-mini.btn-xs.btn-danger {
		background: #eee none repeat scroll 0 0;
		border-color: #d3d7da;
		float: left;
		color: #444;
		margin-right: 10px;
		padding: 6px 10px !important;
	}
.remove .btn.btn-mini.btn-xs.btn-danger{margin-right:0;}
	.quantity input{
		border: 1px solid #ccc;
		border-radius: 3px;
		padding: 5px 3px !important;
		width: 100% !important;
		background: #eeee
	}
	.remove .btn.btn-mini.btn-xs.btn-danger i, .remove .btn.btn-mini.btn-xs.btn-success i {
		display: none;
	}
	.btn-group  #btnCheckout::after {
		content: "Proceed to Checkout";
		font-size: 15px;
	}
	.btn-group #btnCheckout {
		border-radius: 3px !important;
		float: left;
		margin-top: 10px;
		width: 100%;
	}
	.btn-group #btnCheckout i {
		display: none;
	}
	#btnContinueShopping1 {
		display: none;
	}
	#btnFreightEstimate, #btnEmptyCart, #btnContinueShopping, #btnBack, #sendcart {
		display: none;
	}
	.col-sm-12.text-center .pull-right {
		float: left !important;
		width: 100%;
	}
	#btnGotoCheckout {
		float: none;
		font-size: 0;
		width: 63%;
	}
	#btnGotoCheckout i {
		display: none;
	}
	#btnGotoCheckout::after {
		content: "Proceed to Checkout";
		font-size: 15px;
	}
	.remove .btn.btn-mini.btn-xs.btn-success::after {
		content: "Change";
	}
    .filter-breadcrumbs a {
        color: #fff !important;
		font-size: 9px; white-space: pre-wrap;text-align: left;
    }
	.step-header
	{
	padding-bottom: 20px !important;
    padding-top: 22px !important;
	}
	.shopping-cart .page-header{
		display:none;
	}
	.shopping-cart a.btn.btn-primary.proceed{
		display:none;
	}
	#parts-catalog .list-item-wrapper-left .part-title br{
		display: none;
	}
	.desk-view{
		display: none;
	}
	#parts-catalog .list-item-wrapper-right .form-group {
		display: none;
	}
	#parts-catalog .list-item-wrapper-right .buy-panel-image {
		/*margin-bottom: 21%;*/
	}
	#parts-catalog .part-title a {
		text-decoration: underline;
        padding-bottom: 6px !important;
        display: inline-block;
        line-height: 14px;

	}
	#parts-catalog .part-title input {
		display: none;
	}
	#parts-catalog .toolbar .col-sm-6.text-center-right .pagination.pagination-sm {
		margin: 0;
	}
	.muted.pull-left {
		font-size: 19px !important;
		line-height: 100% !important;
		padding: 0 !important;
	}
	.table.table-bordered {
		display: block;
		overflow: scroll;
		margin: 0;
	}
	.navbar-header .dropdown > button {
		font-size: 11px;
		margin-left: 10px;
	}
	.btn-group-vertical > .btn, .btn-group > .btn {
		font-size: 0;
	}
	.btn-group-vertical > .btn i, .btn-group > .btn i {
		font-size: 22px;
	}
	/*#parts-catalog .list-item-wrapper-right {
		float: left;
		padding-right: 3px;
		width: 33%;
	}
	#parts-catalog .list-item-wrapper-left {
		float: right;
		padding-left: 12px;
		width: 67%;
	}*/
	#parts-catalog .list-item-wrapper-right .buy-button {
		display: block;
		font-size: 8px;
        padding-left: 0;
        padding-right: 0;
        width: 100%;
	}
	#parts-catalog .list-item-wrapper-right .buy-button i {
		font-size: 12px;
		display: none;
	}
	#parts-catalog .list-item {
		border-color: #ccc !important;
		padding-bottom: 20px !important;
		padding-top: 20px !important;
	}
	#parts-catalog .list-item-wrapper-right .buy-panel span {
		display: none;
	}
	#parts-catalog .toolbar {
		display: block !important;
	}
	#parts-catalog .toolbar .col-sm-2.text-center-left {
		display: none;
	}
	#parts-catalog .toolbar .col-sm-4.hidden-xs, #parts-catalog .toolbar .col-sm-6.text-center-right {
		display: block !important;
	}
	#parts-catalog .list-item-wrapper-right .buy-panel-image .product-image {
		font-size: 9px;
		text-align: center;
		text-transform: none;
	}
	#parts-catalog .list-item-wrapper-left .buy-panel li.price_rs {
		color: #b12704;
	}
	#parts-catalog .list-item-wrapper-left .buy-panel li.price_rs span {
		color: #585858;
	}
	#parts-catalog .list-item-wrapper-left .buy-panel h5 strong, #parts-catalog .list-item-wrapper-left .buy-panel h5 span {
		color: #585858;
		font-weight: normal;
	}
	#parts-catalog .list-item-wrapper-right .buy-button {
		display: block;
	}
	.rc-anchor-normal .rc-anchor-checkbox-label {
		text-align: center;
	}
	.rc-anchor-normal .rc-anchor-content {
		width: 86px;
	}
	.panel-login .col-xs-4 {
		padding: 0 3px !important;
	}
	.panel.panel-login .panel-heading a.active {
		font-size: 9px;
	}
	.panel.panel-login .panel-heading a {
		font-size: 9px;
	}
	.footer-copyright .copyrights {
		text-align: center;
	}
	.footer-copyright .follow-us {
		display: block;
		float: none;
		margin: 0 auto;
		width: 61%;
	}
	footer .footer-navigation h3 {
		margin-top: 20px;
		padding-left: 0;
		text-align: center;
	}
	footer .footer-navigation {
		text-align: center;
	}
	.home-page .container {
	  padding: 0 !important;
	}
	.home-page {
		background-image: none !important;
	}
	.banner h1 {
		color: #fff;
		display: block !important;
		font-size: 16px !important;
		font-weight: 700;
		padding: 8px 0;
		text-align: center;
		text-shadow: 0 0 10px #000;
	}
	.span10.col-md-10.col-sm-9.col-xs-12.right-all {
		padding: 0 !important;
	}
	#dynamic-content-region content .section1-content h1, content .section1-content p {
		display: none !important;
	}
	#dynamic-content-region{
		margin:  0 !important;
	}
	.row-fluid.row{
		margin: 0 !important;
	}
	.banner p {
		font-size: 12px;
		font-weight: 400;
		line-height: 1.3em;
		color: #fff;
		text-align: center;
	}
	.mobile-view{
		display: block !important;
	}
	.mobile-title {
		color: #555;
		font-size: 20px;
		font-weight: normal;
		margin-bottom: 20px;
		margin-top: 0px;
	}
	.liquid-container select {
		border: 1px solid #eee !important;
		box-shadow: none !important;
		padding: 11px 0 !important;
	}
	.liquid-container .liquid-child {
		overflow: hidden;
		position: absolute;
		top: 10px;
		transform: translateX(-2000px);
		transition: all 0.001s ease 0s;
	}
	.liquid-container .active.liquid-child {
		left: 0;
		position: relative;
		transform: translateX(0px);
		transition: all 0.3s ease 0s;
		z-index: 100000000;
		width: 100%;
	}
	.desktop-view {
		display: none !important;
	}
	.liquid-container {
		overflow: hidden;
		position: relative;
		z-index:9;
	}
	.multi-step.text-top li {
		text-align: center;
		width: 56px;
		pointer-events: none !important;

	}
	.multi-step.text-top li.visited strong {
		pointer-events: auto !important;
	}
	.multi-step li {
		-moz-box-flex: 1;
		background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
		border: medium none;
		display: block;
		flex: 1 1 0;
		margin: 0.4em 0;
		position: relative;
	}
	a .active, a:active, a:focus, a:hover {
		color: #2d5b8c;
		text-decoration: underline;
	}
	.multi-step {
		-moz-box-pack: center;
		justify-content: center;
		padding: 0;
	}
	.multi-step a.current > *, .multi-step a > * {
		color: #2c3f4c;
		display: inline-block;
		position: relative;
	}
	.multi-step a a, .multi-step a strong {
		padding-bottom: 28px;
	}
	.multi-step a.current > *, .multi-step a > * {
		color: #2c3f4c;
		display: inline-block;
		position: relative;
	}
	.h2, .radio label, a, h2, label, select, strong {
		font-weight: 400;
	}
	.multi-step a a, .multi-step a strong {
		padding-bottom: 28px;
	}
	.modal-image-flex, .multi-step {
		display: flex;
	}
	.multi-step.text-top li::after {
		bottom: 8px;
	}
	.multi-step.text-top li::after {
		left: 50%;
		position: absolute;
		width: 100%;
	}
	.multi-step li.visited a::after {
		background-color: #356ca6;
	}
	.multi-step li::after {
		background: #edeff0 none repeat scroll 0 0;
		content: "";
		display: block;
		height: 4px;
		margin: 0;
		position: absolute;
	}
	.multi-step.text-top li.active a> *::before {
		color: #fff;
		content: "\f00c" !important;
		font-family: FontAwesome;
		font-size: 12px;
		padding-left: 1px;
		padding-top: 3px;
	}
	.multi-step.text-top li.visited a> *::before {
		color: #fff;
		content: "\f040";
		font-family: FontAwesome;
		font-size: 12px;
		padding-left: 1px;
		padding-top: 3px;
	}

	.multi-step.text-top a > *::before {
		bottom: 0;
	}
	.multi-step.text-top li a > *::before {
		background-color: #edeff0;
		border-radius: 50%;
		content: "";
		display: block;
		height: 24px;
		left: 50%;
		position: absolute;
		right: auto;
		transform: translateX(-50%);
		width: 24px;
		z-index: 1;
	}
	.multi-step.text-top a:hover::before {
		box-shadow: 0 0 0 3px #468dd8;
	}
	.multi-step li:last-of-type::after {
		display: none;
	}

	.tab-content.liquid-container .tab-pane button {
		background: #fff none repeat scroll 0 0;
		border-color: #ddd #ddd -moz-use-text-color;
		border-style: solid solid none;
		border-width: 1px 1px 0;
		font-family: verdana;
		font-size: 14px;
		font-weight: bold;
		padding: 14px 12px;
		text-align: left;
		width: 100%;
	}
	.tab-content.liquid-container .tab-pane button::after {
		content: "\f105";
		float: right;
		font-family: FontAwesome;
	}
	.tab-content.liquid-container .tab-pane button:hover {
		color: #fff;
	}
	.tab-content.liquid-container .tab-pane button i {
		float: right;
		font-size: 20px;
		font-weight: bold;
	}
    .desktop{
        display: none;
    }
    .mobile{
        display:block;
    }
    .mobile-view{
    display: block !important;
  }
  .multi-step.text-top li {
    text-align: center;
    width: 56px;
    pointer-events: none !important;

  }
  .multi-step.text-top li.visited strong {
    pointer-events: auto !important;
  }
  .multi-step li {
    -moz-box-flex: 1;
    background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
    border: medium none;
    display: block;
    flex: 1 1 0;
    margin: 0.4em 0;
    position: relative;
  }
  a .active, a:active, a:focus, a:hover {
    color: #2d5b8c;
    text-decoration: underline;
  }
  .multi-step {
    -moz-box-pack: center;
    justify-content: center;
    padding: 0;
  }
  .multi-step a.current > *, .multi-step a > * {
    color: #2c3f4c;
    display: inline-block;
    position: relative;
  }
  .multi-step a a, .multi-step a strong {
    padding-bottom: 28px;
  }
  .multi-step a.current > *, .multi-step a > * {
    color: #2c3f4c;
    display: inline-block;
    position: relative;
  }
  .h2, .radio label, a, h2, label, select, strong {
    font-weight: 400;
  }
  .multi-step a a, .multi-step a strong {
    padding-bottom: 28px;
  }
  .modal-image-flex, .multi-step {
    display: flex;
  }
  .multi-step.text-top li::after {
    bottom: 8px;
  }
  .multi-step.text-top li::after {
    left: 50%;
    position: absolute;
    width: 100%;
  }
  .multi-step li.visited a::after {
    background-color: #356ca6;
  }
  .multi-step li::after {
    background: #edeff0 none repeat scroll 0 0;
    content: "";
    display: block;
    height: 4px;
    margin: 0;
    position: absolute;
  }
  .multi-step.text-top li.active a> *::before {
    color: #fff;
    content: "\f00c" !important;
    font-family: FontAwesome;
    font-size: 12px;
    padding-left: 1px;
    padding-top: 3px;
  }
  .multi-step.text-top li.visited a> *::before {
    color: #fff;
    content: "\f040";
    font-family: FontAwesome;
    font-size: 12px;
    padding-left: 1px;
    padding-top: 3px;
  }

  .multi-step.text-top a > *::before {
    bottom: 0;
  }
  .multi-step.text-top li a > *::before {
    background-color: #edeff0;
    border-radius: 50%;
    content: "";
    display: block;
    height: 24px;
    left: 50%;
    position: absolute;
    right: auto;
    transform: translateX(-50%);
    width: 24px;
    z-index: 1;
  }
    #main-content-wrap{
        margin: 0 !important;
    }
  .multi-step.text-top a:hover::before {
    box-shadow: 0 0 0 3px #468dd8;
  }
  .multi-step li:last-of-type::after {
    display: none;
  }
  .multi-step.text-top li.visited > a *::before, .multi-step.text-top li.active > a *::before {
    background-color: black;
}
  .tab-content.liquid-container .tab-pane button {
    background: #fff none repeat scroll 0 0;
    border-color: #ddd #ddd -moz-use-text-color;
    border-style: solid solid none;
    border-width: 1px 1px 0;
    font-family: verdana;
    font-size: 14px;
    font-weight: bold;
    padding: 14px 12px;
    text-align: left;
    width: 100%;
  }
  .tab-content.liquid-container .tab-pane button::after {
    content: "\f105";
    float: right;
    font-family: FontAwesome;
  }
  .tab-content.liquid-container .tab-pane button:hover {
    color: #111;
  }
.mobile-view .tab-content.liquid-container .tab-pane button:hover {
    color: #444;
  }
  .tab-content.liquid-container .tab-pane button i {
    float: right;
    font-size: 20px;
    font-weight: bold;
  }
    .cart-con.mobile-view.icon-all-cart > a {
		font-size: 17px;
	}
	.navbar-header form#frm-catalog-search {
		padding: 10px 0;
	}
	.navbar-header .btn-primary {
		background-color: #454d57;
		border-color: #444;
		color: #fff;
	}
	.navbar-header .btn-primary:hover {
		background-color: #71777f;
	}
	.navbar-header form#frm-catalog-search .form-group {
		margin-bottom: 0;
	}
	.icon-shopping-cart {
		padding-right: 4px;
		text-decoration: none;
	}
	.tab-all-mobile-main {
		/*background: rgba(0, 0, 0, 0) url("http://cdn26.us2.fansshare.com/photo/background/cool-black-background-hd-wallpapers-background-945673229.jpg") repeat scroll 0 0 / cover ;*/
		float: left;
		padding: 26px 0;
		width: 100%;
	}
#header-area .container {
		padding: 0;
	}
	form#frm-vehicle-locator {
		border-radius: 0;
	}
	.tab-all-mobile{
		display: block !important;
	}
	/*.well, .centeror{
		display: none!important;
	}*/
	.tab-all-desktop{
		display: none!important;
	}
	#frm-vehicle-locator legend {
		width: 100%;
	}
	#header-area  .navbar-header {
	}
	.navbar-toggle {
		margin-left:10px;
	}
	.inner-pages .logo {
		width: 177px!important;
	}
	.button-all {
		float: left;
	}
	.accordion-inner {
		float: right;
		width: 184px;
	}
	.navbar-default .navbar-toggle .icon-bar {
		background-color: #113725 !important;
	}
    .header-17.index.sticky .navbar-toggle {
        background: #fff;
    }
	.navbar-default .navbar-toggle {
		border-color: #000 !important;
	}
	.navbar-default .navbar-toggle:focus, .navbar-default .navbar-toggle:hover {
		background-color: transparent!important;
	}
	#header-area .nav li a {
	  text-transform: uppercase;
	}
	#header-area .nav  li {
		border-bottom: 1px solid #ccc;
	}
	#header-area .nav i{
		display: inline-block !important;
	}
	.inner-pages .logo img {
	}
	#order-totals .table, #order-totals .table tbody, #order-totals .table tr{
		display: block;
	}
	#order-totals .table .btn-info {
		font-size: 11px;
	}
	#order-totals .table #order-totals .table strong, #order-totals .table  span {
		font-size: 18px;
	}
	#order-totals .table th, #order-totals .table td {
		border-top-width: 0;
		font-size: 12px;
	}
	.inner-pages .cart-con.icon-all-cart {
		border: 0 none;
		border-radius: 0;
		margin: 10px 0;
		padding: 3px 0;
		width: 56px;
	}
	.inner-pages  .cart-con.icon-all-cart a img {
		margin-right: 10px;
		width: 36%;
	}
	.inner-pages  .navbar-header {
		width: 100%;
		margin: 0 !important;
		padding: 0 !important;
	}
	.inner-pages  .navbar {
		margin-bottom: 5px !important;
	}
	.inner-pages #yearcolumn .col-xs-3.col-sm-2 {
		width: 50%;
	}
	.inner-pages #yearcolumn .badge {
		font-size: 10px;
		padding: 4px;
	}
	.inner-pages #yearcolumn .yearname {
		font-size: 12px;
		margin: ;
	}
	div#bs-example-navbar-collapse-1 {
		width: 100%;
	}
	.inner-pages .logo{
		display: block !important;
	}
	#demo-b{
		display: block !important;
	}
	.inner-pages #top-page-region {
		display: none !important;
	}
	/*#parts-table .text-muted.list-item-fitment-note {
		display: block !important;
	}
	#parts-table .list-item-wrapper-right .btn i {
		font-size: 13px;
	}
	#parts-table .list-item-wrapper-left small {
		display: none;
	}
	#parts-table .list-item-wrapper-right .btn {
		font-size: 0;
		margin-bottom: 2px;
		padding: 1px;
	}
	#parts-table .list-item-wrapper-right h3 {
		font-size: 11px !important;
		margin: 0;
	}
	#parts-table .list-item-wrapper-left h3 {
		font-size: 13px;
	}
	#parts-table .list-item-wrapper-right .form-group, .list-item-wrapper-right .ul {
		margin: 0;
	}
	#parts-table .list-item-wrapper-right select {
		height: auto;
		padding: 0;
	}
	#parts-table .list-item-wrapper-right {
		float: right;
		width: 34%;
	}
	#parts-table .list-item-wrapper-left {
		float: left;
		width: 59%;
	}
	#parts-table .list-item-fitment {
		display: none;
	}
	#parts-table .list-item {
		border-bottom: 3px solid #444;
		padding-bottom: 10px;
	}*/
	.btn-primary.disabled, .btn-primary.disabled.active, .btn-primary.disabled.focus, .btn-primary.disabled:active, .btn-primary.disabled:focus, .btn-primary.disabled:hover, .btn-primary[disabled], .btn-primary.active[disabled], .btn-primary.focus[disabled], .btn-primary[disabled]:active, .btn-primary[disabled]:focus, .btn-primary[disabled]:hover, fieldset[disabled] .btn-primary, fieldset[disabled] .btn-primary.active, fieldset[disabled] .btn-primary.focus, fieldset[disabled] .btn-primary:active, fieldset[disabled] .btn-primary:focus, fieldset[disabled] .btn-primary:hover {
		background-color: black;
		border-color: black;
	}
	.btn.disabled, .btn[disabled], fieldset[disabled] .btn {
		opacity: 1;
	}

	form#frm-vehicle-locator, #frm-catalog-search {
		padding: 0;
	}
	.googletranslate {
		display: none;
	}
	#dynamic-content-region  .page-header{
		margin: 0 !important;
	}
	.row.breadcrumbs-wdigets {
		display: none;
	}
	 #part-listing-keyword-search {
		display: none;
	}
	#parttypes-with-facets .btn-group.pull-right {
		float: left !important;
	}
	/*.navfilters.card.card-refine {
		display: none;
	}*/
	#main-content-area{
		margin: 0 !important;
	}
	.input-group-lg>.form-control, .input-group-lg>.input-group-addon, .input-group-lg>.input-group-btn>.btn {
		font-size: 9px;
	}
	.form-inner {
		width: 100%;
	}
	#contact  .form-group label{
		display: none;
	}
	.badge {
		font-size: 10px;
	}
	.h5, h5 {
		font-size: 11px;
	}
	.thumbnail{
		width: 100% !important;
	}
	.h4, h4 {
		font-size: 9px;
	}
	#yearcolumn .badge {
		font-size: 8px;
		padding: 3px;
	}
	img.sidebar-image {
		display: block;
		margin: 0 auto;
		width: auto;
	}
	.sidemenu-links {
		text-align: center;
	}
	.left-all {
		text-align:center;

	}
	img.sidebar-image {
	  bottom: 50px;
	}
	#frm-vehicle-locator select {
		background: #fff none repeat scroll 0 0;
		border: 0 none;
		border-radius: 3px;
		margin: 2px 0;
		padding: 5px 1px;
		width: 100%;
	}
	#frm-catalog-search .btn-primary {
		background-color: black;
		border-color: black;
	}
	#vehicle-submit {
		border-radius: 3px;
		margin: 2px 0 0;
		padding: 6px 0;
		width: 100%;
	}
	#dynamic-content-region {
		margin-left: 0px;
	}
    #intl-checkout-form-btnGetFreight, #intl-checkout-summary-detail_cart_link{white-space:pre-wrap;}
    #order-totals .table tr{clear:both;}
    #order-totals .table .btn-info{text-align: left;margin-left: 0;display: inline-block;width: 220px;}
    .noscroll{overflow:unset;}
    .list-item-wrapper-left .buy-panel li {
    font-size: 13px !important;
}
.list-item-wrapper-left, .list-item-wrapper-right {
    padding: 0 !important;
}
#parts-catalog .list-item {
    margin: 0 !important;
    border-bottom: 1px solid #ccc !important;
    border-left: 0 !important;
    border-right: 0 !important;
    border-top: 0 !important;
    border-radius: 0 !important;
}
.list-item .col-sm-3 {
    height: auto !important;
    text-align: center;
}
   .mobile button.bnn-filter.btn-danger {
    background: #d9534f !important;
    outline: none !important;
    cursor: pointer;
}
.mobile .mobile-filter #accordion .panel-heading .panel-title a {
    color: #000 !important;
}
  .mobile-filter #accordion {
    position: static;
    width: 100%;
}
.bnn-filter {
    border: 0 none;
    border-radius: 3px;
    color: #ffff;
    float: right;
    padding: 0 10px;
}
.mobile-filter #accordion .panel-heading {
    background: #fafafa !important;
    border-bottom: 1px solid #eee !important;
    padding-bottom: 10px;
    padding-top: 10px;
}
.mobile-filter #accordion .panel-heading h6.panel-title a {
    font-size: 12px !important;
    letter-spacing: 1px;
    color: #444 !important;
    font-weight: 500 !important;
}
.mobile-filter #accordion .panel.panel-default {
    border-color: #fafafa !important;
    border: 1px solid #eee !important;
    margin-left: 10px !important;
    margin-right: 10px !important;
    margin-bottom: 10px !important;
}
.mobile-filter #accordion .panel.panel-default .panel-body a {
    font-weight: normal;
}
#parts-table .collapse {
    display: block !important;
}

.mobile-filter #accordion .panel-body {
    padding-top: 0 !important;
}

#parts-table .checkbox, #parts-table .checkbox a {
    color: #000;
    font-weight: bold;
}

#parts-table .panel-collapse.collapse .checkbox input, #parts-table .panel-collapse.collapse .radio input {
    display: none !important;
}
}
@media screen and (max-width: 860px) {
  .flex-direction-nav .flex-prev {
    opacity: 1;
    left: 10px;
  }
  .flex-direction-nav .flex-next {
    opacity: 1;
    right: 10px;
  }
}

@media screen and (max-width: 680px) {
    .ui-widget-content, .ui-widget-content table{width: 100% !important;}
    .ui-widget-content table td input{width: 80%;margin-left:10px;}
    .ui-widget-content .dashboardtitle{font-size: 18px;white-space:pre-wrap;}
    .confirmation-section-subtitle, .section-subtitle{height: 30px;}
}

@media only screen and ( min-width:240px) and (max-width:360px) {
	.g-recaptcha iframe {
		width: 75%;
    }
}

/*
	Max width before this PARTICULAR table gets nasty. This query will take effect for any screen smaller than 760px and also iPads specifically.
	*/
	@media
	  only screen
    and (max-width: 760px), (min-device-width: 768px)
    and (max-device-width: 1024px)  {

h2{font-size:18px;}
		/* Force table to not be like tables anymore */
		.mobiletable table, .mobiletable thead, .mobiletable tbody, .mobiletable th, .mobiletable td, .mobiletable tr {
			display: block;
		}

		/* Hide table headers (but not display: none;, for accessibility) */
		.mobiletable thead tr {
			position: absolute;
			top: -9999px;
			left: -9999px;
		}

    .mobiletable tr {
      margin: 0 0 1rem 0;
    }

    .mobiletable tr:nth-child(odd) {
      background: #ccc;
    }

		.mobiletable td {
			/* Behave  like a "row" */
			border: none;
			border-bottom: 1px solid #eee;
			position: relative;
			padding-left: 50%;
text-align:left !important;
		}

		.mobiletable td:before {
			/* Now like a table header */
			position: absolute;
			/* Top/left values mimic padding */
			top: 0;
			left: 6px;
			width: 45%;
			padding-right: 10px;
			white-space: nowrap;
		}

		/*
		Label the data
    You could also use a data-* attribute and content for this. That way "bloats" the HTML, this way means you need to keep HTML and CSS in sync. Lea Verou has a clever way to handle with text-shadow.
		*/
		.mobiletable.tax-report td:nth-of-type(1):before { content: "Order Number"; }
		.mobiletable.tax-report td:nth-of-type(2):before { content: "Account"; }
		.mobiletable.tax-report td:nth-of-type(3):before { content: "Date"; }
		.mobiletable.tax-report td:nth-of-type(4):before { content: "Po Number"; }
		.mobiletable.tax-report td:nth-of-type(5):before { content: "Ship To State"; }
		.mobiletable.tax-report td:nth-of-type(6):before { content: "Status"; }
		.mobiletable.tax-report td:nth-of-type(7):before { content: "Invoice Total"; }
		.mobiletable.tax-report td:nth-of-type(8):before { content: "Paid"; }
		.mobiletable.tax-report td:nth-of-type(9):before { content: "Tax"; }

		.mobiletable.order_history{left: -7px;position: relative;}
		.mobiletable.order_history .btn{padding:0;padding-right:3px;background:#eee;border:solid 1px #ccc;display:block;}
		/*.mobiletable.order-history td:nth-of-type(1):before { content: "Action"; }
		.mobiletable.order-history td:nth-of-type(1):before { content: "Ship Status"; }
		.mobiletable.order-history td:nth-of-type(1):before { content: "Pay Status"; }
		.mobiletable.order-history td:nth-of-type(1):before { content: "Order#"; }
		.mobiletable.order-history td:nth-of-type(1):before { content: "Date"; }
		.mobiletable.order-history td:nth-of-type(1):before { content: "Shipping/Tax"; }
		.mobiletable.order-history td:nth-of-type(1):before { content: "Ship To"; }*/

		.mobiletable.return-history td:nth-of-type(1):before { content: "Action"; }
		.mobiletable.return-history td:nth-of-type(1):before { content: "Status"; }
		.mobiletable.return-history td:nth-of-type(1):before { content: "Order #"; }
		.mobiletable.return-history td:nth-of-type(1):before { content: "Amount"; }
		.mobiletable.return-history td:nth-of-type(1):before { content: "Return #"; }
		.mobiletable.return-history td:nth-of-type(1):before { content: "Supplier RMA #"; }
		.mobiletable.return-history td:nth-of-type(1):before { content: "Return Tracking #"; }
		.mobiletable.return-history td:nth-of-type(1):before { content: "Date"; }
		.mobiletable.return-history td:nth-of-type(1):before { content: "Return To"; }

		.mobiletable.refund-report td:nth-of-type(1):before { content: "Return Id"; }
		.mobiletable.refund-report td:nth-of-type(1):before { content: "Order Id"; }
		.mobiletable.refund-report td:nth-of-type(1):before { content: "Transaction Id"; }
		.mobiletable.refund-report td:nth-of-type(1):before { content: "Ship To"; }
		.mobiletable.refund-report td:nth-of-type(1):before { content: "Order Total"; }
		.mobiletable.refund-report td:nth-of-type(1):before { content: "Refunded"; }
		.mobiletable.refund-report td:nth-of-type(1):before { content: "Refund Amount"; }

		.mobiletable.shipping-report td:nth-of-type(1):before { content: "Action"; }
		.mobiletable.shipping-report td:nth-of-type(1):before { content: "Status"; }
		.mobiletable.shipping-report td:nth-of-type(1):before { content: "Order #"; }
		.mobiletable.shipping-report td:nth-of-type(1):before { content: "Date"; }
		.mobiletable.shipping-report td:nth-of-type(1):before { content: "Handling/Tax"; }
		.mobiletable.shipping-report td:nth-of-type(1):before { content: "Ship To"; }
		.mobiletable.shipping-report td:nth-of-type(1):before { content: "Bill To"; }
	}

.buy-panel-sell-price  {
    white-space: nowrap;
    font-size: 20px;
}
.product-compare-container {
    width: 70px;
    z-index: 102;
    position: fixed;
    top: 225px;
    right: 45px;
    padding: 5px;
    box-shadow: 0px 2px 15px 0px rgba(0,0,0,0.15);
    border: 1px solid #eeeeee;
    background-color: #fff;
    border-radius: 6px;
    text-align: center;
    display: none;
    margin-bottom: 10px;
}
.product-compare-container .product-comparison-list {
    list-style-type: none;
    padding-left: 0;
    max-width: 320px;
    width: 100%;
    display: -webkit-box;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    flex-direction: column;
    -webkit-box-align: center;
    align-items: center;
    gap: 10px;
    margin: 0 0 10px;
}

.remove-item {
    display: block;
    font-size: 12px;
    font-weight: 700;
    line-height: 16px;
    position: absolute;
    z-index: 1;
    right: 0;
    top: 0;
    width: 16px;
    height: 16px;
    text-align: center;
    color: #fff;
    border-radius: 3px;
    background: #ef2809;
    cursor: pointer;
    text-decoration: none;
}
.product-comparison-item {
    position: relative;
}
button.remove-item {
    cursor: pointer;
    border: 0;
    padding: 0;
    -webkit-appearance: none;
}
