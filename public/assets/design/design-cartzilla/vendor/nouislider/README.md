# noUiSlider

noUiSlider is a lightweight JavaScript range slider.

- **Accessible** with `aria` and keyboard support
- GPU animated: no reflows, so fast; even on older devices
- All modern browsers and IE > 9 are supported
- **No dependencies**
- Fully **responsive**
- **Multi-touch support** on Android, iOS and Windows devices
- Tons of [examples](https://refreshless.com/nouislider/examples) and answered [Stack Overflow questions](https://stackoverflow.com/questions/tagged/nouislider)

License
-------
noUiSlider is licensed [MIT](https://choosealicense.com/licenses/mit/).

It can be used **for free** and **without any attribution**, in any personal or commercial project.

[Documentation](https://refreshless.com/nouislider/)
-------
An extensive documentation, including **examples**, **options** and **configuration details**, is available here:

[noUiSlider documentation](https://refreshless.com/nouislider/).

Contributing
------------

See [Contributing](CONTRIBUTING.md).

Sponsorship
-----------

noUiSlider is a stable project that still receives a lot of feature requests. A lot of these are interesting, but require a good amount of effort to implement, test and document. Sponsorship of this project will allow me to spend some more of my time on these feature requests.

Please consider sponsoring the project by clicking the "❤ Sponsor" button above. Thanks!

Tooling
-------

Cross-browser testing kindly provided by BrowserStack.

[![Tested with BrowserStack](documentation/assets/browserstack-logo-380x90.png)](http://browserstack.com/)
