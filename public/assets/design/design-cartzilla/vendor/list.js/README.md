# List.js

Perfect library for adding **search**, **sort**, **filters** and **flexibility** to
**tables**, **lists** and various HTML elements. Built to be invisible and work on existing HTML.
Really simple and easy to use!

[![Donate](https://s3.amazonaws.com/listjs/donate-coffee.png)](https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=M7ZGHV75VSD2E)
[![npm version](https://badge.fury.io/js/list.js.svg)](https://badge.fury.io/js/list.js)
[![CircleCI](https://circleci.com/gh/javve/list.js/tree/master.svg?style=shield)](https://circleci.com/gh/javve/list.js/tree/master)
[![codecov](https://codecov.io/gh/javve/list.js/branch/master/graph/badge.svg)](https://codecov.io/gh/javve/list.js)
[![jsDelivr Hits](https://data.jsdelivr.com/v1/package/npm/list.js/badge?style=rounded)](https://www.jsdelivr.com/package/npm/list.js)

### Core idea

- Simple and invisible
- Easy to apply to existing HTML
- No dependencies
- Fast
- Small
- Handle thousands of items

### Features

- Works both lists, tables and almost anything else. E.g. `<div>`,`<ul>`,`<table>`, etc.
- Search [Read more ›](https://listjs.com/docs/list-api#search)
- Sort [Read more ›](https://listjs.com/docs/list-api#sort)
- Filter [Read more ›](https://listjs.com/docs/list-api#filter)
- Simple templating system that adds possibility to add, edit, remove items [Read more ›](https://listjs.com/docs/list-api#add)
- Support for Chrome, Safari, Firefox, IE9+

### Download / Install

##### Via NPM

```
npm install list.js
```

##### Via Bower

```
bower install list.js
```

##### Via CDNJS

```html
<script src="//cdnjs.cloudflare.com/ajax/libs/list.js/2.3.0/list.min.js"></script>
```

##### Via Direct Download

- [Compressed list.js](https://raw.githubusercontent.com/javve/list.js/v2.3.0/dist/list.min.js)
- [Uncompressed list.js](https://raw.githubusercontent.com/javve/list.js/v2.3.0/dist/list.js)

### Questions / How to?

https://stackoverflow.com/questions/tagged/list.js

### Demo / Examples

- [Existing list](https://listjs.com/examples/existing-list)
- [Existing list + add](https://listjs.com/examples/existing-list-add)
- [New list](https://listjs.com/examples/new-list)
- [Add, get, remove](https://listjs.com/examples/add-get-remove)
- [Fuzzy search](https://listjs.com/examples/fuzzy-search)
- [Pagination](https://listjs.com/examples/pagination)
- [Search in specific column](https://codepen.io/javve/pen/GpZpow)
- [Filter in range](https://codepen.io/javve/pen/wKGKWL)
- [Show message filter/search results in 0 items](https://codepen.io/javve/pen/VvavzG)
- [Only show list after search/filter](https://codepen.io/javve/pen/YyqyRg)

## Documentation

- [Getting started](https://listjs.com/docs)
- [Options](https://listjs.com/docs/options)
- [List API](https://listjs.com/docs/list-api)
- [Item API](https://listjs.com/docs/item-api)
- [Changelog](https://github.com/javve/list.js/blob/master/CHANGELOG.md)

### Thanks to [all lovely contributors](https://github.com/javve/list.js/graphs/contributors)! Want to join them?

- Read more at [listjs.com/overview/contribute](https://listjs.com/overview/contribute)

### Creator

|                                                                                          | Jonny Strömberg [@javve](https://twitter.com/javve)                                                                                                                                                                                                          |
| ---------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| ![Image of Jonny](http://1.gravatar.com/avatar/9f8130715cb4c452f1294eafa1b36290?size=80) | I hope you like the lib. I’ve put a lot of hours into it! Feel free to follow me on [Twitter](http://twitter.com/javve) for news and [donate a coffee](https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=M7ZGHV75VSD2E) for good karma ;) |

## License (MIT)

Copyright (c) 2011-2020 Jonny Strömberg <[<EMAIL>](mailto:<EMAIL>)>
[javve.com](https://javve.com)
