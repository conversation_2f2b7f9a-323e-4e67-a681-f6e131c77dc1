{"name": "list.js", "version": "2.3.1", "description": "The perfect library for lists. Supports search, sort, filters and flexibility. Built to be invisible and work on existing HTML", "keywords": ["list", "search", "sort", "table", "dom", "html", "ui"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://javve.com"}, "homepage": "https://listjs.com", "repository": "git://github.com/javve/list.js.git", "license": "MIT", "bugs": {"url": "https://github.com/javve/list.js/issues"}, "dependencies": {"string-natural-compare": "^2.0.2"}, "devDependencies": {"@babel/core": "^7.12.7", "@babel/preset-env": "^7.12.7", "babel-loader": "^8.2.1", "jest": "^26.6.3", "jquery": "^3.5.1", "prettier": "^2.2.0", "webpack": "^5.6.0", "webpack-cli": "^4.2.0"}, "main": "src/index", "engines": {"node": "^6.0 || ^8.0 || ^10.0 || ^12.0 || >=14"}, "scripts": {"test": "npx jest", "build": "npx webpack", "watch": "npx webpack --watch", "watch-test": "npx jest --watch", "preversion": "npm test && npm run build && cp dist/list.min.js docs/assets/javascripts/list.min.js && cp dist/list.min.js.map docs/assets/javascripts/list.min.js.map && git add dist && git add docs/assets/javascripts", "postversion": "git push --follow-tags origin master && cp package.json docs/_data/pkg.json && git add docs/_data/pkg.json && git commit -m \"pkg.json update\""}, "npmName": "list.js", "npmFileMap": [{"basePath": "/dist/", "files": ["*.js", "*.js.map"]}], "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/*.js", "src/utils/*.js"], "testURL": "http://localhost/"}}