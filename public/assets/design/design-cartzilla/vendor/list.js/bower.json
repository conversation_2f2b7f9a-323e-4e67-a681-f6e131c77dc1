{"name": "list.js", "main": "dist/list.js", "homepage": "https://listjs.com", "authors": ["<PERSON><PERSON> <<EMAIL>>"], "description": "The perfect library for lists. Supports search, sort, filters and flexibility. Built to be invisible and work on existing HTML", "keywords": ["list", "search", "sort", "table", "dom", "html", "ui"], "license": "MIT", "ignore": ["**/.*", "node_modules", "__test__", "docs"]}