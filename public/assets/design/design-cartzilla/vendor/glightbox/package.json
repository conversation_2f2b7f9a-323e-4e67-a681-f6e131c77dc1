{"name": "glightbox", "version": "3.3.0", "description": "Pure Javascript lightbox", "main": "dist/js/glightbox.min.js", "scripts": {"watch": "node development/watcher.js"}, "author": "Biati Digital", "types": "./index.d.ts", "keywords": ["lightbox", "javascript", "gallery", "popup"], "license": "MIT", "homepage": "https://biati-digital.github.io/glightbox/", "repository": {"url": "https://github.com/biati-digital/glightbox", "type": "git"}, "bugs": "https://github.com/biati-digital/glightbox/issues", "devDependencies": {"@babel/cli": "^7.10.3", "@babel/core": "^7.10.3", "@babel/node": "^7.10.3", "@babel/preset-env": "^7.10.3", "@babel/register": "^7.10.3", "archiver": "^3.1.1", "babel-plugin-transform-runtime": "^6.23.0", "chokidar": "^3.4.0", "clean-css": "^4.2.3", "css-mqpacker": "^7.0.0", "eslint": "^7.16.0", "fs-extra": "^8.1.0", "fs-jetpack": "^2.4.0", "install": "^0.13.0", "node-notifier": "^5.4.0", "npm": "^6.14.10", "postcss": "^7.0.32", "postcss-nested": "^4.1.1", "postcss-preset-env": "^6.5.0", "postcss-prettify": "^0.3.4", "rollup": "^1.32.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-commonjs": "^9.2.0", "rollup-plugin-node-resolve": "^4.0.0", "temporary": "^1.1.0", "terser": "^4.8.0", "uninstall": "0.0.0"}, "dependencies": {}}