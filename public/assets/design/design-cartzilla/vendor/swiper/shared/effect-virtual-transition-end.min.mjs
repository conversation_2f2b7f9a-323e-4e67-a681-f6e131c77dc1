import{k as elementTransitionEnd}from"./utils.min.mjs";function effectVirtualTransitionEnd(e){let{swiper:t,duration:n,transformElements:r,allSlides:i}=e;const{activeIndex:a}=t;if(t.params.virtualTranslate&&0!==n){let e,n=!1;e=i?r:r.filter((e=>{const n=e.classList.contains("swiper-slide-transform")?(e=>{if(!e.parentElement)return t.slides.filter((t=>t.shadowRoot&&t.shadowRoot===e.parentNode))[0];return e.parentElement})(e):e;return t.getSlideIndex(n)===a})),e.forEach((e=>{elementTransitionEnd(e,(()=>{if(n)return;if(!t||t.destroyed)return;n=!0,t.animating=!1;const e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0});t.wrapperEl.dispatchEvent(e)}))}))}}export{effectVirtualTransitionEnd as e};
//# sourceMappingURL=effect-virtual-transition-end.min.mjs.map