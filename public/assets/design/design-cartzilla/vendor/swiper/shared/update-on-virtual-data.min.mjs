import{e as extend,p as paramsList,i as isObject,n as needsNavigation,a as needsPagination,b as needsScrollbar}from"./update-swiper.min.mjs";import{d as defaults}from"./swiper-core.min.mjs";function getParams(a,e){void 0===a&&(a={}),void 0===e&&(e=!0);const s={on:{}},n={},t={};extend(s,defaults),s._emitClasses=!0,s.init=!1;const r={},i=paramsList.map((a=>a.replace(/_/,""))),l=Object.assign({},a);return Object.keys(l).forEach((l=>{void 0!==a[l]&&(i.indexOf(l)>=0?isObject(a[l])?(s[l]={},t[l]={},extend(s[l],a[l]),extend(t[l],a[l])):(s[l]=a[l],t[l]=a[l]):0===l.search(/on[A-Z]/)&&"function"==typeof a[l]?e?n[`${l[2].toLowerCase()}${l.substr(3)}`]=a[l]:s.on[`${l[2].toLowerCase()}${l.substr(3)}`]=a[l]:r[l]=a[l])})),["navigation","pagination","scrollbar"].forEach((a=>{!0===s[a]&&(s[a]={}),!1===s[a]&&delete s[a]})),{params:s,passedParams:t,rest:r,events:n}}function mountSwiper(a,e){let{el:s,nextEl:n,prevEl:t,paginationEl:r,scrollbarEl:i,swiper:l}=a;needsNavigation(e)&&n&&t&&(l.params.navigation.nextEl=n,l.originalParams.navigation.nextEl=n,l.params.navigation.prevEl=t,l.originalParams.navigation.prevEl=t),needsPagination(e)&&r&&(l.params.pagination.el=r,l.originalParams.pagination.el=r),needsScrollbar(e)&&i&&(l.params.scrollbar.el=i,l.originalParams.scrollbar.el=i),l.init(s)}function getChangedParams(a,e,s,n,t){const r=[];if(!e)return r;const i=a=>{r.indexOf(a)<0&&r.push(a)};if(s&&n){const a=n.map(t),e=s.map(t);a.join("")!==e.join("")&&i("children"),n.length!==s.length&&i("children")}return paramsList.filter((a=>"_"===a[0])).map((a=>a.replace(/_/,""))).forEach((s=>{if(s in a&&s in e)if(isObject(a[s])&&isObject(e[s])){const n=Object.keys(a[s]),t=Object.keys(e[s]);n.length!==t.length?i(s):(n.forEach((n=>{a[s][n]!==e[s][n]&&i(s)})),t.forEach((n=>{a[s][n]!==e[s][n]&&i(s)})))}else a[s]!==e[s]&&i(s)})),r}const updateOnVirtualData=a=>{!a||a.destroyed||!a.params.virtual||a.params.virtual&&!a.params.virtual.enabled||(a.updateSlides(),a.updateProgress(),a.updateSlidesClasses(),a.parallax&&a.params.parallax&&a.params.parallax.enabled&&a.parallax.setTranslate())};export{getChangedParams as a,getParams as g,mountSwiper as m,updateOnVirtualData as u};
//# sourceMappingURL=update-on-virtual-data.min.mjs.map