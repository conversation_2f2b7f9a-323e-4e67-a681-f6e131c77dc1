{"name": "timezz", "version": "7.0.0", "description": "TimezZ - with this plugin, you can easily make a stopwatch or timer on your site. Just init, style and enjoy.", "main": "dist/timezz.js", "types": "dist/timezz.d.ts", "module": "dist/timezz.js", "prepublish": "npm run build && npm run test", "scripts": {"dev": "webpack --env development --watch", "build": "webpack --env production", "test": "jest --env=jsdom"}, "repository": {"type": "git", "url": "git+https://github.com/BrooonS/timezz.git"}, "keywords": ["webpack", "js", "javascript", "library", "es6", "commonjs", "timezz", "plugin", "time", "timer", "stopwatch", "repeat", "typescript", "ui"], "author": "Valery Strelets", "license": "ISC", "bugs": {"url": "https://github.com/BrooonS/timezz/issues"}, "husky": {"hooks": {"pre-commit": "npm run build && npm run test"}}, "homepage": "https://github.com/BrooonS/timezz#readme", "devDependencies": {"@babel/core": "7.17.8", "@babel/preset-env": "7.16.11", "@types/jest": "27.4.1", "@typescript-eslint/eslint-plugin": "5.15.0", "@typescript-eslint/parser": "5.15.0", "babel-loader": "8.2.3", "eslint": "8.11.0", "eslint-config-airbnb-base": "15.0.0", "eslint-config-airbnb-typescript": "16.1.3", "eslint-plugin-import": "2.25.4", "eslint-plugin-jsx-a11y": "6.5.1", "eslint-webpack-plugin": "3.1.1", "husky": "7.0.4", "jest": "27.5.1", "ts-loader": "9.2.8", "typescript": "4.6.2", "webpack": "5.70.0", "webpack-cli": "4.9.2"}}