afmktMakes = new Array();afmktMakes["abarth"]= new Array("abarth","Abarth",[1969,19]);afmktMakes["ac"]= new Array("ac","AC",[1973,26]);afmktMakes["acura"]= new Array("acura","Acura",[2023,37]);afmktMakes["alfa-romeo"]= new Array("alfa-romeo","Alfa Romeo",[2023,8,7,0,13,55,2,16]);afmktMakes["allard"]= new Array("allard","Allard",[1954,8]);afmktMakes["allstate"]= new Array("allstate","Allstate",[1953,1]);afmktMakes["alpine"]= new Array("alpine","Alpine",[1974,11]);afmktMakes["alvis"]= new Array("alvis","<PERSON>vis",[1967,21]);afmktMakes["am-general"]= new Array("am-general","AM General",[2001,9,18,5]);afmktMakes["american-austin"]= new Array("american-austin","American Austin",[1935,5]);afmktMakes["american-bantam"]= new Array("american-bantam","American Bantam",[1941,5]);afmktMakes["american-motors"]= new Array("american-motors","American Motors",[1988,30]);afmktMakes["amphicar"]= new Array("amphicar","Amphicar",[1968,7]);afmktMakes["apollo"]= new Array("apollo","Apollo",[1965,3]);afmktMakes["apperson"]= new Array("apperson","Apperson",[1926,1]);afmktMakes["armstrong-sidde"]= new Array("armstrong-sidde","Armstrong-Sidde",[1960,15]);afmktMakes["arnolt-bristol"]= new Array("arnolt-bristol","Arnolt-Bristol",[1963,9]);afmktMakes["arnolt-mg"]= new Array("arnolt-mg","Arnolt-MG",[1954,1]);afmktMakes["aston-martin"]= new Array("aston-martin","Aston Martin",[2023,23,2,1,3,46]);afmktMakes["auburn"]= new Array("auburn","Auburn",[1936,32]);afmktMakes["audi"]= new Array("audi","Audi",[2023,54]);afmktMakes["austin"]= new Array("austin","Austin",[1975,28,6,0,3,8]);afmktMakes["austin-healey"]= new Array("austin-healey","Austin Healey",[1970,17]);afmktMakes["avanti"]= new Array("avanti","Avanti",[2007,6,10,4,2,20]);afmktMakes["bentley"]= new Array("bentley","Bentley",[2023,77,5,8,2,10]);afmktMakes["berkeley"]= new Array("berkeley","Berkeley",[1961,5]);afmktMakes["bertone"]= new Array("bertone","Bertone",[1989,5]);afmktMakes["biddle"]= new Array("biddle","Biddle",[1922,7]);afmktMakes["bizzarrini"]= new Array("bizzarrini","Bizzarrini",[1969,4]);afmktMakes["blackhawk"]= new Array("blackhawk","Blackhawk",[1930,1]);afmktMakes["bmw"]= new Array("bmw","BMW",[2023,71,11,14]);afmktMakes["bond"]= new Array("bond","Bond",[1960,7]);afmktMakes["borgward"]= new Array("borgward","Borgward",[1961,12]);afmktMakes["bricklin"]= new Array("bricklin","Bricklin",[1976,2]);afmktMakes["bristol"]= new Array("bristol","Bristol",[1974,28]);afmktMakes["bugatti"]= new Array("bugatti","Bugatti",[2022,2,2,0,3,9,55,2,9,18]);afmktMakes["buick"]= new Array("buick","Buick",[2023,77,4,12,2,0,8,0,7,0,3,1]);afmktMakes["cadillac"]= new Array("cadillac","Cadillac",[2023,77,4,39]);afmktMakes["case"]= new Array("case","Case",[1927,15]);afmktMakes["chandler"]= new Array("chandler","Chandler",[1926,2]);afmktMakes["checker"]= new Array("checker","Checker",[1982,35]);afmktMakes["chevrolet"]= new Array("chevrolet","Chevrolet",[2023,102,6]);afmktMakes["chrysler"]= new Array("chrysler","Chrysler",[2022,76,4,18]);afmktMakes["cisitalia"]= new Array("cisitalia","Cisitalia",[1952,6]);afmktMakes["citroen"]= new Array("citroen","Citroen",[1975,30]);afmktMakes["cleveland"]= new Array("cleveland","Cleveland",[1926,7]);afmktMakes["coda"]= new Array("coda","Coda",[2013,1]);afmktMakes["cole"]= new Array("cole","Cole",[1925,7,2,6]);afmktMakes["continental"]= new Array("continental","Continental",[1934,1]);afmktMakes["cord"]= new Array("cord","Cord",[1937,1,4,3]);afmktMakes["crosley"]= new Array("crosley","Crosley",[1952,6,4,3]);afmktMakes["cunningham"]= new Array("cunningham","Cunningham",[1954,1,24,18]);afmktMakes["daewoo"]= new Array("daewoo","Daewoo",[2002,3]);afmktMakes["daf"]= new Array("daf","DAF",[1973,14]);afmktMakes["daihatsu"]= new Array("daihatsu","Daihatsu",[1992,4]);afmktMakes["daimler"]= new Array("daimler","Daimler",[1969,23]);afmktMakes["darrin"]= new Array("darrin","Darrin",[1958,3]);afmktMakes["davis"]= new Array("davis","Davis",[1928,8]);afmktMakes["de-vaux"]= new Array("de-vaux","De Vaux",[1932,0]);afmktMakes["delage"]= new Array("delage","Delage",[1953,7,7,10]);afmktMakes["delahaye"]= new Array("delahaye","Delahaye",[1954,8,7,4]);afmktMakes["dellow"]= new Array("dellow","Dellow",[1959,10]);afmktMakes["delorean"]= new Array("delorean","DeLorean",[1983,2]);afmktMakes["denzel"]= new Array("denzel","Denzel",[1960,3]);afmktMakes["desoto"]= new Array("desoto","DeSoto",[1961,32]);afmktMakes["detomaso"]= new Array("detomaso","DeTomaso",[1989,8,7,7,2,2]);afmktMakes["deutsch-bonnet"]= new Array("deutsch-bonnet","Deutsch-Bonnet",[1961,6]);afmktMakes["diana"]= new Array("diana","Diana",[1928,3]);afmktMakes["dkw"]= new Array("dkw","DKW",[1965,11]);afmktMakes["dodge"]= new Array("dodge","Dodge",[2023,91,4,0,7,0,6]);afmktMakes["doretti"]= new Array("doretti","Doretti",[1955,1]);afmktMakes["du-pont"]= new Array("du-pont","Du Pont",[1932,13]);afmktMakes["dual-ghia"]= new Array("dual-ghia","Dual-Ghia",[1963,3,2,2]);afmktMakes["duesenberg"]= new Array("duesenberg","Duesenberg",[1937,9]);afmktMakes["durant"]= new Array("durant","Durant",[1932,4,2,5]);afmktMakes["duryea"]= new Array("duryea","Duryea",[1907,11]);afmktMakes["eagle"]= new Array("eagle","Eagle",[1998,10]);afmktMakes["edsel"]= new Array("edsel","Edsel",[1960,2]);afmktMakes["elcar"]= new Array("elcar","Elcar",[1931,15]);afmktMakes["elva"]= new Array("elva","Elva",[1966,8]);afmktMakes["erskine"]= new Array("erskine","Erskine",[1930,3]);afmktMakes["essex"]= new Array("essex","Essex",[1933,6,3,5]);afmktMakes["excalibur"]= new Array("excalibur","Excalibur",[1993,0,4,24]);afmktMakes["facel-vega"]= new Array("facel-vega","Facel Vega",[1964,10]);afmktMakes["fairthorpe"]= new Array("fairthorpe","Fairthorpe",[1962,6]);afmktMakes["falcon-knight"]= new Array("falcon-knight","Falcon Knight",[1928,1]);afmktMakes["fargo"]= new Array("fargo","Fargo",[1940,1,3]);afmktMakes["ferrari"]= new Array("ferrari","Ferrari",[2023,76]);afmktMakes["fiat"]= new Array("fiat","Fiat",[2022,10,29,36,34]);afmktMakes["fisker"]= new Array("fisker","Fisker",[2012,0]);afmktMakes["flint"]= new Array("flint","Flint",[1927,4]);afmktMakes["ford"]= new Array("ford","Ford",[2023,120,2]);afmktMakes["franklin"]= new Array("franklin","Franklin",[1934,31]);afmktMakes["frazer"]= new Array("frazer","Frazer",[1951,5]);afmktMakes["frazer-nash"]= new Array("frazer-nash","Frazer Nash",[1957,9]);afmktMakes["freightliner"]= new Array("freightliner","Freightliner",[2021,19]);afmktMakes["fwd"]= new Array("fwd","FWD",[1912,2]);afmktMakes["gardner"]= new Array("gardner","Gardner",[1931,11]);afmktMakes["genesis"]= new Array("genesis","Genesis",[2023,6]);afmktMakes["geo"]= new Array("geo","Geo",[1997,8]);afmktMakes["glas"]= new Array("glas","Glas",[1968,5]);afmktMakes["gmc"]= new Array("gmc","GMC",[2023,88]);afmktMakes["goggomobil"]= new Array("goggomobil","Goggomobil",[1961,6]);afmktMakes["goliath"]= new Array("goliath","Goliath",[1961,11]);afmktMakes["gordon-keeble"]= new Array("gordon-keeble","Gordon-Keeble",[1967,3]);afmktMakes["graham"]= new Array("graham","Graham",[1941,11]);afmktMakes["graham-paige"]= new Array("graham-paige","Graham-Paige",[1930,2]);afmktMakes["griffith"]= new Array("griffith","Griffith",[1966,2]);afmktMakes["hansa"]= new Array("hansa","Hansa",[1961,0]);afmktMakes["haynes"]= new Array("haynes","Haynes",[1925,5]);afmktMakes["hcs"]= new Array("hcs","HCS",[1924,4]);afmktMakes["healey"]= new Array("healey","Healey",[1954,8]);afmktMakes["henry-j"]= new Array("henry-j","Henry J",[1954,3]);afmktMakes["hertz"]= new Array("hertz","Hertz",[1927,2]);afmktMakes["hillman"]= new Array("hillman","Hillman",[1967,21]);afmktMakes["hispano-suiza"]= new Array("hispano-suiza","Hispano-Suiza",[1939,20]);afmktMakes["honda"]= new Array("honda","Honda",[2023,54]);afmktMakes["hotchkiss"]= new Array("hotchkiss","Hotchkiss",[1954,4]);afmktMakes["hrg"]= new Array("hrg","HRG",[1956,10]);afmktMakes["hudson"]= new Array("hudson","Hudson",[1957,11,4,33]);afmktMakes["humber"]= new Array("humber","Humber",[1967,21]);afmktMakes["hummer"]= new Array("hummer","Hummer",[2010,8]);afmktMakes["hupmobile"]= new Array("hupmobile","Hupmobile",[1941,3,2,27]);afmktMakes["hyundai"]= new Array("hyundai","Hyundai",[2023,37]);afmktMakes["infiniti"]= new Array("infiniti","INFINITI",[2023,33]);afmktMakes["international"]= new Array("international","International",[1980,30,37,0,6]);afmktMakes["iso"]= new Array("iso","Iso",[1974,11]);afmktMakes["isotta-fraschin"]= new Array("isotta-fraschin","Isotta Fraschin",[1933,12]);afmktMakes["isuzu"]= new Array("isuzu","Isuzu",[2008,27,15,5]);afmktMakes["jaguar"]= new Array("jaguar","Jaguar",[2023,77,6,4]);afmktMakes["jeep"]= new Array("jeep","Jeep",[2023,80]);afmktMakes["jeffery"]= new Array("jeffery","Jeffery",[1917,3]);afmktMakes["jensen"]= new Array("jensen","Jensen",[1976,27]);afmktMakes["jewett"]= new Array("jewett","Jewett",[1927,5]);afmktMakes["jordan"]= new Array("jordan","Jordan",[1931,14]);afmktMakes["jowett"]= new Array("jowett","Jowett",[1954,4]);afmktMakes["kaiser"]= new Array("kaiser","Kaiser",[1955,9]);afmktMakes["karma"]= new Array("karma","Karma",[2023,6]);afmktMakes["kia"]= new Array("kia","Kia",[2023,29]);afmktMakes["kissel"]= new Array("kissel","Kissel",[1930,2]);afmktMakes["kurtis"]= new Array("kurtis","Kurtis",[1955,1]);afmktMakes["laforza"]= new Array("laforza","Laforza",[2001,12]);afmktMakes["lagonda"]= new Array("lagonda","Lagonda",[1964,3,3,10,8,6]);afmktMakes["lamborghini"]= new Array("lamborghini","Lamborghini",[2022,41,3,14]);afmktMakes["lanchester"]= new Array("lanchester","Lanchester",[1954,4]);afmktMakes["lancia"]= new Array("lancia","Lancia",[1982,1,2,3,2,24]);afmktMakes["land-rover"]= new Array("land-rover","Land Rover",[2023,36,13,26]);afmktMakes["lasalle"]= new Array("lasalle","LaSalle",[1940,13]);afmktMakes["lea-francis"]= new Array("lea-francis","Lea-Francis",[1954,4]);afmktMakes["lexington"]= new Array("lexington","Lexington",[1927,4]);afmktMakes["lexus"]= new Array("lexus","Lexus",[2023,33]);afmktMakes["lincoln"]= new Array("lincoln","Lincoln",[2023,78,3,22]);afmktMakes["lloyd"]= new Array("lloyd","Lloyd",[1961,5]);afmktMakes["locomobile"]= new Array("locomobile","Locomobile",[1929,28]);afmktMakes["lotus"]= new Array("lotus","Lotus",[2023,0,2,5,2,17,2,38]);afmktMakes["lucid"]= new Array("lucid","Lucid",[2023,1]);afmktMakes["maico"]= new Array("maico","Maico",[1960,5]);afmktMakes["marathon"]= new Array("marathon","Marathon",[1956,3]);afmktMakes["marauder"]= new Array("marauder","Marauder",[1952,2]);afmktMakes["marcos"]= new Array("marcos","Marcos",[1972,2]);afmktMakes["marmon"]= new Array("marmon","Marmon",[1933,24]);afmktMakes["marquette"]= new Array("marquette","Marquette",[1930,0]);afmktMakes["maserati"]= new Array("maserati","Maserati",[2023,21,11,2,2,41]);afmktMakes["matra"]= new Array("matra","Matra",[1969,4]);afmktMakes["maxwell"]= new Array("maxwell","Maxwell",[1925,12]);afmktMakes["maybach"]= new Array("maybach","Maybach",[2012,9]);afmktMakes["mazda"]= new Array("mazda","Mazda",[2023,56]);afmktMakes["mclaren"]= new Array("mclaren","McLaren",[2023,11]);afmktMakes["mercedes-benz"]= new Array("mercedes-benz","Mercedes-Benz",[2023,77,6,39]);afmktMakes["mercury"]= new Array("mercury","Mercury",[2011,65,4,3]);afmktMakes["merkur"]= new Array("merkur","Merkur",[1989,4]);afmktMakes["messerschmitt"]= new Array("messerschmitt","Messerschmitt",[1964,9]);afmktMakes["metropolitan"]= new Array("metropolitan","Metropolitan",[1962,8]);afmktMakes["mg"]= new Array("mg","MG",[1980,35,5,6]);afmktMakes["mini"]= new Array("mini","Mini",[2023,21]);afmktMakes["mitsubishi"]= new Array("mitsubishi","Mitsubishi",[2023,40]);afmktMakes["mobility-ventur"]= new Array("mobility-ventur","Mobility Ventur",[2017,3]);afmktMakes["monteverdi"]= new Array("monteverdi","Monteverdi",[1974,7]);afmktMakes["moon"]= new Array("moon","Moon",[1929,9]);afmktMakes["moretti"]= new Array("moretti","Moretti",[1960,10]);afmktMakes["morgan"]= new Array("morgan","Morgan",[2007,5,6,20,2,29]);afmktMakes["morris"]= new Array("morris","Morris",[1974,28]);afmktMakes["moskvich"]= new Array("moskvich","Moskvich",[1964,8]);afmktMakes["nardi"]= new Array("nardi","Nardi",[1959,1]);afmktMakes["nash"]= new Array("nash","Nash",[1957,11,4,24]);afmktMakes["nissan"]= new Array("nissan","Nissan",[2023,65]);afmktMakes["nsu"]= new Array("nsu","NSU",[1974,16]);afmktMakes["oakland"]= new Array("oakland","Oakland",[1931,22,2]);afmktMakes["oldsmobile"]= new Array("oldsmobile","Oldsmobile",[2004,58,4,41]);afmktMakes["omega"]= new Array("omega","Omega",[1968,1]);afmktMakes["opel"]= new Array("opel","Opel",[1975,11,2,15]);afmktMakes["osca"]= new Array("osca","Osca",[1967,12]);afmktMakes["packard"]= new Array("packard","Packard",[1958,12,4,43]);afmktMakes["paige"]= new Array("paige","Paige",[1927,12]);afmktMakes["panhard"]= new Array("panhard","Panhard",[1962,8,5,3]);afmktMakes["panoz"]= new Array("panoz","Panoz",[2007,8]);afmktMakes["panther"]= new Array("panther","Panther",[1974,2]);afmktMakes["peerless"]= new Array("peerless","Peerless",[1960,2,27,11]);afmktMakes["pegaso"]= new Array("pegaso","Pegaso",[1958,7]);afmktMakes["peugeot"]= new Array("peugeot","Peugeot",[1991,46]);afmktMakes["pierce-arrow"]= new Array("pierce-arrow","Pierce-Arrow",[1938,37]);afmktMakes["plymouth"]= new Array("plymouth","Plymouth",[2001,55,4,14]);afmktMakes["polestar"]= new Array("polestar","Polestar",[2023,3]);afmktMakes["pontiac"]= new Array("pontiac","Pontiac",[2010,64,4,16]);afmktMakes["porsche"]= new Array("porsche","Porsche",[2023,75]);afmktMakes["qvale"]= new Array("qvale","Qvale",[2002,1]);afmktMakes["ram"]= new Array("ram","Ram",[2023,12]);afmktMakes["rambler"]= new Array("rambler","Rambler",[1913,11]);afmktMakes["reliant"]= new Array("reliant","Reliant",[1974,12,2,8]);afmktMakes["renault"]= new Array("renault","Renault",[1987,41]);afmktMakes["reo"]= new Array("reo","Reo",[1936,31]);afmktMakes["rickenbacker"]= new Array("rickenbacker","Rickenbacker",[1927,5]);afmktMakes["riley"]= new Array("riley","Riley",[1963,17]);afmktMakes["rivian"]= new Array("rivian","Rivian",[2023,1]);afmktMakes["roamer"]= new Array("roamer","Roamer",[1930,14]);afmktMakes["rockne"]= new Array("rockne","Rockne",[1933,1]);afmktMakes["rollin"]= new Array("rollin","Rollin",[1925,1]);afmktMakes["rolls-royce"]= new Array("rolls-royce","Rolls-Royce",[2022,18,2,56]);afmktMakes["roosevelt"]= new Array("roosevelt","Roosevelt",[1930,1]);afmktMakes["rover"]= new Array("rover","Rover",[1980,0,6,24]);afmktMakes["saab"]= new Array("saab","Saab",[2011,61]);afmktMakes["sabra"]= new Array("sabra","Sabra",[1964,5]);afmktMakes["saleen"]= new Array("saleen","Saleen",[2006,4]);afmktMakes["salmson"]= new Array("salmson","Salmson",[1957,6]);afmktMakes["saturn"]= new Array("saturn","Saturn",[2010,19]);afmktMakes["scion"]= new Array("scion","Scion",[2016,12]);afmktMakes["scripps-booth"]= new Array("scripps-booth","Scripps Booth",[1922,2]);afmktMakes["shelby"]= new Array("shelby","Shelby",[2000,1,31,6]);afmktMakes["sheridan"]= new Array("sheridan","Sheridan",[1921,1]);afmktMakes["siata"]= new Array("siata","Siata",[1970,21]);afmktMakes["simca"]= new Array("simca","Simca",[1971,25]);afmktMakes["singer"]= new Array("singer","Singer",[1965,17]);afmktMakes["skoda"]= new Array("skoda","Skoda",[1967,21]);afmktMakes["smart"]= new Array("smart","Smart",[2019,11]);afmktMakes["spyker"]= new Array("spyker","Spyker",[2019,14]);afmktMakes["srt"]= new Array("srt","SRT",[2014,1]);afmktMakes["standard"]= new Array("standard","Standard",[1963,15]);afmktMakes["stanguellini"]= new Array("stanguellini","Stanguellini",[1949,2]);afmktMakes["star"]= new Array("star","Star",[1928,6]);afmktMakes["stearns-knight"]= new Array("stearns-knight","Stearns Knight",[1930,10]);afmktMakes["sterling"]= new Array("sterling","Sterling",[1991,4]);afmktMakes["stevens-duryea"]= new Array("stevens-duryea","Stevens-Duryea",[1927,2,10,0,2,0,2,4]);afmktMakes["studebaker"]= new Array("studebaker","Studebaker",[1966,53,2,8]);afmktMakes["stutz"]= new Array("stutz","Stutz",[1974,4,35,23]);afmktMakes["subaru"]= new Array("subaru","Subaru",[2023,55]);afmktMakes["sunbeam"]= new Array("sunbeam","Sunbeam",[1973,25]);afmktMakes["suzuki"]= new Array("suzuki","Suzuki",[2013,28]);afmktMakes["swallow"]= new Array("swallow","Swallow",[1955,1]);afmktMakes["talbot-lago"]= new Array("talbot-lago","Talbot-Lago",[1949,2,8,5]);afmktMakes["tatra"]= new Array("tatra","Tatra",[1968,18]);afmktMakes["tesla"]= new Array("tesla","Tesla",[2023,15]);afmktMakes["think"]= new Array("think","Think",[2011,0]);afmktMakes["toyopet"]= new Array("toyopet","Toyopet",[1963,5]);afmktMakes["toyota"]= new Array("toyota","Toyota",[2023,65]);afmktMakes["triumph"]= new Array("triumph","Triumph",[1981,35]);afmktMakes["tucker"]= new Array("tucker","Tucker",[1948,0]);afmktMakes["turner"]= new Array("turner","Turner",[1966,11]);afmktMakes["tvr"]= new Array("tvr","TVR",[1977,20]);afmktMakes["utilimaster"]= new Array("utilimaster","Utilimaster",[1994,5]);afmktMakes["vauxhall"]= new Array("vauxhall","Vauxhall",[1963,6,8,3]);afmktMakes["velie"]= new Array("velie","Velie",[1919,2]);afmktMakes["vespa"]= new Array("vespa","Vespa",[1961,3]);afmktMakes["viking"]= new Array("viking","Viking",[1930,1]);afmktMakes["volkswagen"]= new Array("volkswagen","Volkswagen",[2023,78]);afmktMakes["volvo"]= new Array("volvo","Volvo",[2023,79]);afmktMakes["vpg"]= new Array("vpg","VPG",[2012,1]);afmktMakes["wartburg"]= new Array("wartburg","Wartburg",[1966,10]);afmktMakes["westcott"]= new Array("westcott","Westcott",[1925,3]);afmktMakes["whippet"]= new Array("whippet","Whippet",[1930,0]);afmktMakes["willys"]= new Array("willys","Willys",[1956,45]);afmktMakes["windsor"]= new Array("windsor","Windsor",[1930,1]);afmktMakes["wolseley"]= new Array("wolseley","Wolseley",[1965,16]);afmktMakes["workhorse"]= new Array("workhorse","Workhorse",[2005,5]);afmktMakes["yellow-cab"]= new Array("yellow-cab","Yellow Cab",[1929,8]);afmktMakes["yugo"]= new Array("yugo","Yugo",[1992,6]);afmktMakes["zundapp"]= new Array("zundapp","Zundapp",[1958,2])
