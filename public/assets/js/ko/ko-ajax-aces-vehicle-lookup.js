var afmktYears = {};
var d = new Date();
var yearNext = d.getFullYear()+1;
var SearchViewModel = function () {
    var self = this;
    self.baseAjaxURL = "";
    self.baseSubmitURL = ""
    self.hasPartTypes = true;
    self.hasSubmodels = true;
    self.hasEngines = true;
    self.profile = ko.observable("partsLookup");
    self.submitButtonTitle = "Show Parts";
    self.mode = "mym"
    self.smbeng = "engines"
    self.defaults = {
        "smbeng": "engines",
        "mode": "mym",
        "fromyear": 1896,
        "toyear": yearNext,
        "usestatic": false,
        "onlymake": ''
    }
    self.options = {}
    self.captions = {
        "loading": "Loading...",
        "choosemake": "Choose A Make...",
        "chooseyear": "Choose A Year...",
        "choosemodel": "Choose A Model...",
        "chooseengine": "Choose An Engine...",
        "choosesubmodel": "Choose A Submodel...",
        "chooseparttype": "Choose A Part Type...",
        "allengines": "All Engines",
        "allsubmodels": "All Submodels",
        "partsLookupsubmitButtonTitle": "Show Parts",
        "checkFitsubmitButtonTitle": "Check The Fit",
        "addCarGaragesubmitButtonTitle": "Add To Garage",
    };


    self.setSubmitButtonCaption = function () {
        switch (self.profile()) {
            case "partsLookup": {
                self.submitButtonTitle = self.captions.partsLookupsubmitButtonTitle;
                break
            }
            case "checkFit": {
                self.submitButtonTitle = self.captions.checkFitsubmitButtonTitle;
                break
            }
            case "addCarGarage": {
                self.submitButtonTitle = self.captions.addCarGaragesubmitButtonTitle;
                break
            }
        }
    }

    self.setCaptions = function (captions) {
        try {
            self.captions = jQuery.extend(self.captions, captions);
            self.setSubmitButtonCaption()
        } catch (oerr) {
        }
    }

    if (typeof window.SearchViewCaptions !== "undefined") {
        self.setCaptions(window.SearchViewCaptions);
    }


    self.profileWatcher = ko.computed(function () {
        self.setSubmitButtonCaption();

    })


    // makes
    self.selectedMake = ko.observable();
    self.makeSelectCaption = ko.observable(self.captions.choosemake);
    self.makes = ko.observableArray();
    // years
    self.selectedYear = ko.observable();
    self.yearSelectCaption = ko.observable(self.captions.chooseyear);
    self.years = ko.observableArray();

    // models
    self.selectedModel = ko.observable();
    self.modelSelectCaption = ko.observable(self.captions.choosemodel);
    self.models = ko.observableArray();

    // engines
    self.selectedEngine = ko.observable();
    self.engineSelectCaption = ko.observable(self.captions.chooseengine);
    self.engines = ko.observableArray();

    // submodels
    self.selectedSubModel = ko.observable();
    self.submodelSelectCaption = ko.observable(self.captions.choosesubmodel);
    self.submodels = ko.observableArray();

    // parttypes
    self.selectedPartType = ko.observable();
    self.parttypeSelectCaption = ko.observable(self.captions.chooseparttype);
    self.parttypes = ko.observableArray();

    // additional filters
    self.addonFilter = "";
    self.getAddOnFilters = function () {
        if (typeof  self.addonFilter == 'object') {
            return self.addonFilter;
        }
        return {noval: ""};
    }

    self.getPartsAddOnFilters = function () {
        if (typeof  self.addonFilter == 'object') {
            return "?" + jQuery.param(self.addonFilter);
        }
        return "";
    }
	self.moreFields = [];
	self.getMoreFields = function () {
		 var more = "";
		if (self.moreFields.length > 0 ) {
			for (x in self.moreFields) {
				more = more + '&' +self.moreFields[x]+ '='+$("input[name='"+self.moreFields[x]+"']").val();
			}
		}
		return more;
	}

    self.showParts = function () {
        if (self.profile() == "partsLookup") {
            self.addonFilter = jQuery.extend({
                "engine": self.selectedEngine(),
                "submodel": self.selectedSubModel()
            }, self.getAddOnFilters());
            window.location.href = self.baseSubmitURL + "/" + self.selectedMake() + "/" + self.selectedYear() + "/" + self.selectedModel() + (typeof self.selectedPartType() !== "undefined" ? "/" + self.selectedPartType() : "") + self.getPartsAddOnFilters() + self.getMoreFields();
        } else {
            self.addonFilter = jQuery.extend({
                "checkfit": "y",
                "make": self.selectedMake(),
                "year": self.selectedYear(),
                "model": self.selectedModel(),
                "engine": self.selectedEngine(),
                "submodel": self.selectedSubModel()
            }, self.getAddOnFilters());
           // window.location.href = self.baseSubmitURL + self.getPartsAddOnFilters();
        }
    };

    self.fillMakes = function (makes) {
        self.makes(makes);
        self.makeSelectCaption(self.captions.choosemake)
    }

    self.loadMakes = function (opts) {
        self.options = self.defaults;
        if (opts === Object(opts)) {
            self.options = $.extend(self.defaults, opts);
            smbeng = self.options.smbeng;

        } else {
            smbeng = opts;
        }
        if (typeof afmktMakes == "undefined" || typeof afmktModels == "undefined") {
            self.options.usestatic = false;
        }

        if (typeof smbeng == "undefined") {
            smbeng = "engines";
        }
        self.smbeng = smbeng;
        self.makeSelectCaption(self.captions.loading);
        if (self.options.onlymake != false && self.options.onlymake.length>1) {
            self.baseAjaxURL = self.baseAjaxURL + "&default=makes," + smbeng + '&onlymake='+self.options.onlymake.split('@@').join(',');
        } else {
            self.baseAjaxURL = self.baseAjaxURL + "&default=makes," + smbeng;
        }
        if (self.options.usestatic == false) {
                $.getJSON(self.baseAjaxURL, null,
                    function (data) {
                        self.makeSelectCaption(self.captions.choosemake);
                        self.makes(data.makes);
                    });

        } else {
            var makes = [];
            if (self.options.onlymake != false && self.options.onlymake.length>1) {
				var onlys = self.options.onlymake.split('@@');
                for (var key in afmktMakes) {
                    if (jQuery.inArray(afmktMakes[key][0],onlys)!=-1) {
                        makes.push({"id": afmktMakes[key][0], "name": afmktMakes[key][1]});
                    }
                }
                self.makeSelectCaption(self.captions.choosemake);
                self.makes(makes);

            } else {
				;
                for (var key in afmktMakes) {
					var selMakeYear = afmktMakes[key][2];
					if (selMakeYear[0]>=self.options.fromyear && selMakeYear[0]<=self.options.toyear){
							makes.push({"id": afmktMakes[key][0], "name": afmktMakes[key][1]});
					}

                }
                self.makeSelectCaption(self.captions.choosemake);
                self.makes(makes);
            }

        }
    }
    self.fillYears = function (years) {
        self.years(years);
        self.yearSelectCaption(self.captions.chooseyear);
    };
    self.loadYears = function (opts) {
         self.options = self.defaults;
        if (opts === Object(opts)) {
            self.options = $.extend(self.defaults, opts);
            smbeng = self.options.smbeng;

        } else {
            smbeng = opts;
        }
        if (typeof afmktMakes == "undefined" || typeof afmktModels == "undefined") {
            self.options.usestatic = false;
        }
        self.mode = "ymm";
        if (typeof smbeng == "undefined") {
            smbeng = "engines";
        }
        self.smbeng = smbeng;
		var onlys = self.options.onlymake.split('@@');
		if (self.options.onlymake.length>1){
			self.baseAjaxURL = self.baseAjaxURL + "&default=years," + smbeng+'&onlymake='+onlys.join(',');
		}else{
			self.baseAjaxURL = self.baseAjaxURL + "&default=years," + smbeng;
		}

        self.yearSelectCaption(self.captions.loading)
        if (self.options.usestatic == false) {

            $.getJSON(self.baseAjaxURL, null,
                function (data) {
                    self.yearSelectCaption(self.captions.chooseyear);
                    self.years(data.years);
                });
        } else {
            years = [];

            self.yearSelectCaption(self.captions.chooseyear);
            afmktYears = [];
            for (var make in afmktMakes) {
                var selMake = afmktMakes[make];
                var selMakeYear = selMake[2];
                var yearindex = selMakeYear[0] * 2;
                for (j = 0; j < selMakeYear.length; j += 2) {
                    yearindex = yearindex - selMakeYear[j];
                    for (i = 0; i <= selMakeYear[j + 1]; i++) {
                        y = yearindex - i;
                        if (y in afmktYears === false) {
                            afmktYears[y] = [];
                        }
						if(self.options.onlymake==false ){
								afmktYears[y].push({"id": selMake[0], "name": selMake[1]});

						}else {
							if (self.options.onlymake.length>1)
								if(jQuery.inArray(selMake[0],onlys)!=-1){

									afmktYears[y].push({"id": selMake[0], "name": selMake[1]});

								}
						}

                    }

                    yearindex = yearindex - selMakeYear[j + 1];
                }
            }
            for (var i = self.options.toyear; i >= self.options.fromyear; i--) {
                if(i  in afmktYears) {
                    if(afmktYears[i].length>0) {
                        years.push({"id": i, "name": i});
                    }
                }

            }
            self.years(years);
        }
    };
    self.selectedMake.subscribe(function (val) {
        if (self.mode == "ymm") {
            self.selectedModel("");
            if (!val || val == "") {
                self.modelSelectCaption(self.captions.choosemodel);
                self.models([]);

                return;
            }
            if (self.options.usestatic == false) {
                $.ajax({
                    type: 'GET',
                    url: self.baseAjaxURL,
                    data: $.extend({year: self.selectedYear(), make: val}, self.getAddOnFilters()),
                    context: self,
                    success: function (data) {
                        self.modelSelectCaption(self.captions.choosemodel);
                        self.models(data.models);
                        if (data.models.length == 1) {
                            self.selectedModel(data.models[0].id);
                        }
                    },
                    dataType: 'json'
                });
            } else {
                var selModel = afmktModels[val];
                var modelLength = selModel.length;
                var iyear = self.selectedYear();
                var models = [];
                $.each(selModel, function (i, v) {
                    if (v[2] <= iyear && iyear <= v[3] && (v[4] == "-1" || v[4].charAt(iyear - v[2]) == "1")) {
                        models.push({"id": v[0], "name": v[1]});
                    }
                });
                self.modelSelectCaption(self.captions.choosemodel);
                self.models(models);
                if (models.length == 1) {
                    self.selectedModel(models[0].id);
                }
            }
        } else {
            self.selectedYear("");
            if (!val || val == "") {
                self.yearSelectCaption(self.captions.chooseyear);
                self.years([]);
                return;
            }
            self.yearSelectCaption(self.captions.loading);
            if (self.options.usestatic == false) {
                $.ajax({
                    type: 'GET',
                    url: self.baseAjaxURL,
                    data: $.extend({make: val}, self.getAddOnFilters()),
                    context: self,
                    success: function (data) {
                        self.yearSelectCaption(self.captions.chooseyear);
                        self.years(data.years);
                    },
                    dataType: 'json'
                });
            } else {
                var selMake = afmktMakes[val];
                var selMakeYear = selMake[2];
                var yearindex = selMakeYear[0] * 2;
                var years = [];
                for (j = 0; j < selMakeYear.length; j += 2) {
                    yearindex = yearindex - selMakeYear[j];
                    for (i = 0; i <= selMakeYear[j + 1]; i++) {
                        if ((yearindex - i >= self.options.fromyear) && (yearindex - i <= self.options.toyear)) {
                            years.push({"id": (yearindex - i), "name": (yearindex - i)});
                        }
                    }
                    yearindex = yearindex - selMakeYear[j + 1];

                }
                self.yearSelectCaption(self.captions.chooseyear);
                self.years(years);
            }
        }
    });

    self.selectedYear.subscribe(function (val) {
        if (self.mode == "ymm") {
            self.selectedMake("");
            if (!val || val == "") {
                self.makeSelectCaption(self.captions.choosemodel);
                self.makes([]);
                return;
            }
            self.makeSelectCaption(self.captions.loading);
            if (self.options.usestatic == false) {
                $.ajax({
                    type: 'GET',
                    url: self.baseAjaxURL,
                    data: $.extend({year: val}, self.getAddOnFilters()),
                    context: self,
                    success: function (data) {
                        self.makeSelectCaption(self.captions.choosemake);
                        self.makes(data.makes);
						if (data.makes.length == 1) {
							self.selectedMake(data.makes[0].id);
						}
                    },
                    dataType: 'json'
                });
            } else {
                self.makeSelectCaption(self.captions.choosemake);
                self.makes(afmktYears[val]);
				if (afmktYears[val].length == 1) {
                        self.selectedMake(afmktYears[val][0].id);
                }
            }
        } else {
            self.selectedModel("");
            if (!val || val == "") {
                self.modelSelectCaption(self.captions.choosemodel);
                self.models([]);
                return;
            }
            self.modelSelectCaption(self.captions.loading);
            if (self.options.usestatic == false) {
                $.ajax({
                    type: 'GET',
                    url: self.baseAjaxURL,
                    data: $.extend({make: self.selectedMake(), year: val}, self.getAddOnFilters()),
                    context: self,
                    success: function (data) {
                        self.modelSelectCaption(self.captions.choosemodel);
                        self.models(data.models);
                        if (data.models.length == 1) {
                            self.selectedModel(data.models[0].id);
                        }
                        ;
                    },
                    dataType: 'json'
                });
            } else {
                var selModel = afmktModels[self.selectedMake()];
                var modelLength = selModel.length;
                var iyear = parseInt(val, 10);
                var models = [];
                $.each(selModel, function (i, v) {
                    if (v[2] <= iyear && iyear <= v[3] && (v[4] == "-1" || v[4].charAt(iyear - v[2]) == "1")) {
                        models.push({"id": v[0], "name": v[1]});
                    }

                });
                self.modelSelectCaption(self.captions.choosemodel);
                self.models(models);
                if (models.length == 1) {
                    self.selectedModel(models[0].id);
                }

            }
        }
    });

    self.selectedModel.subscribe(function (val) {
        if (self.smbeng == "engines") {
            if (self.hasEngines === false) {
                return false;
            }
            if (!val || val == "") {
                self.engineSelectCaption(self.captions.chooseengine);
                self.engines([]);
                return;
            }
            self.engineSelectCaption(self.captions.loading);
            $.ajax({
                type: 'GET',
                url: self.baseAjaxURL,
                data: $.extend({
                    make: self.selectedMake(),
                    year: self.selectedYear(),
                    model: val
                }, self.getAddOnFilters()),
                context: self,
                success: function (data) {
                    self.engineSelectCaption(self.captions.choosengine);
                    if (data.engines.length > 1) {
                        data.engines.unshift({"id": "all", "name": self.captions.allengines});
                    }
                    self.engines(data.engines);
                    if (data.engines.length == 1) {
                        self.selectedEngine(data.engines[0].id);
                    }
                    ;
                },
                dataType: 'json'
            });
        } else {
            if (self.hasSubmodels === false) {
                return false;
            }
            self.selectedSubModel("");
            if (!val || val == "") {
                self.submodelSelectCaption(self.captions.choosesubmodel);
                self.submodels([]);
                return;
            }
            if (!val || val == "") {
                self.engineSelectCaption(self.captions.chooseengine);
                self.engines([]);
                return;
            }
            self.submodelSelectCaption(self.captions.loading);
            self.engineSelectCaption(self.captions.loading);
            $.ajax({
                type: 'GET',
                url: self.baseAjaxURL,
                data: $.extend({
                    make: self.selectedMake(),
                    year: self.selectedYear(),
                    model: val
                }, self.getAddOnFilters()),
                context: self,
                success: function (data) {
                    self.submodelSelectCaption(self.captions.choosengine);
                    if (data.submodels.length > 1) {
                        data.submodels.unshift({"id": "all", "name": self.captions.allsubmodels});
                    }
                    self.submodels(data.submodels);
                    if (data.submodels.length == 1) {
                        self.selectedSubModel(data.submodels[0].id);
                    }
                    ;
                },
                dataType: 'json'
            });

        }
    });

    self.selectedEngine.subscribe(function (val) {
        if (self.smbeng == "engines") {
            if (self.hasSubmodels === false) {
                return false;
            }
            self.selectedSubModel("");
            if (!val || val == "") {
                self.submodelSelectCaption(self.captions.choosesubmodel);
                self.submodels([]);
                return;
            }
            self.submodelSelectCaption(self.captions.loading);
            $.ajax({
                type: 'GET',
                url: self.baseAjaxURL,
                data: $.extend({
                    make: self.selectedMake(),
                    year: self.selectedYear(),
                    model: self.selectedModel(),
                    engine: val
                }, self.getAddOnFilters()),
                context: self,
                success: function (data) {
                    self.submodelSelectCaption(self.captions.choosesubmodel);
					if (data.submodels){
                    if (data.submodels.length > 1) {
                        data.submodels.unshift({"id": "all", "name": self.captions.allsubmodels});
                    }
                    self.submodels(data.submodels);
                    if (data.submodels.length == 1) {
                        self.selectedSubModel(data.submodels[0].id);
                    }
				}
                },
                dataType: 'json'
            });
        } else {
            if (self.hasPartTypes === false) {
                return false;
            }

            self.selectedPartType("");
            if (!val || val == "") {
                self.parttypeSelectCaption(self.captions.chooseparttype);
                self.parttypes([]);
                return;
            }
            self.parttypeSelectCaption(self.captions.loading);
            $.ajax({
                type: 'GET',
                url: self.baseAjaxURL,
                data: $.extend({
                    make: self.selectedMake(),
                    year: self.selectedYear(),
                    model: self.selectedModel(),
                    submodel: self.selectedSubModel(),
                    engine: val
                }, self.getAddOnFilters()),
                context: self,
                success: function (data) {
                    self.parttypeSelectCaption(self.captions.chooseparttype);
                    self.parttypes(data.parttypes);
                },
                dataType: 'json'
            });
        }
    });


    self.selectedSubModel.subscribe(function (val) {
        if (self.smbeng == "engines") {
            if (self.hasPartTypes === false) {
                return false;
            }

            self.selectedPartType("");
            if (!val || val == "") {
                self.parttypeSelectCaption(self.captions.chooseparttype);
                self.parttypes([]);
                return;
            }
            self.parttypeSelectCaption(self.captions.loading);
            $.ajax({
                type: 'GET',
                url: self.baseAjaxURL,
                data: $.extend({
                    make: self.selectedMake(),
                    year: self.selectedYear(),
                    model: self.selectedModel(),
                    engine: self.selectedEngine(),
                    submodel: val
                }, self.getAddOnFilters()),
                context: self,
                success: function (data) {
                    self.parttypeSelectCaption(self.captions.chooseparttype);
                    self.parttypes(data.parttypes);
                },
                dataType: 'json'
            });
        } else {
            if (self.hasEngines === false) {
                return false;
            }

            self.selectedEngine("");
            if (!val || val == "") {
                self.engineSelectCaption(self.captions.chooseengine);
                self.engines([]);
                return;
            }
            self.engineSelectCaption(self.captions.loading);
            if(self.selectedModel()=="") {
                return false;
            }
            $.ajax({
                type: 'GET',
                url: self.baseAjaxURL,
                data: $.extend({
                    make: self.selectedMake(),
                    year: self.selectedYear(),
                    model: self.selectedModel(),
                    submodel: val
                }, self.getAddOnFilters()),
                context: self,
                success: function (data) {
                    self.engineSelectCaption(self.captions.chooseengine);
                    if(data.engines)
                    {
                        if (data.engines.length > 1) {
                            data.engines.unshift({"id": "all", "name": self.captions.allengines});
                        }
                        self.engines(data.engines);
                        if (data.engines.length == 1) {
                            self.selectedEngine(data.engines[0].id);
                        }
                    }


                },
                dataType: 'json'
            });
        }
    });

}
