var SearchViewModel = function() {
    var self = this;
    self.baseAjaxURL = "";
    self.baseSubmitURL = ""
    // makes
    self.selectedMake = ko.observable();
    self.makeSelectCaption = ko.observable("Loading...");
    self.makes = ko.observableArray();
    // years
    self.selectedYear = ko.observable();
    self.yearSelectCaption = ko.observable("Choose Year...");
    self.years = ko.observableArray();

    // models
    self.selectedModel = ko.observable();
    self.modelSelectCaption = ko.observable("Choose Model...");
    self.models = ko.observableArray();

    // parttypes
    self.selectedPartType = ko.observable();
    self.parttypeSelectCaption = ko.observable("Choose Part Type...");
    self.parttypes = ko.observableArray();

    // additional filters
    self.addonFilter="";
    self.getAddOnFilters=function(){
        if(typeof  self.addonFilter =='object'){
            return self.addonFilter;
        }
        return {noval:""};
    }

    self.getPartsAddOnFilters=function(){
        if(typeof  self.addonFilter =='object'){
            return "?"+jQuery.param(self.addonFilter);
        }
        return "";
    }


    self.showParts = function() {
        window.location.href = self.baseSubmitURL + "/" + self.selectedMake() + "/" + self.selectedYear() + "/" + self.selectedModel() + "/" + self.selectedPartType()+self.getPartsAddOnFilters();
    };

    self.fillMakes = function(makes) {
        self.makes(makes);
        self.makeSelectCaption("Choose A Make...")
    }

    self.loadMakes = function() {
        self.makeSelectCaption("Loading...")
        $.getJSON(self.baseAjaxURL, null,
            function(data) {
                self.makeSelectCaption("Choose Make...");
                self.makes(data.makes);
            });
    }

    self.selectedMake.subscribe(function(val) {
        self.selectedYear("");
        if (!val) {
            return;
        }
        self.yearSelectCaption("Loading...");

        $.ajax({
            type: 'GET',
            url: self.baseAjaxURL,
            data: $.extend({make: val},self.getAddOnFilters()),
            context: self,
            success: function(data) {
                self.yearSelectCaption("Choose Year...");
                self.years(data.years);
            },
            dataType: 'json'
        });
    });

    self.selectedYear.subscribe(function(val) {
        self.selectedModel("");
        if (!val) {
            return;
        }
        self.modelSelectCaption("Loading...");
        $.ajax({
            type: 'GET',
            url: self.baseAjaxURL,
            data: $.extend({make: self.selectedMake(),year: val},self.getAddOnFilters()),
            context: self,
            success: function(data) {
                self.modelSelectCaption("Choose Model...");
                self.models(data.models);
            },
            dataType: 'json'
        });
    });

    self.selectedModel.subscribe(function(val) {
        self.selectedPartType("");
        if (!val) {
            return;
        }
        self.parttypeSelectCaption("Loading...");
        $.ajax({
            type: 'GET',
            url: self.baseAjaxURL,
            data: $.extend({make: self.selectedMake(), year: self.selectedYear(), model: val},self.getAddOnFilters()),
            context: self,
            success: function(data) {
                self.parttypeSelectCaption("Choose Part Type...");
                var parttypes = new Array();
                $.each(data.parttypes, function(k1, item) {
                    $.each(item.categories, function(k2, ctg) {
                        parttypes.push({
                            "id": item.id,
                            "name": ctg.category_name + " - " + item.name,
                            "cpos": ctg.position,
                            "ppos": item.position
                        });
                    })
                })
                self.parttypes(parttypes.sort(function(a, b) {
                    if(a.name < b.name) return -1;
                    if(a.name > b.name) return 1;
                    return 0;
                }));
            },
            dataType: 'json'
        });
    });

}
