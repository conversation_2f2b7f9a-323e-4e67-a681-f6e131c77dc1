function findDistance(t1, n1, t2, n2) {
    var lat1, lon1, lat2, lon2, dlat, dlon, a, c, dm, dk, mi, km;
    // convert coordinates to radians
    lat1 = deg2rad(t1);
    lon1 = deg2rad(n1);
    lat2 = deg2rad(t2);
    lon2 = deg2rad(n2);
    // find the differences between the coordinates
    dlat = lat2 - lat1;
    dlon = lon2 - lon1;
    // here's the heavy lifting
    a = Math.pow(Math.sin(dlat / 2), 2) + Math.cos(lat1) * Math.cos(lat2) * Math.pow(Math.sin(dlon / 2), 2);
    c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a)); // great circle distance in radians
    dm = c * 3961; // great circle distance in miles
    // round the results down to the nearest 1/1000
    mi = round(dm);

    return mi;
}

// convert degrees to radians
function deg2rad(deg) {
    rad = deg * Math.PI / 180; // radians = degrees * pi/180
    return rad;
}


// round to the nearest 1/1000
function round(x) {
    return Math.round(x * 1000) / 1000;
}

(function () {
    var toMoney = function (num) {
        return '$' + (num.toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
    };
    var handler = function (element, valueAccessor, allBindings) {
        var $el = $(element);
        var method;
        // Gives us the real value if it is a computed observable or not
        var valueUnwrapped = ko.unwrap(valueAccessor());
        if ($el.is(':input')) {
            method = 'val';
        } else {
            method = 'text';
        }
        return $el[method](toMoney(valueUnwrapped));
    };
    ko.bindingHandlers.money = {
        update: handler
    };
})();


var Shipmethod = function (availMethods, ispDefault, lat, lng) {
    var self = this;
    self.availService = ko.observable();
    self.availMethods = availMethods;
    self.ispDefault = ispDefault;
    self.distance = 0;
    if (availMethods == null && ispDefault) {
        self.availService({
            "desc": "",
            "charges": [{
                "id": "In Store Pickup",
                "value": 0
            }]
        });

        return false;
    }

    self.calculateAvailableServices = function (lat, lng) {

        try {
			if(typeof window.sophiouserlat !=='undefined') {
				lat =window.sophiouserlat;
				lng = window.sophiouserlong;
			}
            self.distance = findDistance(lat, lng, self.availMethods.lat, self.availMethods.lng);

            jQuery.each(self.availMethods.shipmethods, function (key, value) {
                if (self.distance > value.from && self.distance <= value.to) {
                    // if (self.ispDefault) {
                    //   value.charges.unshift({
                    //       "id": "In Store Pickup",
                    //       "value": 0
                    //  });
                    // }
					value.charges.forEach(function(part,index,arr){
						arr[index].row =key;
					});


                    self.availService(value);
                    return false;
                }

            });
        } catch (oerr) {

        }

        if(self.availService()==null){
             self.availService({
            "desc": "",
            "charges": [{
                "id": "In Store Pickup",
                "value": 0
            }]
        });
        }
    }


    self.calculateAvailableServices(lat, lng);

};

var Warranty = function (option, price) {
    var self = this;
    self.description = option.desc || "";
    self.charge = (option.incl ? 0 : Math.round(Math.max(price * option.markup, option.min,option.min_transmission)));
    self.includedInSellPrice = option.incl;
	self.formattedId =  (self.includedInSellPrice == 1 ? self.description + "-0" : self.description + "-" + self.charge.toString());
    self.formatedDescription = (self.includedInSellPrice == 1 ? self.description + " - Included" : self.description + " +$" + self.charge.toString());
};
// CLASS Product
var Product = function (id, name, price, type, shippingoptions, lat, lng, warrantyoptions, extraData) {

    var self = this;
    self.id = ko.observable(id);
    self.name = ko.observable(name);
    self.price = ko.observable(price);
    self.type = ko.observable(type);
    self.extra = ko.observable(extraData);
    self.shipping = ko.observable(new Shipmethod(shippingoptions, (type == "recycled" ? true : false), lat, lng));

    self.warranty = ko.observableArray(ko.utils.arrayMap(warrantyoptions, function (item) {
        return new Warranty(item, self.price() + (self.extra().coreprice ? self.extra().coreprice : 0));
    }));




};
// CLASS CartItem
var CartItem = function (product, quantity, shippingOption, warrantyOption, sellerId) {
    var self = this;




    var getShippingCharge = function (val) {
         try {
        return ko.utils.arrayFirst(self.product().shipping().availService().charges, function (item) {
                return item.id === val;
            }) || {
                "value": 0
            }
             } catch (err){
            return {
                "value": 0
            };
        }
    }

    var getWarrantyCharge = function (val) {
        try {
        return ko.utils.arrayFirst(self.product().warranty(), function (item) {
                return item.description === val;
            }) || {
                "charge": 0
            };
        } catch (err){
            return {
                "charge": 0
            };
        }
    }

    var saveQtyToBacked = function (value) {
        /*
         if (self.sendChangesToBackend() === false || self.backendURL() === "") {
         return true;
         }
         */


        $.when($.ajax({
            url: "updatecartajax.wws",
            method: "POST",
            dataType: 'json',
            context: self,
            data: {
                value: value,
                id: self.product().id()
            }
        })).done(function (data) {
            if (data.msg) {
                swal({text: data.msg, type: "info", "title": "Attention"});
            }
            self.quantity(data.qty || value);
            //window.location.href = window.location.href;
        });


    }
    var saveChangesToBackend = function (field, value) {
        if (self.sendChangesToBackend() === false) {
            return true;
        }


        $.when($.ajax({
            url: "/updatetlineitemajax.wws",
            method: "POST",
            dataType: 'json',
            context: self,
            data: {
                value: value,
                field: field,
                id: self.product().id()
            }
        })).done(function (data) {
            if (data.message) {
                swal({text: data.message, type: "info", "title": "Attention"});
            }
        });
    }


    self.sellerId = sellerId;
    self.product = ko.observable(product);
    self.quantity = ko.observable(quantity || 1);


    self.quantity.subscribe(function (val) {
        if (val == 0) {
            ko.postbox.publish('REMOVABLE_LINE_ITEM', self);
        }
    });

    self.changedDrop = ko.observable();
    self.selectedWarrantyOption = ko.observable(unescape(warrantyOption));
    self.selectedShippingOption = ko.observable(unescape(shippingOption));



    self.warrantyCost = ko.computed(function () {
        var charge = getWarrantyCharge(self.selectedWarrantyOption());
        return charge.charge;
    })



    self.shippingCost = ko.computed(function () {
        var charge = getShippingCharge(self.selectedShippingOption());
        return charge.value;
    });



    self.selectedWarrantyOption.subscribe(function (val) {
        if (val !== warrantyOption) {
            saveChangesToBackend("wroption", val);
            warrantyOption = val;
        }
    })

    self.selectedShippingOption.subscribe(function (val) {
        self.changedDrop = val;

        if (val !== shippingOption) {
            saveChangesToBackend("frgservice", val);
            shippingOption = val;

        }
    })


    self.sendChangesToBackend = ko.observable(false);
    self.backendURL = ko.observable("");


    self.corecharge = ko.computed(function () {
        return (self.product().extra().coreprice ? self.product().extra().coreprice : 0) * self.quantity();
    })

    self.cost = ko.computed(function () {
        // return (self.product().price() + self.warrantyCost() + self.shippingCost()) * self.quantity();
        //return (self.product().price() + self.warrantyCost()) * self.quantity();
        return (self.product().price()) * self.quantity();
    });


    self.incrementQuantity = function () {

        saveQtyToBacked(self.quantity() + 1)

    }
    self.decrementQuantity = function (event) {
        self.sellerID = event.sellerId;
        if (self.quantity() - 1 == 0) {
            swal({
                title: "Are you sure?",
                text: "This will delete the  item from the cart!",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "Yes, delete it!"
            }, function () {
                saveQtyToBacked(self.quantity() - 1);

                ko.postbox.publish('MANAGE_PAYMENT_OPTION',{sellerID : self.sellerID});
            });
        } else {
            saveQtyToBacked(self.quantity() - 1);
        }
    }

};


// CLASS ViewModel
var CartViewModel = function () {
    var self = this; // Scope Trick
    self.sellers = new Array();
    self.sellerID= ko.observable();
    self.isPortal = false;

    self.taxcharge = "";
    self.pickupTaxcharge="";

    self.width = ko.observable($(window).width());
    self.height = ko.observable($(window).height());

    self.screenSize = ko.computed(function () {
        switch (true) {
            case self.width() < 721:
            {
                return 'small';
                break;
            }
            case self.width() < 1001:
            {
                return 'medium';
                break;
            }
            default :
            {
                return "large";
                break;
            }
        }
    })


    /**
     * Observables
     */
    self.sales_tax = ko.observable(0);
    self.customerLocation = ko.observable();
    self.showcart = ko.observable(false);
    /**
     * Observable Arrays
     */
    self.cart = ko.observableArray();
    self.products = ko.observableArray();
    /**
     * Computed Observables
     */


    self.shipping = ko.computed(function () {
        var shiptotal = 0;
        $(self.cart()).each(function (index, cart_item) {
            shiptotal += cart_item.shippingCost();
        });
        return shiptotal;
    });
    self.warranty = ko.computed(function () {
        var wartotal = 0;
        $(self.cart()).each(function (index, cart_item) {
            wartotal += cart_item.warrantyCost();
        });
        return wartotal;
    });
    self.subtotal = ko.computed(function () {
        var subtotal = 0;
        $(self.cart()).each(function (index, cart_item) {
            // subtotal += (cart_item.cost() - cart_item.shippingCost());
            subtotal += cart_item.cost();
        });
        return subtotal ;
    });

    self.corecharge = ko.computed(function () {
        var corecharge = 0;
        $(self.cart()).each(function (index, cart_item) {
            corecharge += cart_item.corecharge();
        });
        return corecharge;

    });

    self.updateShippingLocation = function (lat, lng) {
        ko.utils.arrayForEach(self.cart(), function (item) {
            item.product().shipping().calculateAvailableServices(lat, lng);
        });
    }
    self.address = ko.observableArray();
    self.tax = function () {
        return ko.computed(function () {
            var total = 0;
            var shipping_option = '';
            $(self.cart()).each(function (index, cart_item) {
                shipping_option += cart_item.selectedShippingOption();
            });
            ko.utils.arrayForEach(self.sellers, function (item) {
                var value = parseFloat(self.sellerTax(item.id)().charge);
                var store_state=$('#storestate').val();
                var buyer_state = self.checkout.ShippingAddress().SelectedState();
                self.address = self.checkout.ShippingAddress();
                self.sellerID=self.sellers[0].id;
                    if(self.address.PostalCode() && buyer_state && self.address.City()){
                        if(store_state && buyer_state.trim() === store_state.trim() && shipping_option=="In Store Pickup"){
                            if (!isNaN(value)) {
                                total =total+ value;
                            }
                        }
                        else if(store_state && buyer_state.trim() != store_state.trim() && shipping_option=="In Store Pickup"){
                            if (!isNaN(value)) {
                                total =total+ value;
                            }
                        }
                        else if(store_state && buyer_state.trim() === store_state.trim() && shipping_option != "In Store Pickup"){
                            if (!isNaN(value)) {
                                total =total+ value;
                            }
                        }
                        /*else if(item.state && seller_state.trim() != item.state.trim() && shipping_option != "In Store Pickup"){
                            if (!isNaN(value)) {
                                total =total+ value;
                            }
                        }*/
                    }

            });
            return {"desc": "Tax:", "charge": total};
        })
    };

     self.cartitemCalc = ko.computed(function(){
        var payOptions=[];
        var storeType=$('#storetype').val();
        $(self.cart()).each(function (index, cart_item) {
            if(storeType==="PORTAL"){
                if(self.cart().length > 1){
                        payOptions.push("creditcard");
                        window.CartViewModel.checkout.SetPaymentOptions(payOptions);
                }else{
                    var paymentType=[];
                        $.ajax({
                            url: "/getpaymentmethods.wws",
                            method: "POST",
                            async: false,
                            data: "sellerid="+cart_item.sellerId,
                            success: function(data){
                                var response= JSON.parse(data);
                                if (response.success) {
                                    if(response.message.length>1){
                                        paymentType=response.message.split(',');
                                    }
                                    paymentType.forEach(function(item,index){
                                       switch(item){
                                            case 'CC' : {
                                                payOptions.push("creditcard");
                                                break;
                                            }
                                             case 'PP' : {
                                                payOptions.push("paypal");
                                                break;
                                            }
                                            case 'PI' : {
                                                payOptions.push("pi");
                                                break;
                                            }
                                        }
                                    });
                                 window.CartViewModel.checkout.SetPaymentOptions(payOptions);
                                }
                            },
                            error: function(error) {

                            }
                        });
                    }
            }else if(storeType==="B2C"){
                payOptions.push("pi");
                window.CartViewModel.checkout.SetPaymentOptions(payOptions);
            }

        });
     });
    /*self.total = ko.computed(function () {
        return self.shipping() + (self.subtotal() - (self.discount_value || 0))+ self.tax()().charge + self.corecharge() + self.warranty();
    });*/
    // self.actualTotal = ko.observable(0);
    // self.DiscountedTotal = ko.observable(0);
    self.discount_amount= ko.observable(0);
    self.discountid=ko.observable();
    self.actualTotal = ko.observable(0);
    self.total = ko.computed({
        read : function(){
            var DiscountedTotal = 0;
            DiscountedTotal  = self.shipping() + self.subtotal()+ self.tax()().charge + self.corecharge() + self.warranty();
            self.actualTotal(DiscountedTotal);
            return DiscountedTotal
        },
        write : function(discount_value){
            var DiscountedTotal = 0;
            DiscountedTotal  = self.shipping() + (self.subtotal() - discount_value || 0) + self.tax()().charge + self.corecharge() + self.warranty();
            self.actualTotal(DiscountedTotal);
            return DiscountedTotal;
        }
    });
    // self.actualTotal(self.total());

    /*self.pretotal = ko.computed(function () {
        var pretotal = 0;
        var dis =
        pretotal = self.shipping() + (self.subtotal() - ) + self.tax()().charge + self.corecharge() + self.warranty()
        return pretotal;
    });*/

    /*self.total = ko.computed({
        read: function () {
            debugger
            self.actualTotal = self.shipping() + self.subtotal() + self.tax()().charge + self.corecharge() + self.warranty();
            return self.actualTotal;
            // return self.pretotal();
        },
        write: function (discount_value,discountid) {
            debugger
            self.discount_amount= discount_value;
            self.discountid=discountid;
            self.DiscountedTotal  = self.shipping() + (self.subtotal() - discount_value || 0) + self.tax()().charge + self.corecharge() + self.warranty();
            return self.DiscountedTotal;
        },
        owner: self
    });*/

    self.setCustomerLocation = function (custloc) {
        if (custloc) {
            try{

            custloc.lng=custloc.lon;
            self.customerLocation(custloc);
            } catch(oerr){}
        }
    }

    self.sellerSubtotal = function (sellerId) {
        var sellerCart = self.itemsBySellerId(sellerId);
        return ko.computed(function () {
            var subtotal = 0;
            $(sellerCart).each(function (index, cart_item) {
                subtotal += (cart_item.cost() - cart_item.shippingCost());
            });
            return subtotal;
        });
    }

    self.sellerCoreCharge = function (sellerId) {
        var sellerCart = self.itemsBySellerId(sellerId);
        return ko.computed(function () {
            var corecharge = 0;
            $(sellerCart).each(function (index, cart_item) {
                corecharge += cart_item.corecharge();
            });
            return corecharge;
        });
    }

    function hasTax(seller) {

        try {

            if (!seller || seller.hasOwnProperty("tax") == false) {
                return false;
            }

                self.taxcharge = 0;
                self.taxcharge.sellerid = seller.id;
                self.pickupTaxcharge = 0;
                self.pickupTaxcharge.sellerid = seller.id;


            // check if the saved tax is for this client and the state is the same
            if (self.taxcharge && self.taxcharge.sellerid == seller.id && self.taxcharge.state === self.checkout.ShippingAddress().SelectedState()) {
                return true;
            }


            var tax = ko.utils.arrayFirst(seller.tax, function (item) {
                return item.state === self.checkout.ShippingAddress().SelectedState();
            });




            if (tax) {
                self.taxcharge = tax;
                self.taxcharge.sellerid = seller.id;
                self.pickupTaxcharge = tax;
                self.pickupTaxcharge.sellerid = seller.id;
                return true;
            } else {
                if(seller.tax.length>0) {
                self.pickupTaxcharge = seller.tax[0];
                self.pickupTaxcharge.sellerid = seller.id;
                return true;
                }
            }

        } catch (oerr) {

        }

        return false;

    }
    self.sellerShipping = function (sellerId) {
        var sellerCart = self.itemsBySellerId(sellerId);
        return ko.computed(function () {
            var shiptotal = 0;
            $(sellerCart).each(function (index, cart_item) {
                shiptotal += cart_item.shippingCost();
            });
            return shiptotal;
        });
    }
    self.sellerTotal = function (sellerId) {
        var sellerCart = self.itemsBySellerId(sellerId);
        return ko.computed(function () {
            var total = 0;
            $(sellerCart).each(function (index, cart_item) {
                total += cart_item.cost() + cart_item.shippingCost() + cart_item.corecharge();
            });
            return total + self.sellerTax(sellerId)().charge;
        });
    }


    self.sellerTax = function (sellerId) {
        return ko.computed(function () {

            var seller = ko.utils.arrayFirst(self.sellers, function (item) {
                return item.id === sellerId;
            })

          if (hasTax(seller) == false) {
                return {charge: 0};
            }

            var rate = 0;
            var ratePickup = 0;
            var taxdesc = "Tax:";
            if (self.taxcharge && self.taxcharge.charges.length >= 1) {
                rate = self.taxcharge.charges[0].rate || 0;
                taxdesc = "Tax " + self.taxcharge.charges[0].desc + ":" || "Tax:";
            }

                var Store_taxrate=$('#statetax1').val()*100;


             if (self.pickupTaxcharge && self.pickupTaxcharge.charges.length >= 1) {
                //ratePickup = self.pickupTaxcharge.charges[0].rate || 0;
                ratePickup = Store_taxrate|| 0;
                taxdescPickup = "Tax " + self.pickupTaxcharge.charges[0].desc + ":" || "Tax:";
            }


            var total = 0;
            var totalPickup=0;
            var grand_total=0;
            var sellerCart = self.itemsBySellerId(sellerId);
            $(sellerCart).each(function (index, cart_item) {
                var ship_taxable=$('#shiptaxabl').val();
                var core_taxable=$('#coretaxabl').val();

                if(core_taxable =="T" && ship_taxable =="T"){
                     totalPickup = totalPickup + cart_item.cost()+cart_item.corecharge()+cart_item.shippingCost();
                }
                else if(core_taxable =="T"){
                   totalPickup = totalPickup + cart_item.cost()+cart_item.corecharge()
                }
                else if(ship_taxable =="T"){
                   totalPickup = totalPickup + cart_item.cost()+cart_item.shippingCost();
                }else {
                    totalPickup = totalPickup + cart_item.cost();
                                    }
               /* if(cart_item.selectedShippingOption()=="In Store Pickup"){
                 totalPickup = totalPickup+cart_item.cost() + cart_item.shippingCost() + cart_item.corecharge();
                } else {
                total = total+ cart_item.cost() + cart_item.shippingCost() + cart_item.corecharge();
            }*/
            });

            return {
                "desc": taxdesc,
                "charge": (Math.round(total * rate) +Math.round(totalPickup * ratePickup))/ 100
            };
        });
    }

    ko.postbox.subscribe('REMOVABLE_LINE_ITEM', function (cart_item) {
        try {
            self.removeFromCart(cart_item);
        } catch (oerr) {
        }
    });

    /**
     * Actions
     */
          self.cartNew = [];

    self.addToCart = function (product, qty, selectedShippingOption, selectedWarrantyOption, seller,sendChanges) {
        // Instantiate a new CartItem object using the passed
        // in `Product` object, and then set a quantity of 1.
        var cart_item = new CartItem(product, qty, selectedShippingOption, selectedWarrantyOption, seller.id||seller);

        if (sendChanges) {
            cart_item.sendChangesToBackend(true);
        }
        // Add the CartItem instance to the self.cart (Observable Array)
        self.cart.push(cart_item);
                self.cartNew.push(cart_item);

    };

    self.removeFromCart = function (cart_item) {
        self.cart.remove(cart_item);
    }

    self.addSeller = function (seller, taxcharges) {
        if (!self.isPortal) {
            seller.id = "XZWZ";
        }

        if (!ko.utils.arrayFirst(self.sellers, function (item) {
                return item.id === seller.id;
            })) {
            if (taxcharges) {
                jQuery.extend(seller, taxcharges);
            }
            self.sellers.push(seller);
        }
    }
    // return items belonging to a specific seller
    self.itemsBySellerId = function (sellerId) {
        return ko.utils.arrayFilter(self.cart(), function (cartitem) {
            return (self.isPortal ? cartitem.sellerId === sellerId : true);
        });
    };


    ko.postbox.subscribe('SHIPPING_GEOLOCATION_CHANGED', function (objval) {
        if (objval && objval.hasOwnProperty("lat") && objval.hasOwnProperty("lng")) {
           self.updateShippingLocation(objval.lat, objval.lng);
        }
    });

    ko.postbox.subscribe('READY_TO_PLACE_ORDER', function (val) {


        var cartitems = [];
        ko.utils.arrayForEach(self.cart(), function (cart_item) {
            cartitems.push({
                "id": cart_item.product().id(), "qty": cart_item.quantity(), "frgservice": cart_item.selectedShippingOption(),
                "shipping": cart_item.shippingCost(), "wroption": cart_item.selectedWarrantyOption(),
                "warranty": cart_item.warrantyCost(), "tax": self.sellerTax(cart_item.sellerId)
            });
        });




        var addresses = {
            "email": self.checkout.CustomerEmail(), "shipto": self.checkout.ShippingAddress().exportToJS(),
            "billto": (self.checkout.BillingSameAsShipping() == true ? self.checkout.ShippingAddress().exportToJS() : self.checkout.BillingAddress().exportToJS()),
            "billtoshipto": self.checkout.BillingSameAsShipping()
        };

        var ordertotal = {"subtotal": self.subtotal(), "tax": self.tax(), "discountid":typeof(self.discountid)==="function" ?  '' : self.discountid, "discount": typeof(self.discount_amount)==="function" ? 0 : self.discount_amount, "shipping": self.shipping(), "core": self.corecharge(), "total": self.DiscountedTotal > 0 ? self.DiscountedTotal : self.actualTotal}

        var order = {};
        order.cartitems = cartitems;
        order.addresses = addresses;
        order.payment = self.checkout.Payment();
        order.ordertotal = ordertotal;


        var jsondata = ko.toJSON(order, function (key, value) {

            if (/sellers|^Countries$|^States$|height|width|screenSize|showcart|checkoutsteps/.test(key)) {
                return;
            }
            else {
                return value;
            }
        });
        jQuery.blockUI({message: '<h3><i class="fa fa-circle-o-notch fa-spin" style="width: auto"></i> Please wait.Saving your order...</h3>'});
        $.ajax({
            url: "saveorderajax.wws",
            method: "POST",
            data: {data: jsondata, invpk: self.checkout.InvoicePk(), custpk: self.checkout.CustomerPk},
            context: self,
            success: function (data) {

                if (!data.success) {
                    var msg = (data.message || "An Error Occured. Order was not placed!");
                    jQuery.unblockUI();
                    swal({text: msg, html: true, type: "error", "title": "OOPS"});
                } else {
                    window.location.href = data.data.nextstep||"screenconfirmation.wws";
                }
            },
            error: function (data) {
                jQuery.unblockUI();
                swal({text: "There was an error while sending the order to the server. Please retry!", type: "error", "title": "OOPS"});
            },
            dataType: 'json'
        });
    });
    ko.postbox.subscribe('GET_DISCOUNT', function (data) {
        self.total(data.response)
    });
    ko.postbox.subscribe('MANAGE_PAYMENT_OPTION', function (data) {
        var i = -1;
        $.each(self.sellers,function(index,value){
            if(value.id === data.sellerID){
                 i = index;
            }
        });
        if(i>-1){
            self.sellers.splice(i,1);
        }
        self.cartitemCalc();
    });
};
