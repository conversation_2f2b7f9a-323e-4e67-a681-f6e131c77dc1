var vinletters = new Array();
vinletters[1] = "AJ";
vinletters[2] = "BKS";
vinletters[3] = "CLT";
vinletters[4] = "DMU";
vinletters[5] = "ENV";
vinletters[6] = "FW";
vinletters[7] = "GPX";
vinletters[8] = "HY";
vinletters[9] = "RZ";

//--------------12345678901234567
var vinweight = "8765432T098765432";

function valvin(serie) {
    if (serie.length == 17) {
        suma = 0;
        for (i = 0; i < serie.length; i++) {
            nextchar = serie.charAt(i);
            for (j in vinletters) {
                if (vinletters[j].indexOf(nextchar) != -1) {
                    nextchar = j;
                }
            }
            val = parseInt(nextchar);
            weight = vinweight.charAt(i);
            if (weight == "T") {
                weight = 10;
            } else {
                weight = parseInt(weight);
            }
            suma += val * weight;
        }
        controlchar = suma % 11;
        if (controlchar == 10) controlchar = "X";
        if (controlchar == serie.charAt(8)) {
            return true;
        } else {
            return false;
        }
    } else {
        return false;
    }
    return false;
}

function validateVin(vin) {
    return validate(vin.toUpperCase());

    function transliterate(c) {
        return '0123456789.ABCDEFGH..JKLMN.P.R..STUVWXYZ'.indexOf(c) % 10;
    }

    function get_check_digit(vin) {
        var map = '0123456789X';
        var weights = '8765432X098765432';
        var sum = 0;
        for (var i = 0; i < 17; ++i)
            sum += transliterate(vin[i]) * map.indexOf(weights[i]);
        return map[sum % 11];
    }

    function validate(vin) {
        if (vin.length !== 17) return false;
        return get_check_digit(vin) === vin[8];
    }

}

function detectVehicleinURL() {

    path = window.location.pathname;
    reg_veh = new RegExp(/vehicle\/(\w+)\/(\w+)\/(\w+)\/(\w+)/i);
    if (reg_veh.test(path)) {
        matching = path.match(reg_veh);
        params = new URLSearchParams(location.search);
        return {
            make: matching[1],
            year: matching[2],
            model: matching[3],
            engine: params.get('engine') || 'ALL',
            submodel: params.get('submodel') || 'ALL'
        }

    }
    return false;
}

function showPosition(position) {
    lat = position.coords.latitude;
    longi = position.coords.longitude;
    window.sophiouserlat = position.coords.latitude;
    window.sophiouserlong = position.coords.longitude;
    timestamp = position.timestamp;
    now = new Date();
    if (document.cookie.split(';').filter(function (item) {
        return item.indexOf('locationset=1') >= 0
    }).length) {
    } else {
        $.ajax({
            type: "get",
            data: "lat=" + lat + "&long=" + longi + "&timestamp=" + timestamp + "&now=" + now,
            url: "/setlatlong.wws",
            success: function (data) {
                var date = new Date();
                date.setTime(date.getTime() + (24 * 60 * 60));
                expires = "; expires=" + date.toUTCString();
                document.cookie = 'locationset=1' + expires + ';path=/';
            },
            error: function (jqXHR, textStatus, errorThrown) {
                request.abort();
            }
        });
    }
    if (typeof callAfterGeo === "function") {
        callAfterGeo();
    }
}
