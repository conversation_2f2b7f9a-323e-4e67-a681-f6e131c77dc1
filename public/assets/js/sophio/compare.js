class Compare {
    constructor() {
        this.itemsAllowed=4;
       this.start();
    }
    start()
    {

        if(localStorage.getItem('sophio_compare')) {
            this.items = JSON.parse(localStorage.getItem('sophio_compare'));
        }else{
            this.items = [];
        }

    }
    isFull()
    {
        if(this.items.length==this.itemsAllowed) {
            return true;
        }else{
            return false;
        }
    }
    remove(id)
    {
        this.items = this.items.filter(function(item) {
            return item.id !== id
        });
        $('.product-comparison-item[data-id="'+id+'"]').remove();

        if($('.product-comparison-item').length==0) {
            this.hide();
        }
        this.save();
        var l =[];
        this.items.forEach(function(item){  l.push(item.slug);});
        $('#compare-link').attr('href','/compare?parts='+l.join(','));
    }
    add(el)
    {
        if(this.isFull()) {
            this.show();
            return false;
        }
       var  item = el.getAttribute('data-partmaster-id');
        if(this.items.includes(item)) {
            this.show();
            return false;
        }
        for (var i = 0; i < this.items.length; i++) {
            if(this.items[i].id==item) {
                 this.show();
                return false;
            }
        }
            this.items.push({id:item,image_url:$('#p__'+item+'img').attr('src'),
                slug:  el.getAttribute("data-part-slug"),
                name:el.getAttribute("data-part-name")});
        this.save();
        this.hide();
        this.show();
    }
    getAll()
    {
        return this.items;
    }
    save()
    {
        localStorage.setItem('sophio_compare',JSON.stringify(this.items))
    }
    empty()
    {
        this.items=[];
        this.save();
    }
    show()
    {
        var l =[];
        $('#product-comparison-list').empty();
        this.items.forEach(function(item){
            $('#product-comparison-list').append(
                `                <div class="col product-comparison-item" data-id="${item.id}">\n` +
                `                    <div class="animate-underline hover-effect-opacity">\n` +
                `                        <div class="position-relative mb-3">\n` +
                `<button type="button"   class="  btn btn-icon btn-secondary animate-pulse fs-base bg-transparent border-0 position-absolute top-0 end-0 z-2 mt-1 mt-sm-2 me-1 me-sm-2" >\n` +
                `                          <i class="ci-close remove-item animate-target" data-id="${item.id}"></i>\n` +
                `                        </button>`+
                `                            <a class="d-flex bg-body-tertiary rounded p-3" href="#">\n` +
                `                                <div class="ratio" style="--cz-aspect-ratio: calc(240 / 258 * 100%);height:150px;" >\n` +

                `                                    <img src="${item.image_url}"  style="object-fit: contain" >\n` +
                `                                </div>\n` +
                `                            </a>\n` +
                `                        </div>\n` +
                `                        <div class="nav mb-2">\n` +
                `                            <a class="nav-link animate-target min-w-0 text-dark-emphasis p-0" href="#!">\n` +
                `                                <span class="text-truncate">${item.name}</span>\n` +
                `                            </a>\n` +
                `                        </div>\n` +
                `                    </div>\n` +
                `                </div>`);

            l.push(item.slug);
        });
        var my = this;
        $('.remove-item').on('click',function(e) {
            my.remove($(e.target).attr('data-id'));
        })
        $('#compare-link').attr('href','/compare?parts='+l.join(','));
        $('#compareCanvas').offcanvas('show');
    }
    hide()
    {
        $('#product-comparison-list').html('');
        $('#compareCanvas').offcanvas('hide');
    }
}
