var vinletters = new Array();
vinletters[1] = "AJ";
vinletters[2] = "BKS";
vinletters[3] = "CLT";
vinletters[4] = "DMU";
vinletters[5] = "ENV";
vinletters[6] = "FW";
vinletters[7] = "GPX";
vinletters[8] = "HY";
vinletters[9] = "RZ";

//--------------12345678901234567
var vinweight = "8765432T098765432";

function valvin(serie) {
    if (serie.length == 17) {
        suma = 0;
        for (i = 0; i < serie.length; i++) {
            nextchar = serie.charAt(i);
            for (j in vinletters) {
                if (vinletters[j].indexOf(nextchar) != -1) {
                    nextchar = j;
                }
            }
            val = parseInt(nextchar);
            weight = vinweight.charAt(i);
            if (weight == "T") {
                weight = 10;
            } else {
                weight = parseInt(weight);
            }
            suma += val * weight;
        }
        controlchar = suma % 11;
        if (controlchar == 10) controlchar = "X";
        if (controlchar == serie.charAt(8)) {
            return true;
        } else {
            return false;
        }
    } else {
        return false;
    }
    return false;
}

function validateVin(vin) {
    return validate(vin.toUpperCase());

    function transliterate(c) {
        return '0123456789.ABCDEFGH..JKLMN.P.R..STUVWXYZ'.indexOf(c) % 10;
    }

    function get_check_digit(vin) {
        var map = '0123456789X';
        var weights = '8765432X098765432';
        var sum = 0;
        for (var i = 0; i < 17; ++i)
            sum += transliterate(vin[i]) * map.indexOf(weights[i]);
        return map[sum % 11];
    }

    function validate(vin) {
        if (vin.length !== 17) return false;
        return get_check_digit(vin) === vin[8];
    }

}



function genAPIVehicleURL()
{
    let params = "";
    for (const p in sophio.api_parameters) {
        if(sophio.api_parameters[p]!=null) {
            params = params + p +"="+sophio.api_parameters[p]+"&";
        }

    }
    return sophio.api_base_url+"parts?"+params;
}
function showPosition(position) {
    lat = position.coords.latitude;
    longi = position.coords.longitude;
    window.sophiouserlat = position.coords.latitude;
    window.sophiouserlong = position.coords.longitude;
    timestamp = position.timestamp;
    now = new Date();
    if (document.cookie.split(';').filter(function (item) {
        return item.indexOf('locationset=1') >= 0
    }).length) {
    } else {
        $.ajax({
            type: "get",
            data: "lat=" + lat + "&long=" + longi + "&timestamp=" + timestamp + "&now=" + now,
            url: "/setlatlong.wws",
            success: function (data) {
                var date = new Date();
                date.setTime(date.getTime() + (24 * 60 * 60));
                expires = "; expires=" + date.toUTCString();
                document.cookie = 'locationset=1' + expires + ';path=/';
            },
            error: function (jqXHR, textStatus, errorThrown) {

            }
        });
    }
    if (typeof callAfterGeo === "function") {
        callAfterGeo();
    }
}
function UpdateQueryString(paramKey, paramVal, uri) {
    if (!uri) uri = window.location.href;
    var re = new RegExp("([?&])" + paramKey + "=[^&#]*", "i");
    if (re.test(uri)) {
        uri = uri.replace(re, '$1' + paramKey + "=" + paramVal);
    } else {
        var separator = /\?/.test(uri) ? "&" : "?";
        uri = uri + separator + paramKey + "=" + paramVal;
    }
    return uri;
}

function equalheight() {
    var maxHeight = 0;
    $('.addtocartform').each(function () {
        if ($(this).height() > maxHeight)
            maxHeight = $(this).height();
    });
    $('.addtocartform').each(function () {
        $(this).height(maxHeight);
    });
}
function buyOnclick(partunique) {

    if ($('#branchid_' + partunique).val() === "") {
        $('#branchid_' + partunique).toggleClass('error text-danger');

    } else {
        $('#mmbuy_' + partunique).attr('checked', true);
        return true;

    }
    return false;
}
String.prototype.trunc =
    function (n) {
        return this.substr(0, n - 1) + (this.length > n ? '&hellip;' : '');
    };
function createPaginatedTable() {

    $('#table-fitment1').each(function () {
        var currentPage = 0;
        var numPerPage = 100;
        var $table = $(this);
        $table.bind('repaginate', function () {
            $table.find('tbody tr').hide().slice(currentPage * numPerPage, (currentPage + 1) * numPerPage).show();
        });
        $table.trigger('repaginate');
        var numRows = $table.find('tbody tr').length;
        var numPages = Math.ceil(numRows / numPerPage);
        if(numPages>20) numPages=20;
        var $paginator = $('<div class="pull-right"></div>')
        var $pager = $('<ul class="pagination"></ul>');
        for (var page = 0; page < numPages; page++) {
            $('<li></li>').html('<a href="#">' + (page + 1) + '</a>').bind('click', {
                newPage: page
            }, function (event) {
                event.preventDefault();
                currentPage = event.data['newPage'];
                $table.trigger('repaginate');
                $(this).addClass('active').siblings().removeClass('active');

            }).appendTo($pager).addClass('clickable');
        }
        if (numRows > numPerPage) {
            $paginator.append($pager);
        }

        $paginator.insertBefore($table.parent()).find('li:first').addClass('active');
    });
}
function imgError(image) {
    image.onerror = "";
    image.src = "https://images-us1.sophio.com/vehicle/coming-soon.jpg";
    return true;
}

function rtsc_list(container,list,url,token)
{
    $(container).find('.sellprice_div').html('<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div>');
    $.ajax({
        url: url,
        timeout: 40000,
        cache: false,
        type: 'POST',
        dataType: 'json',
        contentType: "application/json; charset=utf-8",
        headers: {"X-CSRF-TOKEN": token},
        data: list,
        beforeSend: function () {
            $(container).find('.sellprice_div').html('<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div>');
        }
    }).done(function (response) {
        response.forEach(function (i) {
            var price = "";
            if (parseInt(i.sellprice) > 0) {
                price = '$' + i.sellprice;
                $(container).find('.sellprice_' + i.seq).each(function(){
                    if ($(this).attr('data-msrp') !== "0.0" && $(this).attr('data-msrp') !== "0" && $(this).attr('data-msrp') !== 0) {
                        price = price + '<del class=" fw-normal text-body-tertiary ms-2">' +$(this).attr('data-msrp') + '</del>';
                    }
                    $(this).html(price);
                });
            } else {

                $(container).find('.sellprice_' + i.seq).each(function(){
                    if($(this).attr('data-price')!=="0.0" && $(this).attr('data-price')!=="0") {
                        price = '$' + $(this).attr('data-price');
                        if ($(this).attr('data-msrp') !== "0.0" && $(this).attr('data-msrp') !== "0"&& $(this).attr('data-msrp') !== 0) {
                            price = price + '<del class=" fw-normal text-body-tertiary ms-2">' + $(this).attr('data-msrp') + '</del>';
                        }
                        $(this).html(price);
                    }

                });
            }

        })
    }).fail(function () {
        $(container).find('.sellprice_div').each(
            function () {
                $(this).html($(this).attr('sellprice'));
            }
        );
    });
}
