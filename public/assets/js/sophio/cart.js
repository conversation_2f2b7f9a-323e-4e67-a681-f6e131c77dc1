class Cart {
    data;
    noffcanvas;
    constructor() {
        this.noffcanvas =false;
    }
    empty()
    {
        $.ajax({
            type: 'GET',
            url: '/cart/empty',
        });
    }
    disableOffCanvas()
    {
        this.noffcanvas = true;
    }
    add(form) {
        $(form).find('button[type=submit]').html('<div class="spinner-border spinner-border-sm" role="status">\n' +
            '  <span class="visually-hidden">Loading...</span>\n' +
            '</div>');
        $.ajax({
            type: 'POST',
            url: $(form).attr('action'),
            data: $(form).serialize(),
            dataType: 'json',
            success: function (data) {
                cart.refreshOffCanvas(data);
                $(form).find('button[type=submit]').html(' <i class="ci-shopping-cart fs-base animate-target"></i>');
            }
        });
        return false;
    }
    updatemainqty(id,qty)
    {
        $.ajax({
            type: 'POST',
            url: '/cart/update',
            data: {
                "_token": csrf,
                "linenum": id,
                "quantity":qty,
                "absolute":true
            },
            success: function (data) {
                cart.refreshOffCanvas(data,false);
                var subtotal=0;
                data.forEach(item => {
                    subtotal = subtotal + item.quantity*item.price;
                    $(`#qty_`+item.id).html('$'+(+(item.quantity*item.price).toFixed(2)));
                });

                $(`#mcart_subtotal`).html('$'+(+subtotal.toFixed(2)));
                $(`#mcart_total`).html('$'+(+subtotal.toFixed(2)));
                $(`#mcart_count`).html(data.length);
            }
        });
    }
    updatemain(id,qty)
    {
        $.ajax({
            type: 'POST',
            url: '/cart/update',
            data: {
                "_token": csrf,
                "linenum": id,
                "quantity":qty
            },
            success: function (data) {
                cart.refreshOffCanvas(data,false);
                var subtotal=0;
                data.forEach(item => {
                    subtotal = subtotal + item.quantity*item.price;
                    $(`#qty_`+item.id).html('$'+(+(item.quantity*item.price).toFixed(2)));
                });

                $(`#mcart_subtotal`).html('$'+(+subtotal.toFixed(2)));
                $(`#mcart_total`).html('$'+(+subtotal.toFixed(2)));
                $(`#mcart_count`).html(data.length);
            }
        });
    }
    updateqty(id,qty,toggle=true)
    {
        $.ajax({
            type: 'POST',
            url: '/cart/update',
            data: {
                "_token": csrf,
                "linenum": id,
                "quantity":qty,
                "absolute":true
            },
            success: function (data) {

                cart.refreshOffCanvas(data,toggle);

            }
        });
    }
    update(id,qty,toggle=true)
    {
        $.ajax({
            type: 'POST',
            url: '/cart/update',
            data: {
                "_token": csrf,
                "linenum": id,
                "quantity":qty
            },
            success: function (data) {
                cart.refreshOffCanvas(data,toggle);

            }
        });
    }
    removemain(id)
    {
        $.ajax({
            type: 'DELETE',
            url: '/cart/remove',
            data: {
                "_token": csrf,
                "linenum": id
            },
            success: function (data) {

                cart.refreshOffCanvas(data,false);
                var subtotal=0;
                data.forEach(item => {
                    subtotal = subtotal + item.quantity*item.price;
                    $(`#qty_`+item.id).html('$'+(+(item.quantity*item.price).toFixed(2)));
                });

                $(`#mcart_subtotal`).html('$'+(+subtotal.toFixed(2)));
                $(`#mcart_total`).html('$'+(+subtotal.toFixed(2)));
                $(`#mcart_count`).html(data.length);
                $('#linenum_'+id).remove();
            }
        });
    }
    remove(id,toggle=true)
    {
        $.ajax({
            type: 'DELETE',
            url: '/cart/remove',
            data: {
                "_token": csrf,
                "linenum": id
            },
            success: function (data) {

                     cart.refreshOffCanvas(data,toggle);
            }
        });

    }
    refreshOffCanvas(cart_data,toggle=true) {
        $(`#cart_offcanvas`).empty();
        var subtotal=0;
        this.data = cart_data;
        $(`#cart_count`).html(cart_data.length);
        if(this.noffcanvas ==true) {
            return;
        }
        cart_data.forEach(item => {
            subtotal = subtotal + item.quantity*item.price;
            var img = item.image;
            if(item.image.indexOf('http')!== -1) {
                if(item.image.indexOf('images-us1')!== -1 && !(item.image.indexOf('original')!== -1)) {
                  var imgt = new URL(item.image);
                  img = 'https://images-us1.sophio.com/thumb'+ imgt.pathname;
                }
            }else{
                if(!(item.image.indexOf('original')!== -1)) {
                    img = 'https://images-us1.sophio.com/thumb'+item.image;
                }
            }
            console.log(this.noffcanvas);

            $(`#cart_offcanvas `).append(
                ` <div class="d-flex align-items-center" id="cart_linenum_${item.id}">\n` +
                `                <a class="flex-shrink-0  ratio ratio-1x1" style="max-width: 110px;max-height: 110px"  href="#!">\n ` +
                `                    <img\n ` +
                `                        src="${img}"\n ` +
                `                      style="object-fit: contain" alt="${item.partlabel}">\n ` +
                `                </a>\n ` +
                `                <div class="w-100 min-w-0 ps-2 ps-sm-3">\n ` +
                `                    <h5 class="d-flex animate-underline mb-2">\n ` +
                `                        <a class="d-block fs-sm fw-medium text-truncate animate-target"\n ` +
                `                           href="`+ "/" + sophio.routes['catalog.itemdetail'].replace('{mfg}', item.mfg_name_slug).replace('{sku}', item.part_number_slug)+
                        `">    ${item.mfgname}  ${item.partlabel}</a>\n ` +
                `                    </h5>\n ` +
                `                    <div class="h6 pb-1 mb-2">${item.price}</div>\n ` +
                `                    <div class="d-flex align-items-center justify-content-between">\n ` +
                `                        <div class="count-input rounded-2">\n ` +

                `                            <input type="number" class="form-control form-control-sm" value="${item.quantity}"\n style="width: 3rem;text-align: center; padding: 0 0.25rem;appearance: textfield;background-color:transparent" ` +
                `                                    onchange="cart.updateqty(${item.id},this.value)">\n ` +

                `                        </div>\n ` +
                `                        <button type="button" onclick="cart.remove(${item.id})" class="btn-close fs-sm" data-bs-toggle="tooltip"\n ` +
                `                                data-bs-custom-class="tooltip-sm" data-bs-title="Remove"\n ` +
                `                                aria-label="Remove from cart"></button>\n ` +
                `                    </div>\n ` +
                `                </div>\n ` +
                `            </div> `
            );
        });

        $(`#cart_subtotal`).html('$'+(+subtotal.toFixed(2)));

        if(cart_data.length>0) {
            $('#offcanvas-footer-cart').show( );
        }else{
            $('#offcanvas-footer-cart').hide( );
            $(`#cart_offcanvas `).append(
                ` <div class="d-flex align-items-center" id="cart_empty_line">\n` +
                `                <div class="w-100 min-w-0 ps-2 ps-sm-3">\n ` +
                `          <h5>Cart is empty!</h5>\n ` +
                `                </div>\n ` +
                `            </div> `

            );
        }
        if(toggle) {
            if(cart_data.length>0) {
                $('#shoppingCart').offcanvas('show');

            }else{
                $('#shoppingCart').offcanvas('hide');


            }
        }


    }
}
