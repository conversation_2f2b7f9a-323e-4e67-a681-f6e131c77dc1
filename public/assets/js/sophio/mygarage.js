class MyGarage {
    constructor() {
        if (MyGarage._instance) {
            return MyGarage._instance
        }
        MyGarage._instance = this;
        let garage_raw = localStorage.getItem('mygarage');
        if (garage_raw == null) {
            this.garage = {};
        } else {
            this.garage = JSON.parse(garage_raw);
        }
        let vehicle = this.detectVehicleinURL();

        if (vehicle != false) {
            if( vehicle.make + '-' + vehicle.model + '-' + vehicle.year in this.garage) {
                this.garage[vehicle.make + '-' + vehicle.model + '-' + vehicle.year] =Object.assign(this.garage[vehicle.make + '-' + vehicle.model + '-' + vehicle.year], vehicle);
            }else{
                this.garage[vehicle.make + '-' + vehicle.model + '-' + vehicle.year] = vehicle;
            }
            for(const a in this.garage) {
                if(a=="undefined-undefined-undefined") {
                   delete this.garage[a];
                }
            }
            this.save();
            this.setCurrent(vehicle);
        }


    }
    save()
    {

        localStorage.setItem('mygarage', JSON.stringify(this.garage));
    }
    delete(key)
    {
        delete this.garage[key];
    }
    destroy()
    {
        this.garage = {};
        this.save()

    }
    setCurrent(vehicle)
    {
        localStorage.setItem('current_vehicle',vehicle.make + '-' + vehicle.model + '-' + vehicle.year);


    }
    resetVehicle(vehicle)
    {
        this.garage[vehicle.make + '-' + vehicle.model + '-' + vehicle.year] = vehicle;
        this.setCurrent(vehicle);
        this.setCurrentCookie(vehicle);
        this.save();
    }
    setCurrentCookie(vehicle)
    {
        Cookies.set('current_vehicle',btoa( JSON.stringify(vehicle)));
    }
    set(key,vehicle) {
        this.garage[key] = vehicle;
    }
    detectVehicleinURL() {

       let  path = window.location.pathname;
        let reg_veh = new RegExp(/vehicle\/([\-\w]+)\/([\-\w]+)\/([\-\w]+)\/([\-\w]+)/i);
        let item_veh = new RegExp(/itemdetail\/(?:[\-\w]+)\/(?:[\-\w]+)\/([\-\w]+)\/([\-\w]+)\/([\-\w]+)/i)
        if (reg_veh.test(path) | item_veh.test(path)) {
            let matching = [];
            if (reg_veh.test(path)){
                matching = path.match(reg_veh);
            }else{
                matching = path.match(item_veh);
            }
            let params = new URLSearchParams(location.search);
            let veh =  {
                make: matching[1],
                year: matching[2],
                model: matching[3],
                engine: params.get('engine') || 'all',
                submodel: params.get('submodel') || 'all',
                values:{}
            }
            let attributes = ['AC', 'ASPIRATION', 'BEN_LENGTH', 'BED_TYPE', 'BODY_NUM_DOORS', 'SUB_MODEL',
                'BODY_TYPE', 'BRAKE_ABS', 'BRAKE_SYSTEM', 'CYLINDER_HEAD_TYPE', 'DRIVE_TYPE', 'ENGINE_DESIGNATION', 'ENGINE_VERSION',
                'ENGINE_VIN', 'ENGINE_MFR', 'FRONT_BRAKE_TYPE', 'FRONT_SPRING_TYPE', 'FUEL_DELIVERY_TYPE', 'FUEL_DELIVERY_SUBTYPE',
                'FUEL_SYSTEM_CONTROL_TYPE', 'FUEL_SYSTEM_DESIGN', 'FUEL_TYPE', 'IGNITION_SYSTEM_TYPE', 'LOAD_INDEX', 'MFR_BODY_CODE',
                'POWER_OUTPUT', 'REAR_BRAKE_TYPE', 'REAR_SPRING_TYPE', 'SPEED_RATING', 'STEERING_SYSTEM', 'STEERING_TYPE', 'TIRE_SIZE',
                'TRANSMISSION_CONTROL_TYPE', 'TRANS_ELEC_CONTROLLED', 'TRANSMISSION_BASE', 'TRANSMISSION_MFR', 'TRANSMISSION_MFR_CODE',
                'TRANSMISSION_NUM_SPEEDS', 'TRANSMISSION_TYPE', 'VALVES_PER_ENGINE', 'WHEEL_BASE'];
            for (const a in attributes) {
                if (params.get(attributes[a]) != null) {
                    veh[attributes[a]] = params.get(attributes[a]);
                }
            }

            return veh;
        }
        return false;
    }
    getVehicles() {
        return this.garage;
    }
    getCurrent()
    {
        let current = localStorage.getItem('current_vehicle');
        if(current!=null) {
            if(current in this.garage) {
                return this.garage[current];
            }
        }
        return false;
    }
    getCurrentKey()
    {
        return localStorage.getItem('current_vehicle');
    }
    hasVehicles() {
        return Object.keys(this.garage).length > 0;
    }

}
