BloodhoundData = {}
var oincrementdata = new Bloodhound({
    limit: 100,
    datumTokenizer: function (d) {
        return Bloodhound.tokenizers.whitespace(d.value);
    },
    queryTokenizer: Bloodhound.tokenizers.whitespace,
    sorter: function (a, b) {
        if (a.value > b.value)
            return -1;
        if (a.value < b.value)
            return 1;
        // a must be equal to b
        return 0;
    },
    dupDetector: function (remoteMatch, localMatch) {
        return (remoteMatch.value === localMatch.value);
    },

    remote: {
        url: sophio.api_base_url+'search/incrementalSearch?returntype=json&subsystem=te&clientId='+sophio.api_parameters.clientId+'&q=%QUERY',
        wildcard: '%QUERY',
        transform: function (response) {
            BloodhoundData = response;
            return response;
        }
    }
});
oincrementdata.initialize();


$('#hd-input-keyword-search').typeahead(
    {
        minLength: 3,
        highlight: true,
        limit:100,
        autoselect:true,
    }
    , {
        limit:100,
        name: 'oincrdata',
        displayKey: function (title) {
            let doc = new DOMParser().parseFromString(title.value, 'text/html');
            return doc.body.textContent || "";
        },
        templates: {
            suggestion: function (data) {

                if(data.type=='category') {
                    return '<div><p>' + data.value + '</p></div>';
                }else if(data.type=='subcategory') {
                    return '<div><p>' + data.value + '</p></div>';
                }else if(data.type=='parttype') {
                    return '<div><p>' + data.value + '</p></div>';
                }else if(data.type=='manufacturer') {
                    return '<div><p>' + data.value + '</p></div>';
                }else if(data.type=="separator"){
                        return data.value;
                }else if(data.type=='partnumber') {
                    return '<div><p>' + data.value + '</p></div>';
                } else if(data.type=='keyword') {
                    return '<div><p>' + data.value + '</p></div>';
                }else{

                    return '<div><p style="cursor:default">' + data.value + '</p></div>';
                }

            },
            pending: function(context) {
                return '<div class="pt-2">\n' +
                    '  <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>\n' +
                    '  Searching...\n' +
                    '</div>';
            },
            footer: function(context) {
                if(sophio.advanced_search===1) {
                   return 'For advanced search <a href="/advancedsearch?searchfor='+context.query+'"><strong class="tt-highlight">click here</strong>';
                }else{
               //     return 'For advanced search <a href="/search?searchfor='+context.query+'"><strong class="tt-highlight">click here</strong>';
                }

            },
            empty: function(context){
                //  console.log(1) // put here your code when result not found
                if(sophio.no_result_contact===1) {
                    $(".tt-dataset").html('<h5></h5><div class="tt-suggestion"><p>Could not find <strong class="tt-highlight">'+context.query+'</strong>, please try a different keyword or <a href="/contact?message=Looking for '+context.query+'"><strong class="tt-highlight">click here</strong></a></p>');
                }else if(sophio.advanced_search===1) {
                    $(".tt-dataset").html('<h5></h5><div class="tt-suggestion"><p>Could not find <strong class="tt-highlight">' + context.query + '</strong>, please try a different keyword or <a href="/advancedsearch?searchfor=' + context.query + '"><strong class="tt-highlight">search here</strong></a></p>');
                }else{
                    $(".tt-dataset").html('<h5></h5><div class="tt-suggestion"><p>Could not find <strong class="tt-highlight">' + context.query + '</strong>, please try a different keyword or <a href="/search?searchfor=' + context.query + '"><strong class="tt-highlight">search here</strong></a></p>');
                }
            }
        },
        source: oincrementdata.ttAdapter()
    }).on('typeahead:selected', function ($e, data) {
        $('#hd-input-keyword-search-increment').val(data.key);
        $('#hd-input-keyword-search').val(data.value);
        $('#hd-input-keyword-search').attr('searchtype', data.type);
    if (data.type == "separator") {
        return false;
    }
    if (data.type == "incremental") {
        $('#hd-input-keyword-search').attr('year', data.data.year);
        $('#hd-input-keyword-search').attr('make', data.data.make);
        $('#hd-input-keyword-search').attr('model', data.data.model);
        $('#hd-input-keyword-search').attr('parttype', data.data.parttype);

    } else if (data.type == "partnumber" && 'data' in data && 'manufacturer' in data.data) {
        $('#hd-input-keyword-search').attr('manufacturer', data.data.manufacturer);
    }else if('data' in data && 'category' in data.data) {
        $('#hd-input-keyword-search').attr('category', data.data.category);
    }else if('data' in data && 'subcategory' in data.data) {
        $('#hd-input-keyword-search').attr('subcategory', data.data.subcategory);
    }else if('data' in data && 'parttype' in data.data) {
        $('#hd-input-keyword-search').attr('parttype', data.data.parttype);
    }
    else if('data' in data && 'manufacturer' in data.data) {
        $('#hd-input-keyword-search').attr('manufacturer', data.data.manufacturer);
    }else{

    }
    $('#frm-header-search-vin').trigger('submit');
}).on('typeahead:rendered',function(e){
    $('.tt-dataset').find('.separator').removeClass('tt-selectable').removeClass('tt-suggestion');;
});


$(document).ready(function () {


    var $inputkwsearch = $("#hd-input-keyword-search");
    // $inputkwsearch.width($("#frm-catalog-search").innerWidth()-(10+$("#frm-catalog-search .btn-catalog-search").outerWidth()+($inputkwsearch.outerWidth()-$inputkwsearch.innerWidth())));


    $('#frm-header-search-vin').submit(function () {

        var keyword = $('#frm-header-search-vin').find("input[name='keyword']").val();

        var incremental = $('#hd-input-keyword-search-increment').val();
        var url = this.action;
        console.log($('#hd-input-keyword-search').attr('searchtype'));
        if ($('#hd-input-keyword-search').attr('searchtype') == 'incremental') {
            url = url + $('#frm-header-search-vin').find("input[name='keyword']").val() + "?searchtype=incremental";
            if ($('#hd-input-keyword-search').attr('parttype')) {
                url = '/vehicle/' + $('#hd-input-keyword-search').attr('make') + '/' + $('#hd-input-keyword-search').attr('year') + '/' + $('#hd-input-keyword-search').attr('model') + '/' + $('#hd-input-keyword-search').attr('parttype');

            }
            $(location).attr('href', url);
        } else if ($('#hd-input-keyword-search').attr('searchtype') == 'partnumber') {
            url = url +'?searchfor='+ encodeURIComponent($('#frm-header-search-vin').find("input[name='incrementkeyword']").val()) + "&searchtype=partnumber";
            if ($('#hd-input-keyword-search').attr('manufacturer')) {
                url = "/itemdetail/"+$('#hd-input-keyword-search').attr('manufacturer')+'/'+ $('#hd-input-keyword-search-increment').val();

            }else{
                url = url + "&manufacturer=" + $('#hd-input-keyword-search').attr('manufacturer');
            }
            $(location).attr('href', url);
        }else if ($('#hd-input-keyword-search').attr('searchtype') == 'category') {

            $(location).attr('href', '/category/'+$('#hd-input-keyword-search').attr('category'));
        }else if ($('#hd-input-keyword-search').attr('searchtype') == 'parttype') {

            $(location).attr('href', '/parttype/'+$('#hd-input-keyword-search').attr('parttype'));
        }else if ($('#hd-input-keyword-search').attr('searchtype') == 'keyword') {

            $(location).attr('href', '/search?searchfor='+$('#frm-header-search-vin').find("input[name='keyword']").val() );
        }else if ($('#hd-input-keyword-search').attr('searchtype') == 'manufacturer') {

            $(location).attr('href', '/brand/'+$('#hd-input-keyword-search').attr('manufacturer'));
        }   else {
            var vin = $('#frm-header-search-vin').find("input[name='keyword']").val();
            if (!validateVin(vin)) {
                if (BloodhoundData.length > 0) {
                    var first = BloodhoundData[0];
                    if (first.type == "incremental") {
                        url = url +'?searchfor='+ encodeURIComponent(first.key)+ "&searchtype=incremental";
                        if (first.hasOwnProperty('data')) {
                            url = '/vehicle/' + first.data.make + '/' + first.data.year + '/' + first.data.model + '/' + first.data.parttype;
                        }
                        $(location).attr('href', url);
                    }else if(first.type=="vehicle"){
                        url = url  +'?searchfor='+  $('#frm-header-search-vin').find("input[name='incrementkeyword']").val() + "&searchtype=vin";
                        $(location).attr('href', url);
                    } else {
                        url = url + first.key + "?searchtype=partnumber";
                        $(location).attr('href', url);
                    }
                } else {
                    $(location).attr('href', '/search?searchfor='+$('#frm-header-search-vin').find("input[name='keyword']").val() );
                    //$('#smart-search-modal').modal();
                }
            } else {
                url = url  +'?searchfor='+  $('#frm-header-search-vin').find("input[name='keyword']").val() + "&searchtype=vin";
                $(location).attr('href', url);
            }
        }
        return false;
    });
});

function doNormalSearch() {
    url = $('#frm-header-search-vin').attr('action') + $('#frm-header-search-vin').find("input[name='keyword']").val();
    $(location).attr('href', url);
}
