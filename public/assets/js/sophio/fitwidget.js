class FitWidget {
    constructor(part,el,onSuccces,settings) {
        this.part = part;
        this.el = el;
        this.settings = settings;
        this.onSuccces = onSuccces;
        this.SearchVM = new SearchViewModel();


    }

    initFitWidget() {
        let cb = this;
        if(mygarage.hasVehicles()) {
            let cars = mygarage.getVehicles()
            $('#fit-locator-choose-vehicle').show();
            $('#fit-locator-no-vehicle').hide();
            let ul = document.createElement('ul');
            ul.className="nav  nav-stacked";
           for(const car in cars) {
                let li = document.createElement('li');
                let a = document.createElement('a');
                a.href="javascript:;";
                a.addEventListener('click',function(){cb.checkGarage(cars[car].make,cars[car].year,cars[car].model,cars[car].engine)});
                a.className="text-uppercase";


                a.innerHTML= cars[car].make+" "+cars[car].model+" " +cars[car].year;
                li.append(a);
                ul.append(li);
           }
           let div = document.getElementById('fit-locator-cars');
           div.innerHTML = "";
           div.append(ul);
        }else{
            $('#fit-locator-no-vehicle').show();
            $('#fit-locator-choose-vehicle').hide();
        }
        this.SearchVM.baseAjaxURL =   sophio.api_base_url+"vehicle?clientId="+sophio.api_parameters.clientId+"&nomfgcode="+sophio.api_parameters.nomfgcode+"&regionid="+sophio.api_parameters.regionId;
        this.SearchVM.baseSubmitURL = "";
        this.SearchVM.hasPartTypes = false;

        this.SearchVM.profile("checkFit")
        this.SearchVM.loadYears({'smbeng': 'submodels','mode':'ymm', 'usestatic': true, 'onlymake': sophio.website_parameters.onlymakes});
        if(this.settings.questions==0) {
            this.SearchVM.showParts = function () {
                this.addonFilter = jQuery.extend({
                    "checkfit": "y",
                    "make": this.selectedMake(),
                    "year": this.selectedYear(),
                    "model": this.selectedModel(),
                    "engine": this.selectedEngine()

                }, this.getAddOnFilters());

                //checkForFitment(this.baseSubmitURL + this.getPartsAddOnFilters());
                let checkfit_route = "/"+sophio.routes['catalog.checkfit'].replace('{productSku}',cb.part.product_sku )
                    .replace('{year}',this.selectedYear() ).replace('{make}',this.selectedMake() ).replace('{model}',this.selectedModel() ).replace('{engine?}',this.selectedEngine() );

                let urlCheckFitment =  sophio.api_base_url +"checkFitment/" + cb.part.product_sku + "/" + this.selectedYear() + "/" + this.selectedMake() + "/" + this.selectedModel() + "/" + this.selectedEngine();
                cb.year = this.selectedYear();
                cb.make = this.selectedMake();
                cb.model = this.selectedModel();
                cb.engine = this.selectedEngine();
                cb.checkForFitment(checkfit_route);
            }
        }else{
            this.SearchVM.showParts = function () {
                this.addonFilter = jQuery.extend({
                    "checkfit": "y",
                    "make": this.selectedMake(),
                    "year": this.selectedYear(),
                    "model": this.selectedModel(),
                    "engine": this.selectedEngine(),
                    "submodel": this.selectedSubModel()
                }, this.getAddOnFilters());

                //checkForFitment(this.baseSubmitURL + this.getPartsAddOnFilters());
                let checkfit_route = "/"+sophio.routes['catalog.checkfit'].replace('{productSku}',cb.part.product_sku )
                    .replace('{year}',this.selectedYear() ).replace('{make}',this.selectedMake() ).replace('{model}',this.selectedModel() ).replace('{engine?}',this.selectedEngine() );

                let urlCheckFitment =  sophio.api_base_url +"checkFitment/" + cb.part.product_sku + "/" + this.selectedYear() + "/" + this.selectedMake() + "/" + this.selectedModel() + "/" + this.selectedEngine();
                cb.year = this.selectedYear();
                cb.make = this.selectedMake();
                cb.model = this.selectedModel();
                cb.engine = this.selectedEngine();
                cb.submodel = this.selectedSubModel();
                cb.checkForFitment(checkfit_route);
            }
        }




        ko.cleanNode(document.getElementById("product-fit-locator"));
        ko.applyBindings(this.SearchVM, document.getElementById("product-fit-locator"));
        $('#widget-product-fit-locator .part-title').html(this.part.part_label);
        $('#widget-product-fit-locator .part-number-title strong').html(this.part.part_number);
        $('#widget-product-fit-locator .manufacturer-title  strong').html(this.part.mfg_name);
        $('#widget-product-fit-locator').modal({
            show: true
        });

    }

    checkGarage(cMake, cYear, cModel,cEngine) {
        cMake = cMake.toLowerCase();
        cYear = cYear.toLowerCase();
        cModel = cModel.toLowerCase();
        cEngine = cEngine.toLowerCase();
        let cb = this;
        let checkfit_route = "/"+sophio.routes['catalog.checkfit'].replace('{productSku}',cb.part.product_sku )
            .replace('{year}',cYear ).replace('{make}',cMake).replace('{model}',cModel).replace('{engine?}',cEngine);

        let urlCheckFitment =  sophio.api_base_url +"checkFitment/" + cb.part.product_sku + "/" + cYear+ "/" + cMake + "/" + cModel+ "/" + cEngine;
        cb.year = cYear;
        cb.make = cMake;
        cb.model = cModel;
        cb.engine = cEngine;
        cb.checkForFitment(checkfit_route);
    }
    checkForFitment(url) {
        let part = this.part;
        toastr.info("Fitment Checking in Process....", "One moment", {timeOut: 0, closeButton: true});




        let cb = this;
        $.ajax({
            url: url,
            timeout: 40000,
            cache: false,
            type: 'get',
            beforeSend: function () {
                $.LoadingOverlay("show")
            }
        })
            .always(function () {
                $.LoadingOverlay("hide");
                toastr.clear();
            })
            .done(function (response) {
               // cb.initFitWidget();
                if (response) {
                    if (response.fits=="OK") {
                        let cMessage = "This <strong>" + cb.part.taxonomy.parttype.name + "</strong> part does fit the <strong>" + cb.year + ' '+cb.make.replace('-',' ').toUpperCase()+' '+cb.model.replace('-',' ').toUpperCase()
                            + "</strong> vehicle!";
                        swal({
                            title: "Part does Fit!",
                            text: cMessage,
                            type: "success",
                            html: true
                        }, function () {cb.onSuccces(cb);});
                        $(cb.el).html('<span class="text-success"><i class="fa fa-check-circle"></i> Fits your vehicle.</span>');
                        $('#widget-product-fit-locator').modal('hide');
                        var href=window.location.href;
                        mygarage.resetVehicle(
                               {
                                make: cb.make,
                                year:  cb.year,
                                model: cb.model,
                                engine: cb.engine,
                                submodel: cb.submodel,
                                values:{}
                            }
                        );


                        var href=new URL(window.location.href);
                        href.searchParams.set('make',cb.make);
                        href.searchParams.set('model',cb.model);
                        href.searchParams.set('year',cb.year);
                        location.href= href;
                    } else {
                        let cMessage = "This <strong>" +  cb.part.taxonomy.parttype.name + "</strong> part does not fit the <strong>" +
                            cb.year + ' '+cb.make.replace('-',' ').toUpperCase()+' '+cb.model.replace('-',' ').toUpperCase() + "</strong> vehicle! Click here to search for parts that do fit!";

                        swal({
                            title: "Sorry it does not fit!",
                            text: "Would you like to search for <strong>" + part.taxonomy.parttype.name + "'s</strong>  that fit your <strong>" +
                                cb.year + ' '+cb.make.replace('-',' ').toUpperCase()+' '+cb.model.replace('-',' ').toUpperCase() + "</strong>?",
                            type: "error",
                            html: true,
                            showCancelButton: true,
                            confirmButtonColor: "green",
                            confirmButtonText: "Yes, show me the parts!",
                            cancelButtonText: "No, thank you!",
                            closeOnConfirm: true
                        }, function () {
                            $(location).attr("href", "/vehicle/"+cb.make+"/"+cb.year+"/"+cb.model+"/"+part.taxonomy.parttype.id);
                        });
                    }

                } else {

                    toastr.error("The server encountered a problem while checking fitment. Please try again.", "Oh Snap", {
                        timeOut: 3000,
                        progressBar: true,
                        closeButton: true
                    });
                }

            })
            .fail(function(){            toastr.error("The server encountered a problem while checking fitment. Please try again.", "Oh Snap", {
                timeOut: 3000,
                progressBar: true,
                closeButton: true
            });});

    }
}
