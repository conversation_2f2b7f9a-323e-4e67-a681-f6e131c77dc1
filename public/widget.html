<html>
<head>
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.4/css/bootstrap.min.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.3/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.3/js/select2.min.js"></script>
    <style>
        #quick-parts-selector {
            padding: 20px;
        }

        /* Iframe spinner styles */
        .iframe-spinner {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1000;
            display: none;
            text-align: center;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .spinner-text {
            margin-top: 10px;
            color: #666;
            font-size: 14px;
        }

        #container {
            position: relative;
        }

        /* Button spinner styles */
        .btn-spinner {
            width: 16px;
            height: 16px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 8px;
        }

        .btn-loading {
            position: relative;
        }
    </style>
</head>
<body>
<div class="container-fluid"><div class="row"><div class="col-sm-4">

    &nbsp;
    <div id="quick-parts-selector" class="bg-primary">
        <h2>Buy Auto Parts</h2>
        <form id="frm-vehicle-locator-af" class="vehicle-locator-af-vertical"  method="get" action="https://www.sophio.com/catalog-2/vehicle">
            <fieldset>

                <div class="form-group">
                    <select class="form-control" data-bind="options: years, optionsCaption: yearSelectCaption(),
					 optionsValue: function(item) { return item.id; },
					 optionsText: function(item) { return item.name; }, value: selectedYear,
				valueUpdate: 'change', enable: ( years().length)" id="af-year" name="year"></select>
                </div>
                <div class="form-group">
                    <select class="form-control" data-bind="options: makes, optionsCaption: makeSelectCaption(),
					 optionsValue: function(item) { return item.id; },
					 optionsText: function(item) { return item.name; }, value: selectedMake,
					 valueUpdate: 'change',  enable: (selectedYear && makes().length)" id="af-make" name="make">
                    </select>
                </div>
                <div class="form-group">
                    <select class="form-control" data-bind="options: models, optionsCaption: modelSelectCaption(),
					 optionsValue: function(item) { return item.id; },
					 optionsText: function(item) { return item.name; }, value: selectedModel,
				valueUpdate: 'change', enable:(selectedMake && models().length)" id="af-model" name="model"></select>
                </div>
                <div class="form-group" data-bind="visible:hasSubmodels">
                    <select class="form-control" data-bind="options: submodels, optionsCaption: submodelSelectCaption(),
					 optionsValue: function(item) { return item.id; },
					 optionsText: function(item) { return item.name; }, value: selectedSubModel,
				valueUpdate: 'change', enable:(selectedEngine && submodels().length)" id="af-submodel" name="submodel"></select>
                </div>
                <div class="form-group" data-bind="visible:hasEngines">
                    <select class="form-control" data-bind="options: engines, optionsCaption: engineSelectCaption(),
					 optionsValue: function(item) { return item.id; },
					 optionsText: function(item) { return item.name; }, value: selectedEngine,
				valueUpdate: 'change', enable:(selectedModel && engines().length)" id="af-engine" name="engine"></select>
                </div>
                <div class="form-group" data-bind="visible:hasPartTypes">
                    <select class="form-control" data-bind="options: parttypes, optionsCaption: parttypeSelectCaption(),
					 optionsValue: function(item) { return item.id; },
					 optionsText: function(item) { return item.name; }, value: selectedPartType,
				valueUpdate: 'change', enable: (selectedSubModel && parttypes().length)" id="af-parttype" name="parttype"></select>
                </div>

                <div class="form-group">
                    <button type="button" id="af-submit" class="btn btn-default" disabled="disabled" data-bind="enable: selectedModel, click:showParts,text:submitButtonTitle">Show Parts</button>
                </div>
            </fieldset>

        </form>
    </div>
</div>
<div class="col-sm-8" id="container">
<div class="iframe-spinner" id="iframe-spinner">
    <div class="spinner"></div>
    <div class="spinner-text">Loading parts...</div>
</div>
<iframe id="myiframe" src=""  width="100%" height="500" style="border:none;padding:20px"></iframe>
</div>
</div></div>
<script>

    var onlymakes_string = 'afla-romeo@@aston-martin@@bentley@@daewoo@@acura@@audi@@bmw@@buick@@cadillac@@chevrolet@@chrysler@@dodge@@eagle@@ferrari@@fiat@@ford@@geo@@gmc@@honda@@hummer@@hyundai@@infiniti@@isuzu@@jaguar@@jeep@@kia@@land-rover@@lexus@@lincoln@@lamborghini@@lotus@@maserati@@mercedes-benz@@mazda@@mercury@@mg@@mini@@mitsubishi@@nissan@@oldsmobile@@peugeot@@plymouth@@pontiac@@porsche@@ram@@renault@@rolls-royce@@rover@@saturn@@scion@@shelby@@smart@@subaru@@suzuki@@tesla@@toyota@@volkswagen@@volvo@@yugo@@workhorse';

    $(document).ready(function () {
        $("#af-parttype").select2({
        });
        const url_params = new URLSearchParams(window.location.search);

        window.SearchVM = new SearchViewModel();
        window.SearchVM.baseAjaxURL =  "https://faceted-catalog-api-us4.sophio.com/api/v1/c36e9644-3a6d-4b03-907f-2515145e1b52/catalog-2/vehicle?clientId=10";
        for (const [key, value] of url_params){
            window.SearchVM.baseAjaxURL=window.SearchVM.baseAjaxURL+"&"+key+"="+value;
        }

        window.SearchVM.baseSubmitURL = "https://tsc1.sophio.com/vehicle";
        window.SearchVM.loadYears({'smbeng':'submodels','usestatic':(url_params.get('usestatic')!=null?(url_params.get('usestatic').toLowerCase() === "true"):true),'onlymake':onlymakes_string});

        window.SearchVM.showParts = function() {
            // Show iframe spinner
            $('#iframe-spinner').show();

            // Show button spinner and update button text
            var $button = $('#af-submit');
            var originalText = $button.text();
            $button.addClass('btn-loading')
                   .prop('disabled', true)
                   .html('<span class="btn-spinner"></span>Loading...');

            // Build the URL
            var url = window.SearchVM.baseSubmitURL + "/" + window.SearchVM.selectedMake() + "/" +window.SearchVM.selectedYear() + "/" +window.SearchVM.selectedModel() + (typeof window.SearchVM.selectedPartType() !== "undefined" ? "/" + window.SearchVM.selectedPartType() : "") +
                "?" + jQuery.param({
                    "engine": window.SearchVM.selectedEngine(),
                    "submodel": window.SearchVM.selectedSubModel(),
                    "showas":"grid"
                }) + window.SearchVM.getMoreFields();

            // Set up iframe load event handler to hide spinners
            $('#myiframe').off('load.spinner').on('load.spinner', function() {
                // Hide iframe spinner
                $('#iframe-spinner').hide();

                // Reset button to original state
                $button.removeClass('btn-loading')
                       .prop('disabled', false)
                       .text(originalText);
            });

            // Set the iframe source
            $('#myiframe').attr('src', url);
        }
        ko.applyBindings(window.SearchVM, document.getElementById("quick-parts-selector"));

        $( '#frm-vehicle-locator-af' ).bind('keypress', function(e){
            if ( e.keyCode == 13 ) {
                if ( $( '#af-submit' ).is(':enabled') ) {
                    $( '#af-submit' ).trigger('click');
                }
                return false;
            }
        });
    });

</script>

<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/knockout/3.3.0/knockout-min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/knockout.mapping/2.4.1/knockout.mapping.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/knockout-validation/2.0.2/knockout.validation.min.js"></script>
<script type="text/javascript" src="https://michael.sophio.com/sophio-shared-assets/data/aces-catalog-2-make-year.js"></script>
<script type="text/javascript" src="https://michael.sophio.com/sophio-shared-assets/data/aces-catalog-2-models.js"></script>
<script type="text/javascript" src="/assets/js/ko/ko-postbox.js"></script>
<script type="text/javascript" src="/assets/js/ko/ko-ajax-aces-vehicle-lookup.js"></script>

</body>
</html>
