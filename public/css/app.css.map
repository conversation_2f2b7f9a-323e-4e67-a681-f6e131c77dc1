{"version": 3, "file": "css/app.css", "mappings": ";AAsGE,gBCslBF;AC3rBE;;;;EAAA,CCDF,4BASI,qQAIA,sMAIA,iKAIA,sNAIA,iRAIA,iPAIA,iRAGF,2BACA,qBAMA,yCACA,mGACA,4EAOA,gDC2OI,0BALI,CDpOR,0BACA,0BAKA,wBACA,6BACA,qBACA,6BAEA,yBACA,8BAEA,wCACA,kCACA,0BACA,kCAEA,sCACA,iCACA,yBACA,iCAGA,2BAEA,wBACA,+BACA,+BAEA,8BACA,oCAMA,wBACA,6BACA,0BAGA,sBACA,wBACA,0BACA,+CAEA,4BACA,8BACA,6BACA,2BACA,4BACA,mDACA,8BAGA,8CACA,uDACA,gDACA,uDAIA,8BACA,6BACA,2CAIA,8BACA,qCACA,gCACA,sCFDF,CI/GI,qBFyHA,wBACA,gCACA,qBACA,0BAEA,yBACA,oCAEA,2CACA,qCACA,0BACA,+BAEA,yCACA,oCACA,yBACA,8BAGE,iRAIA,iPAIA,iRAGF,2BAEA,wBACA,8BACA,gCACA,sCAEA,wBACA,6BACA,0BAEA,0BACA,kDAEA,8BACA,qCACA,gCACA,uCAlDA,iBFiDJ,CKvKA,iBAGE,qBL0KF,CK3JI,8CANJ,MAOM,sBL+JJ,CACF,CKlJA,KASE,8BACA,0CAFA,mCAFA,2BAJA,uCF6OI,kCALI,CEtOR,uCACA,uCAJA,SAMA,oCLwJF,CK5IA,GAGE,SACA,wCAFA,aCmnB4B,CDpnB5B,cAIA,WL+IF,CKrIA,0CAOE,8BAFA,eCwjB4B,CDvjB5B,eCwjB4B,CD5jB5B,mBCwjB4B,CDzjB5B,YL4IF,CKnIA,OFuMQ,+BHhER,CG5FI,0BE3CJ,OF8MQ,iBHnEN,CACF,CKvIA,OFkMQ,gCHvDR,CGrGI,0BEtCJ,OFyMQ,gBH1DN,CACF,CK3IA,OF6LQ,iCH9CR,CG9GI,0BEjCJ,OFoMQ,kBHjDN,CACF,CK/IA,OFwLQ,+BHrCR,CGvHI,0BE5BJ,OF+LQ,iBHxCN,CACF,CKnJA,OF+KM,kBHxBN,CKlJA,OF0KM,eHpBN,CK3IA,EAEE,kBCwV0B,CDzV1B,YL+IF,CKpIA,YAEE,YADA,0EAEA,mELuIF,CKjIA,QAEE,kBACA,oBAFA,kBLsIF,CK9HA,MAEE,iBLiIF,CK9HA,SAIE,mBADA,YLkIF,CK9HA,wBAIE,eLiIF,CK9HA,GACE,eLiIF,CK5HA,GACE,oBACA,aL+HF,CKzHA,WACE,eL4HF,CKpHA,SAEE,kBLuHF,CK/GA,aF6EM,gBHsCN,CK5GA,WAGE,wCADA,gCADA,eLiHF,CKtGA,QF0DM,eALI,CEjDR,cAFA,kBAGA,uBLyGF,CKtGA,IAAM,aL0GN,CKzGA,IAAM,SL6GN,CKxGA,EACE,8DACA,yBL2GF,CKzGE,QACE,kDL2GJ,CKhGE,4DAEE,cACA,oBLkGJ,CK3FA,kBAIE,oCCgV4B,CHlUxB,aHiFN,CKvFA,IACE,cFKI,gBALI,CEER,mBADA,aAEA,aL2FF,CKtFE,SAEE,cFLE,iBALI,CEWN,iBLwFJ,CKpFA,KAGE,qBADA,2BFZI,gBHoGN,CKpFE,OACE,aLsFJ,CKlFA,IAIE,qCCy5CkC,CC9rDhC,qBFoSF,uBCy5CkC,CHj7C9B,gBALI,CE2BR,wBLyFF,CKnFE,QF5BI,aALI,CEkCN,SLsFJ,CK3EA,OACE,eL8EF,CKxEA,QAEE,qBL2EF,CKnEA,MAEE,yBADA,mBLuEF,CKnEA,QAGE,+BC4Z4B,CD7Z5B,oBC2X4B,CD5X5B,iBC4X4B,CDzX5B,eLsEF,CK/DA,GAEE,mBACA,+BLiEF,CK9DA,2BAQE,eAFA,oBLmEF,CKzDA,MACE,oBL4DF,CKtDA,OAEE,eLwDF,CKhDA,iCACE,SLmDF,CK9CA,sCAME,oBF5HI,iBALI,CEmIR,oBAHA,QLoDF,CK7CA,cAEE,mBLgDF,CK3CA,cACE,cL8CF,CK3CA,OAGE,gBL4CF,CKzCE,gBACE,SL2CJ,CKpCA,0IACE,sBLuCF,CK/BA,gDAIE,yBLkCF,CK/BI,4GACE,cLoCN,CK7BA,mBAEE,kBADA,SLiCF,CK3BA,SACE,eL8BF,CKpBA,SAIE,SADA,SAFA,YACA,SLyBF,CKfA,OACE,WF9MM,gCEoNN,oBAHA,mBCmN4B,CDpN5B,UADA,ULsBF,CGjYI,0BEyWJ,OFtMQ,gBHkON,CACF,CKpBE,SACE,ULsBJ,CKfA,+OAOE,SLkBF,CKfA,4BACE,WLkBF,CKTA,cACE,6BACA,mBLYF,CKOA,4BACE,uBLIF,CKCA,+BACE,SLEF,CKKA,uBAEE,0BADA,YLDF,CKOA,OACE,oBLJF,CKSA,OACE,QLNF,CKaA,QAEE,eADA,iBLTF,CKkBA,SACE,uBLfF,CKuBA,SACE,sBLpBF,CDjjBA,MImQM,kBALI,CJ5PR,eCojBF,CD/iBE,WIgQM,iCJ5PJ,eOynBkB,CPxnBlB,eCgjBJ,CGjdI,0BJpGF,WIuQM,cHkTN,CACF,CD1jBE,WIgQM,iCJ5PJ,eOynBkB,CPxnBlB,eC2jBJ,CG5dI,0BJpGF,WIuQM,gBH6TN,CACF,CDrkBE,WIgQM,iCJ5PJ,eOynBkB,CPxnBlB,eCskBJ,CGveI,0BJpGF,WIuQM,cHwUN,CACF,CDhlBE,WIgQM,iCJ5PJ,eOynBkB,CPxnBlB,eCilBJ,CGlfI,0BJpGF,WIuQM,gBHmVN,CACF,CD3lBE,WIgQM,iCJ5PJ,eOynBkB,CPxnBlB,eC4lBJ,CG7fI,0BJpGF,WIuQM,cH8VN,CACF,CDtmBE,WIgQM,iCJ5PJ,eOynBkB,CPxnBlB,eCumBJ,CGxgBI,0BJpGF,WIuQM,gBHyWN,CACF,CDplBA,4BS3DE,gBADA,cR0pBF,CD3lBA,kBACE,oBC8lBF,CD5lBE,mCACE,kBC8lBJ,CDplBA,YI8MM,gBALI,CJvMR,wBCulBF,CDnlBA,YIwMM,kBALI,CJlMR,kBCulBF,CDplBE,wBACE,eCslBJ,CDllBA,mBAIE,aOtFS,CHiRL,gBALI,CJxLR,kBOuTO,CPxTP,gBCwlBF,CDnlBE,0BACE,YCqlBJ,CS/qBA,0BCCE,YAHA,cV+rBF,CS7rBA,eAEE,kCH+jDkC,CG9jDlC,2DFGE,sCELF,cT4rBF,CS9qBA,QAEE,oBTgrBF,CS7qBA,YAEE,cADA,mBTirBF,CS7qBA,gBAEE,+BHkjDkC,CH3zC9B,gBHybN,CWltBE,mGCHA,qBACA,gBAKA,iBADA,kBADA,yCADA,0CADA,UZmuBF,CazqBI,yBF5CE,yBACE,eXytBN,CACF,Ca/qBI,yBF5CE,uCACE,eX8tBN,CACF,CaprBI,yBF5CE,qDACE,eXmuBN,CACF,CazrBI,0BF5CE,mEACE,gBXwuBN,CACF,Ca9rBI,0BF5CE,kFACE,gBX6uBN,CACF,Cc9vBA,MAEI,oJdowBJ,Cc/vBE,KCNA,qBACA,gBACA,aACA,eAIA,yCADA,0CADA,sCf0wBF,CctwBI,OCOF,cAKA,8BAHA,eAEA,yCADA,0CAFA,UfswBF,CentBM,KACE,WfstBR,CentBM,iBApCJ,cACA,Uf2vBF,Ce7uBE,cACE,cACA,UfgvBJ,CelvBE,cACE,cACA,SfqvBJ,CevvBE,cACE,cACA,kBf0vBJ,Ce5vBE,cACE,cACA,Sf+vBJ,CejwBE,cACE,cACA,SfowBJ,CetwBE,cACE,cACA,kBfywBJ,Ce1uBM,UAhDJ,cACA,Uf8xBF,CezuBU,OAhEN,cACA,iBf6yBJ,Ce9uBU,OAhEN,cACA,kBfkzBJ,CenvBU,OAhEN,cACA,SfuzBJ,CexvBU,OAhEN,cACA,kBf4zBJ,Ce7vBU,OAhEN,cACA,kBfi0BJ,CelwBU,OAhEN,cACA,Sfs0BJ,CevwBU,OAhEN,cACA,kBf20BJ,Ce5wBU,OAhEN,cACA,kBfg1BJ,CejxBU,OAhEN,cACA,Sfq1BJ,CetxBU,QAhEN,cACA,kBf01BJ,Ce3xBU,QAhEN,cACA,kBf+1BJ,CehyBU,QAhEN,cACA,Ufo2BJ,Ce7xBY,UAxDV,uBfy1BF,CejyBY,UAxDV,wBf61BF,CeryBY,UAxDV,efi2BF,CezyBY,UAxDV,wBfq2BF,Ce7yBY,UAxDV,wBfy2BF,CejzBY,UAxDV,ef62BF,CerzBY,UAxDV,wBfi3BF,CezzBY,UAxDV,wBfq3BF,Ce7zBY,UAxDV,efy3BF,Cej0BY,WAxDV,wBf63BF,Cer0BY,WAxDV,wBfi4BF,Ce9zBQ,WAEE,efi0BV,Ce9zBQ,WAEE,efi0BV,Cex0BQ,WAEE,qBf20BV,Cex0BQ,WAEE,qBf20BV,Cel1BQ,WAEE,oBfq1BV,Cel1BQ,WAEE,oBfq1BV,Ce51BQ,WAEE,kBf+1BV,Ce51BQ,WAEE,kBf+1BV,Cet2BQ,WAEE,oBfy2BV,Cet2BQ,WAEE,oBfy2BV,Ceh3BQ,WAEE,kBfm3BV,Ceh3BQ,WAEE,kBfm3BV,Ca76BI,yBEUE,QACE,Wfu6BN,Cep6BI,oBApCJ,cACA,Uf28BA,Ce77BA,iBACE,cACA,Uf+7BF,Cej8BA,iBACE,cACA,Sfm8BF,Cer8BA,iBACE,cACA,kBfu8BF,Cez8BA,iBACE,cACA,Sf28BF,Ce78BA,iBACE,cACA,Sf+8BF,Cej9BA,iBACE,cACA,kBfm9BF,Cep7BI,aAhDJ,cACA,Ufu+BA,Cel7BQ,UAhEN,cACA,iBfq/BF,Cet7BQ,UAhEN,cACA,kBfy/BF,Ce17BQ,UAhEN,cACA,Sf6/BF,Ce97BQ,UAhEN,cACA,kBfigCF,Cel8BQ,UAhEN,cACA,kBfqgCF,Cet8BQ,UAhEN,cACA,SfygCF,Ce18BQ,UAhEN,cACA,kBf6gCF,Ce98BQ,UAhEN,cACA,kBfihCF,Cel9BQ,UAhEN,cACA,SfqhCF,Cet9BQ,WAhEN,cACA,kBfyhCF,Ce19BQ,WAhEN,cACA,kBf6hCF,Ce99BQ,WAhEN,cACA,UfiiCF,Ce19BU,aAxDV,afqhCA,Ce79BU,aAxDV,uBfwhCA,Ceh+BU,aAxDV,wBf2hCA,Cen+BU,aAxDV,ef8hCA,Cet+BU,aAxDV,wBfiiCA,Cez+BU,aAxDV,wBfoiCA,Ce5+BU,aAxDV,efuiCA,Ce/+BU,aAxDV,wBf0iCA,Cel/BU,aAxDV,wBf6iCA,Cer/BU,aAxDV,efgjCA,Cex/BU,cAxDV,wBfmjCA,Ce3/BU,cAxDV,wBfsjCA,Cen/BM,iBAEE,efq/BR,Cel/BM,iBAEE,efo/BR,Ce3/BM,iBAEE,qBf6/BR,Ce1/BM,iBAEE,qBf4/BR,CengCM,iBAEE,oBfqgCR,CelgCM,iBAEE,oBfogCR,Ce3gCM,iBAEE,kBf6gCR,Ce1gCM,iBAEE,kBf4gCR,CenhCM,iBAEE,oBfqhCR,CelhCM,iBAEE,oBfohCR,Ce3hCM,iBAEE,kBf6hCR,Ce1hCM,iBAEE,kBf4hCR,CACF,CavlCI,yBEUE,QACE,WfglCN,Ce7kCI,oBApCJ,cACA,UfonCA,CetmCA,iBACE,cACA,UfwmCF,Ce1mCA,iBACE,cACA,Sf4mCF,Ce9mCA,iBACE,cACA,kBfgnCF,CelnCA,iBACE,cACA,SfonCF,CetnCA,iBACE,cACA,SfwnCF,Ce1nCA,iBACE,cACA,kBf4nCF,Ce7lCI,aAhDJ,cACA,UfgpCA,Ce3lCQ,UAhEN,cACA,iBf8pCF,Ce/lCQ,UAhEN,cACA,kBfkqCF,CenmCQ,UAhEN,cACA,SfsqCF,CevmCQ,UAhEN,cACA,kBf0qCF,Ce3mCQ,UAhEN,cACA,kBf8qCF,Ce/mCQ,UAhEN,cACA,SfkrCF,CennCQ,UAhEN,cACA,kBfsrCF,CevnCQ,UAhEN,cACA,kBf0rCF,Ce3nCQ,UAhEN,cACA,Sf8rCF,Ce/nCQ,WAhEN,cACA,kBfksCF,CenoCQ,WAhEN,cACA,kBfssCF,CevoCQ,WAhEN,cACA,Uf0sCF,CenoCU,aAxDV,af8rCA,CetoCU,aAxDV,uBfisCA,CezoCU,aAxDV,wBfosCA,Ce5oCU,aAxDV,efusCA,Ce/oCU,aAxDV,wBf0sCA,CelpCU,aAxDV,wBf6sCA,CerpCU,aAxDV,efgtCA,CexpCU,aAxDV,wBfmtCA,Ce3pCU,aAxDV,wBfstCA,Ce9pCU,aAxDV,efytCA,CejqCU,cAxDV,wBf4tCA,CepqCU,cAxDV,wBf+tCA,Ce5pCM,iBAEE,ef8pCR,Ce3pCM,iBAEE,ef6pCR,CepqCM,iBAEE,qBfsqCR,CenqCM,iBAEE,qBfqqCR,Ce5qCM,iBAEE,oBf8qCR,Ce3qCM,iBAEE,oBf6qCR,CeprCM,iBAEE,kBfsrCR,CenrCM,iBAEE,kBfqrCR,Ce5rCM,iBAEE,oBf8rCR,Ce3rCM,iBAEE,oBf6rCR,CepsCM,iBAEE,kBfssCR,CensCM,iBAEE,kBfqsCR,CACF,CahwCI,yBEUE,QACE,WfyvCN,CetvCI,oBApCJ,cACA,Uf6xCA,Ce/wCA,iBACE,cACA,UfixCF,CenxCA,iBACE,cACA,SfqxCF,CevxCA,iBACE,cACA,kBfyxCF,Ce3xCA,iBACE,cACA,Sf6xCF,Ce/xCA,iBACE,cACA,SfiyCF,CenyCA,iBACE,cACA,kBfqyCF,CetwCI,aAhDJ,cACA,UfyzCA,CepwCQ,UAhEN,cACA,iBfu0CF,CexwCQ,UAhEN,cACA,kBf20CF,Ce5wCQ,UAhEN,cACA,Sf+0CF,CehxCQ,UAhEN,cACA,kBfm1CF,CepxCQ,UAhEN,cACA,kBfu1CF,CexxCQ,UAhEN,cACA,Sf21CF,Ce5xCQ,UAhEN,cACA,kBf+1CF,CehyCQ,UAhEN,cACA,kBfm2CF,CepyCQ,UAhEN,cACA,Sfu2CF,CexyCQ,WAhEN,cACA,kBf22CF,Ce5yCQ,WAhEN,cACA,kBf+2CF,CehzCQ,WAhEN,cACA,Ufm3CF,Ce5yCU,aAxDV,afu2CA,Ce/yCU,aAxDV,uBf02CA,CelzCU,aAxDV,wBf62CA,CerzCU,aAxDV,efg3CA,CexzCU,aAxDV,wBfm3CA,Ce3zCU,aAxDV,wBfs3CA,Ce9zCU,aAxDV,efy3CA,Cej0CU,aAxDV,wBf43CA,Cep0CU,aAxDV,wBf+3CA,Cev0CU,aAxDV,efk4CA,Ce10CU,cAxDV,wBfq4CA,Ce70CU,cAxDV,wBfw4CA,Cer0CM,iBAEE,efu0CR,Cep0CM,iBAEE,efs0CR,Ce70CM,iBAEE,qBf+0CR,Ce50CM,iBAEE,qBf80CR,Cer1CM,iBAEE,oBfu1CR,Cep1CM,iBAEE,oBfs1CR,Ce71CM,iBAEE,kBf+1CR,Ce51CM,iBAEE,kBf81CR,Cer2CM,iBAEE,oBfu2CR,Cep2CM,iBAEE,oBfs2CR,Ce72CM,iBAEE,kBf+2CR,Ce52CM,iBAEE,kBf82CR,CACF,Caz6CI,0BEUE,QACE,Wfk6CN,Ce/5CI,oBApCJ,cACA,Ufs8CA,Cex7CA,iBACE,cACA,Uf07CF,Ce57CA,iBACE,cACA,Sf87CF,Ceh8CA,iBACE,cACA,kBfk8CF,Cep8CA,iBACE,cACA,Sfs8CF,Cex8CA,iBACE,cACA,Sf08CF,Ce58CA,iBACE,cACA,kBf88CF,Ce/6CI,aAhDJ,cACA,Ufk+CA,Ce76CQ,UAhEN,cACA,iBfg/CF,Cej7CQ,UAhEN,cACA,kBfo/CF,Cer7CQ,UAhEN,cACA,Sfw/CF,Cez7CQ,UAhEN,cACA,kBf4/CF,Ce77CQ,UAhEN,cACA,kBfggDF,Cej8CQ,UAhEN,cACA,SfogDF,Cer8CQ,UAhEN,cACA,kBfwgDF,Cez8CQ,UAhEN,cACA,kBf4gDF,Ce78CQ,UAhEN,cACA,SfghDF,Cej9CQ,WAhEN,cACA,kBfohDF,Cer9CQ,WAhEN,cACA,kBfwhDF,Cez9CQ,WAhEN,cACA,Uf4hDF,Cer9CU,aAxDV,afghDA,Cex9CU,aAxDV,uBfmhDA,Ce39CU,aAxDV,wBfshDA,Ce99CU,aAxDV,efyhDA,Cej+CU,aAxDV,wBf4hDA,Cep+CU,aAxDV,wBf+hDA,Cev+CU,aAxDV,efkiDA,Ce1+CU,aAxDV,wBfqiDA,Ce7+CU,aAxDV,wBfwiDA,Ceh/CU,aAxDV,ef2iDA,Cen/CU,cAxDV,wBf8iDA,Cet/CU,cAxDV,wBfijDA,Ce9+CM,iBAEE,efg/CR,Ce7+CM,iBAEE,ef++CR,Cet/CM,iBAEE,qBfw/CR,Cer/CM,iBAEE,qBfu/CR,Ce9/CM,iBAEE,oBfggDR,Ce7/CM,iBAEE,oBf+/CR,CetgDM,iBAEE,kBfwgDR,CergDM,iBAEE,kBfugDR,Ce9gDM,iBAEE,oBfghDR,Ce7gDM,iBAEE,oBf+gDR,CethDM,iBAEE,kBfwhDR,CerhDM,iBAEE,kBfuhDR,CACF,CallDI,0BEUE,SACE,Wf2kDN,CexkDI,qBApCJ,cACA,Uf+mDA,CejmDA,kBACE,cACA,UfmmDF,CermDA,kBACE,cACA,SfumDF,CezmDA,kBACE,cACA,kBf2mDF,Ce7mDA,kBACE,cACA,Sf+mDF,CejnDA,kBACE,cACA,SfmnDF,CernDA,kBACE,cACA,kBfunDF,CexlDI,cAhDJ,cACA,Uf2oDA,CetlDQ,WAhEN,cACA,iBfypDF,Ce1lDQ,WAhEN,cACA,kBf6pDF,Ce9lDQ,WAhEN,cACA,SfiqDF,CelmDQ,WAhEN,cACA,kBfqqDF,CetmDQ,WAhEN,cACA,kBfyqDF,Ce1mDQ,WAhEN,cACA,Sf6qDF,Ce9mDQ,WAhEN,cACA,kBfirDF,CelnDQ,WAhEN,cACA,kBfqrDF,CetnDQ,WAhEN,cACA,SfyrDF,Ce1nDQ,YAhEN,cACA,kBf6rDF,Ce9nDQ,YAhEN,cACA,kBfisDF,CeloDQ,YAhEN,cACA,UfqsDF,Ce9nDU,cAxDV,afyrDA,CejoDU,cAxDV,uBf4rDA,CepoDU,cAxDV,wBf+rDA,CevoDU,cAxDV,efksDA,Ce1oDU,cAxDV,wBfqsDA,Ce7oDU,cAxDV,wBfwsDA,CehpDU,cAxDV,ef2sDA,CenpDU,cAxDV,wBf8sDA,CetpDU,cAxDV,wBfitDA,CezpDU,cAxDV,efotDA,Ce5pDU,eAxDV,wBfutDA,Ce/pDU,eAxDV,wBf0tDA,CevpDM,mBAEE,efypDR,CetpDM,mBAEE,efwpDR,Ce/pDM,mBAEE,qBfiqDR,Ce9pDM,mBAEE,qBfgqDR,CevqDM,mBAEE,oBfyqDR,CetqDM,mBAEE,oBfwqDR,Ce/qDM,mBAEE,kBfirDR,Ce9qDM,mBAEE,kBfgrDR,CevrDM,mBAEE,oBfyrDR,CetrDM,mBAEE,oBfwrDR,Ce/rDM,mBAEE,kBfisDR,Ce9rDM,mBAEE,kBfgsDR,CACF,CgBtzDA,OAEE,8BACA,2BACA,+BACA,4BAEA,0CACA,gCACA,+CACA,iCACA,kDACA,8DACA,iDACA,4DACA,gDACA,6DAKA,0CAFA,kBVkYO,CUjYP,kBVusB4B,CUzsB5B,UhBwzDF,CgB9yDE,yBAIE,oCACA,0CV+sB0B,CU9sB1B,yGAHA,mFAFA,ahBozDJ,CgB5yDE,aACE,sBhB8yDJ,CgB3yDE,aACE,qBhB6yDJ,CgBzyDA,qBACE,+ChB4yDF,CgBryDA,aACE,gBhBwyDF,CgB9xDE,4BACE,chBiyDJ,CgBlxDE,gCACE,qChBqxDJ,CgBlxDI,kCACE,qChBoxDN,CgB7wDE,oCACE,qBhBgxDJ,CgB7wDE,qCACE,kBhB+wDJ,CgB7vDE,kGACE,oDACA,6ChBqwDJ,CgB7vDA,cACE,oDACA,6ChBgwDF,CgBxvDE,8BACE,mDACA,4ChB2vDJ,CiBv4DE,eAOE,sBACA,sBACA,gCACA,8BACA,8BACA,6BACA,6BACA,4BACA,2BjBs4DJ,CiBr5DE,gCAkBE,0CADA,2BjBk5DJ,CiBn6DE,iBAOE,sBACA,sBACA,gCACA,8BACA,8BACA,6BACA,6BACA,4BACA,2BjBo5DJ,CiBn6DE,eAOE,sBACA,sBACA,gCACA,8BACA,8BACA,6BACA,6BACA,4BACA,2BjBk6DJ,CiBj7DE,2BAkBE,0CADA,2BjB86DJ,CiB/7DE,YAOE,sBACA,sBACA,gCACA,8BACA,8BACA,6BACA,6BACA,4BACA,2BjBg7DJ,CiB/7DE,eAOE,sBACA,sBACA,gCACA,8BACA,8BACA,6BACA,6BACA,4BACA,2BjB87DJ,CiB78DE,6BAkBE,0CADA,2BjB08DJ,CiB39DE,cAOE,sBACA,sBACA,gCACA,8BACA,8BACA,6BACA,6BACA,4BACA,2BjB48DJ,CiB39DE,aAOE,sBACA,sBACA,gCACA,8BACA,8BACA,6BACA,6BACA,4BACA,2BjB09DJ,CiBz+DE,yBAkBE,0CADA,2BjBs+DJ,CiBv/DE,YAOE,sBACA,sBACA,gCACA,8BACA,8BACA,6BACA,6BACA,4BACA,2BjBw+DJ,CgBp1DI,kBAEE,iCADA,ehBw1DN,Cal7DI,4BGyFA,qBAEE,iCADA,ehB81DJ,CACF,Caz7DI,4BGyFA,qBAEE,iCADA,ehBo2DJ,CACF,Ca/7DI,4BGyFA,qBAEE,iCADA,ehB02DJ,CACF,Car8DI,6BGyFA,qBAEE,iCADA,ehBg3DJ,CACF,Ca38DI,6BGyFA,sBAEE,iCADA,ehBs3DJ,CACF,CkBzhEA,YACE,mBlB2hEF,CkBlhEA,gBfiRM,iBALI,CerQR,eCfiB,CDWjB,gBADA,sDADA,kDlByhEF,CkB/gEA,mBfsQM,kBALI,Ce/PR,oDADA,gDlBohEF,CkB/gEA,mBfgQM,kBALI,CezPR,qDADA,iDlBohEF,CoBhjEA,WAKE,+Bd+1BsC,CHzkBlC,gBALI,CiBrRR,iBpBqjEF,CqBtjEA,cASE,6DAEA,4BADA,kCfq3BsC,Cen3BtC,2DdGE,sCcPF,0Bf43BsC,Cen4BtC,clB0RI,eALI,CkBhRR,efkmB4B,CejmB5B,eFLiB,CECjB,uBCSI,oEDMJ,CAhBA,UrBokEF,CsBtjEM,uCDhBN,cCiBQ,etByjEN,CACF,CqBvjEE,yBACE,erByjEJ,CqBvjEI,wDACE,crByjEN,CqBpjEE,oBAEE,kCfg2BoC,Ce/1BpC,oBf82BoC,Cex2BlC,4CfkhBkB,Ce1hBpB,0Bfs2BoC,Cen2BpC,SrBujEJ,CqB9iEE,2CAYE,aAKA,SAXA,crB6iEJ,CqB7hEE,qCACE,cACA,SrB+hEJ,CqB3hEE,gCACE,+Bf40BoC,Ce10BpC,SrB4hEJ,CqB/hEE,2BACE,+Bf40BoC,Ce10BpC,SrB4hEJ,CqBphEE,uBAEE,uCf8yBoC,Ce3yBpC,SrBmhEJ,CqB/gEE,oCE1FA,sCjBqiCgC,Cel8B9B,eAFA,qBAGA,8CfgsB0B,Ce/rB1B,gBAPA,0BfsyBoC,CexyBpC,wBACA,wBforB0B,CetrB1B,uBAKA,oBCpFE,6HtB2mEN,CsBvmEM,uCD0EJ,oCCzEM,etB0mEN,CACF,CqBnhEE,yEACE,uCrBqhEJ,CqB5gEA,wBAOE,6BACA,yBACA,sCAHA,0Bf2xBsC,CehyBtC,cAIA,eFtHiB,CEqHjB,gBADA,kBADA,UrBshEF,CqB7gEE,8BACE,SrB+gEJ,CqB5gEE,gFAGE,eADA,erB8gEJ,CqBlgEA,iBdjII,yCJ4QE,kBALI,CkBrIR,yDf4wBsC,Ce3wBtC,oBrBugEF,CqBngEE,uCAEE,sBACA,uBfooB0B,CetoB1B,oBrBugEJ,CqBjgEA,iBd9II,yCJ4QE,kBALI,CkBxHR,wDfgwBsC,Ce/vBtC,kBrBsgEF,CqBlgEE,uCAEE,oBACA,sBf2nB0B,Ce7nB1B,kBrBsgEJ,CqB5/DE,sBACE,0DrB+/DJ,CqB5/DE,yBACE,yDrB8/DJ,CqB3/DE,yBACE,wDrB6/DJ,CqBx/DA,oBAEE,sDf8tBsC,Ce7tBtC,efilB4B,CenlB5B,UrB6/DF,CqBz/DE,mDACE,crB2/DJ,CqBx/DE,uCACE,mBdvLA,qCPkrEJ,CqBv/DE,0CACE,mBd5LA,qCPsrEJ,CqBt/DE,oCAAoB,qDrBy/DtB,CqBx/DE,oCAAoB,oDrB2/DtB,CwB1sEA,aACE,sQAUA,6DACA,kClBk3BsC,CkBj3BtC,iFAEA,uClB+9BkC,CkBh+BlC,4BAEA,yBlB+9BkC,CkB99BlC,2DjBHE,sCiBJF,0BlBy3BsC,CkBh4BtC,crBuRI,eALI,CqB7QR,elB+lB4B,CkB9lB5B,eLRiB,CKIjB,uCFMI,oEESJ,CAhBA,UxB0tEF,CsB/sEM,uCEfN,aFgBQ,etBktEN,CACF,CwB7sEE,mBACE,oBlBs3BoC,CkBh3BlC,4ClBi+B4B,CkBt+B9B,SxBgtEJ,CwBvsEE,0DAGE,sBADA,oBxBysEJ,CwBrsEE,sBAEE,uCxBssEJ,CwBjsEE,4BACE,kBACA,sCxBmsEJ,CwB/rEA,gBjBtCI,yCJ4QE,kBALI,CqB/NR,qBlBquB4B,CkBpuB5B,kBlBquB4B,CkBvuB5B,kBxBssEF,CwB/rEA,gBjB9CI,yCJ4QE,kBALI,CqBvNR,oBlBiuB4B,CkBhuB5B,iBlBiuB4B,CkBnuB5B,iBxBssEF,CwB7rEI,kCACE,qQxBgsEN,CyBxwEA,YACE,cAGA,qBnBq6BwC,CmBv6BxC,kBnBq6BwC,CmBp6BxC,kBzB4wEF,CyBzwEE,8BACE,WACA,kBzB2wEJ,CyBvwEA,oBAEE,eADA,mBnB25BwC,CmBz5BxC,gBzB0wEF,CyBxwEE,sCACE,YAEA,cADA,mBzB2wEJ,CyBtwEA,kBACE,qCAOA,6DACA,yCACA,+CAEA,wBADA,4BAEA,wBACA,0DnB24BwC,CmBt5BxC,cAEA,UnBy4BwC,CmBx4BxC,gBASA,0DARA,mBAHA,SzBmxEF,CyBrwEE,iClB3BE,mBPmyEJ,CyBpwEE,8BAEE,iBzBqwEJ,CyBlwEE,yBACE,sBzBowEJ,CyBjwEE,wBACE,oBnBs1BoC,CmBp1BpC,4CnB8foB,CmB/fpB,SzBowEJ,CyBhwEE,0BACE,wBnB5BM,CmB6BN,oBzBkwEJ,CyBhwEI,yCAII,oQzB+vER,CyB3vEI,sCAII,4KzB0vER,CyBrvEE,+CAOI,+PANF,wBnBjDM,CmBkDN,oBzBwvEJ,CyB/uEE,2BAEE,YACA,UnBk2BuC,CmBp2BvC,mBzBmvEJ,CyB1uEI,2FACE,eACA,UzB4uEN,CyB9tEA,aACE,kBzBiuEF,CyB/tEE,+BACE,qLAIA,0CACA,sBlBjHA,kBkB+GA,mBHlHE,+CGsHF,CALA,SzBquEJ,CsBl1EM,uCG0GJ,+BHzGM,etBq1EN,CACF,CyBnuEI,qCACE,0KzBquEN,CyBluEI,uCAMI,wKALF,wBzBquEN,CyB3tEE,gCAEE,eADA,mBzB8tEJ,CyB3tEI,kDAEE,cADA,mBzB8tEN,CyBxtEA,mBACE,qBACA,iBzB2tEF,CyBxtEA,WAEE,mBACA,oBAFA,iBzB6tEF,CyBvtEI,mDAEE,YACA,WnBspBwB,CmBxpBxB,mBzB2tEN,CyBltEI,8EACE,0LzBqtEN,C0Bx4EA,YAIE,6DACA,6BAHA,cACA,UAFA,U1B+4EF,C0Bz4EE,kBACE,S1B24EJ,C0Bv4EI,wCAA0B,8D1B04E9B,C0Bz4EI,oCAA0B,8D1B44E9B,C0Bz4EE,8BACE,Q1B24EJ,C0Bx4EE,kCAIE,wCH1BF,wBjBkCQ,CoBNN,QpB6/BuC,CC1gCvC,mBmBSA,WpB8/BuC,CoB7/BvC,mBJbE,8GImBF,CJnBE,sGImBF,CARA,U1Bi5EJ,CsBx5EM,uCIMJ,kCJLM,uCtB25EN,CACF,C0B54EI,yCHjCF,wBvBg7EF,C0B14EE,2CAKE,uCpBu+B8B,CoBt+B9B,yBnB7BA,mBmB0BA,kBACA,cpBu+B8B,CoBz+B9B,YpBw+B8B,CoBz+B9B,U1Bk5EJ,C0Bx4EE,8BAGE,qCHpDF,wBjBkCQ,CoBoBN,QpBm+BuC,CC1gCvC,mBmBoCA,WpBm+BuC,CgB1gCrC,2GI6CF,CJ7CE,sGI6CF,CAPA,U1Bg5EJ,CsBl7EM,uCIiCJ,8BJhCM,oCtBq7EN,CACF,C0B54EI,qCH3DF,wBvB08EF,C0B14EE,8BAKE,uCpB68B8B,CoB58B9B,yBnBvDA,mBmBoDA,kBACA,cpB68B8B,CoB/8B9B,YpB88B8B,CoB/8B9B,U1Bk5EJ,C0Bx4EE,qBACE,mB1B04EJ,C0Bx4EI,2CACE,0C1B04EN,C0Bv4EI,uCACE,0C1By4EN,C2Bh+EA,eACE,iB3Bm+EF,C2Bj+EE,gGAGE,8CrBwiCoC,CqBtiCpC,gBrBuiCoC,CqBxiCpC,kD3Bo+EJ,C2Bh+EE,qBAYE,gDAPA,YAFA,OAIA,gBADA,oBAKA,oBAVA,kBAOA,iBACA,uBAPA,MAWA,qBLRE,4DKSF,CAJA,mBANA,S3B4+EJ,CsBv+EM,uCKTJ,qBLUM,etB0+EN,CACF,C2Bp+EE,oEAEE,mB3Bs+EJ,C2Bp+EI,wGACE,iB3Bu+EN,C2Bx+EI,8FACE,iB3Bu+EN,C2Bp+EI,8HAGE,sBrB4gCkC,CqB7gClC,oB3Bw+EN,C2B1+EI,oMAGE,sBrB4gCkC,CqB7gClC,oB3Bw+EN,C2Bp+EI,sGAEE,sBrBugCkC,CqBxgClC,oB3Bw+EN,C2Bn+EE,4BAEE,sBrBigCoC,CqBlgCpC,oB3Bs+EJ,C2B99EI,gEACE,yCACA,0D3Bm+EN,C2Br+EI,mLACE,yCACA,0D3Bm+EN,C2Bj+EM,sEAME,kCrBg0BgC,CCh3BpC,sCoB+CI,WADA,YrBm/BgC,CqBr/BhC,mBADA,kBAEA,U3B0+ER,C2B7+EM,2MAME,kCrBg0BgC,CCh3BpC,sCoB+CI,WADA,YrBm/BgC,CqBr/BhC,mBADA,kBAEA,U3B0+ER,C2Bh+EI,oDACE,yCACA,0D3Bk+EN,C2B79EI,6CACE,qC3B+9EN,C2B39EE,2EAEE,a3B69EJ,C2B39EI,uFACE,uC3B89EN,C4BrjFA,aAIE,oBAFA,aACA,eAFA,kBAIA,U5BwjFF,C4BtjFE,iFAIE,cAEA,YAHA,kBAEA,Q5ByjFJ,C4BpjFE,0GAGE,S5BsjFJ,C4BhjFE,kBACE,kBACA,S5BkjFJ,C4BhjFI,wBACE,S5BkjFN,C4BviFA,kBAEE,mBAQA,sCtB06BsC,CsBz6BtC,2DrBtCE,sCqBkCF,0BtBm1BsC,CsBz1BtC,azBgPI,eALI,CyBvOR,etByjB4B,CsBxjB5B,eT9CiB,CS2CjB,uBAKA,kBACA,kB5B6iFF,C4BjiFA,kHrBhDI,yCJ4QE,kBALI,CyBnNR,kB5BsiFF,C4BjiFA,kHrBzDI,yCJ4QE,kBALI,CyB1MR,oB5BsiFF,C4BjiFA,0DAEE,kB5BoiFF,C4B9gFI,iqBrBzEA,6BADA,yBPumFJ,C4BhhFE,0IrBxEE,4BADA,yBqB0EA,2C5BohFJ,C4BhhFE,uHrB7EE,4BADA,wBPmmFJ,C6B1nFE,gBAME,gCvBkjCqB,CuBvjCrB,a1BoQE,gBALI,C0B7PN,iBvBu0BoC,CuBx0BpC,U7BgoFJ,C6BznFE,eAWE,kCvBoiCqB,CC/jCrB,sCsB0BA,UvBqiCqB,CuB3iCrB,a1BwPE,kBALI,C0BhPN,iBAFA,eACA,qBALA,kBACA,SACA,S7BooFJ,C6BvnFI,8HAEE,a7B4nFN,C6B3qFI,0DAyDI,yQAEA,uDADA,4BAEA,0DAPF,8CvBuhCmB,CuBphCjB,kC7B4nFR,C6BrnFM,sEACE,8CvB4gCiB,CuBvgCf,uD7BmnFV,C6BxrFI,0EAgFI,0EADA,kC7B8mFR,C6B7rFI,wDAuFE,8C7B0mFN,C6BvmFQ,4NAEE,iRAEA,6DACA,oEAFA,sB7B0mFV,C6BpmFM,oEACE,8CvBw+BiB,CuBn+Bf,uD7BkmFV,C6B3sFI,sEAkHI,2B7B6lFR,C6B/sFI,kEAyHE,8C7B0lFN,C6BxlFM,kFACE,2C7B0lFR,C6BvlFM,8EACE,uD7BylFR,C6BtlFM,sGACE,gC7BwlFR,C6BnlFI,qDACE,gB7BslFN,C6BhuFI,kVAoJM,S7BolFV,C6BptFE,kBAME,kCvBkjCqB,CuBvjCrB,a1BoQE,gBALI,C0B7PN,iBvBu0BoC,CuBx0BpC,U7B0tFJ,C6BntFE,iBAWE,iCvBoiCqB,CC/jCrB,sCsB0BA,UvBqiCqB,CuB3iCrB,a1BwPE,kBALI,C0BhPN,iBAFA,eACA,qBALA,kBACA,SACA,S7B8tFJ,C6BjtFI,8IAEE,a7BstFN,C6BrwFI,8DAyDI,sUAEA,uDADA,4BAEA,0DAPF,gDvBuhCmB,CuBphCjB,kC7BstFR,C6B/sFM,0EACE,gDvB4gCiB,CuBvgCf,sD7B6sFV,C6BlxFI,8EAgFI,0EADA,kC7BwsFR,C6BvxFI,4DAuFE,gD7BosFN,C6BjsFQ,oOAEE,8UAEA,6DACA,oEAFA,sB7BosFV,C6B9rFM,wEACE,gDvBw+BiB,CuBn+Bf,sD7B4rFV,C6BryFI,0EAkHI,2B7BurFR,C6BzyFI,sEAyHE,gD7BorFN,C6BlrFM,sFACE,6C7BorFR,C6BjrFM,kFACE,sD7BmrFR,C6BhrFM,0GACE,kC7BkrFR,C6B7qFI,uDACE,gB7BgrFN,C6B1zFI,8VAsJM,S7B4qFV,C8Bp0FA,KAEE,2BACA,4BACA,uB3BuRI,yBALI,C2BhRR,yBACA,yBACA,oCACA,wBACA,6CACA,kCACA,+CACA,wCACA,iFACA,+BACA,gFPhBA,iCOkCqB,CAFrB,mEvBjBE,0CuBUF,0BAKA,eAXA,qBAEA,sC3BsQI,iCALI,C2B/PR,sCACA,sCAJA,wDAMA,kBACA,qBRfI,6HQwBJ,CALA,gEAFA,qB9By0FF,CsBt1FM,uCQhBN,KRiBQ,etBy1FN,CACF,C8Br0FE,WAGE,wCACA,8CAHA,+B9By0FJ,C8Bn0FE,sBAGE,kCACA,wCAFA,yB9Bs0FJ,C8Bj0FE,mBPpDA,uCOsDuB,CACrB,8CAME,0CARF,gCAGA,S9Bo0FJ,C8B3zFE,8BACE,8CAME,0CALF,S9B8zFJ,C8BrzFE,mGAME,yCAGA,+CAJA,gC9BqzFJ,C8B9yFI,yKAKI,yC9B4yFR,C8BvyFE,sCAKI,yC9BqyFN,C8BjyFE,mDAKE,2CAEA,iDAJA,mCAKA,uCAJA,mB9BoyFJ,C8BpxFE,aC/GA,oBACA,oBACA,8BACA,0BACA,0BACA,oCACA,qCACA,2BACA,2BACA,qCACA,wDACA,6BACA,6BACA,sC/Bu4FF,C8BryFE,eC/GA,oBACA,oBACA,8BACA,0BACA,0BACA,oCACA,sCACA,2BACA,2BACA,qCACA,wDACA,6BACA,6BACA,sC/Bw5FF,C8BtzFE,aC/GA,oBACA,oBACA,8BACA,0BACA,0BACA,oCACA,qCACA,2BACA,2BACA,qCACA,wDACA,6BACA,6BACA,sC/By6FF,C8Bv0FE,UC/GA,oBACA,oBACA,8BACA,0BACA,0BACA,oCACA,qCACA,2BACA,2BACA,qCACA,wDACA,6BACA,6BACA,sC/B07FF,C8Bx1FE,aC/GA,oBACA,oBACA,8BACA,0BACA,0BACA,oCACA,oCACA,2BACA,2BACA,qCACA,wDACA,6BACA,6BACA,sC/B28FF,C8Bz2FE,YC/GA,oBACA,oBACA,8BACA,0BACA,0BACA,oCACA,oCACA,2BACA,2BACA,qCACA,wDACA,6BACA,6BACA,sC/B49FF,C8B13FE,WC/GA,oBACA,oBACA,8BACA,0BACA,0BACA,oCACA,sCACA,2BACA,2BACA,qCACA,wDACA,6BACA,6BACA,sC/B6+FF,C8B34FE,UC/GA,oBACA,oBACA,8BACA,0BACA,0BACA,oCACA,mCACA,2BACA,2BACA,qCACA,wDACA,6BACA,6BACA,sC/B8/FF,C8Bl4FE,qBChHA,uBACA,8BACA,0BACA,0BACA,oCACA,qCACA,2BACA,2BACA,qCACA,wDACA,gCACA,iCACA,uCACA,kB/Bs/FF,C8Bn5FE,uBChHA,uBACA,8BACA,0BACA,0BACA,oCACA,sCACA,2BACA,2BACA,qCACA,wDACA,gCACA,iCACA,uCACA,kB/BugGF,C8Bp6FE,qBChHA,uBACA,8BACA,0BACA,0BACA,oCACA,oCACA,2BACA,2BACA,qCACA,wDACA,gCACA,iCACA,uCACA,kB/BwhGF,C8Br7FE,kBChHA,uBACA,8BACA,0BACA,0BACA,oCACA,qCACA,2BACA,2BACA,qCACA,wDACA,gCACA,iCACA,uCACA,kB/ByiGF,C8Bt8FE,qBChHA,uBACA,8BACA,0BACA,0BACA,oCACA,oCACA,2BACA,2BACA,qCACA,wDACA,gCACA,iCACA,uCACA,kB/B0jGF,C8Bv9FE,oBChHA,uBACA,8BACA,0BACA,0BACA,oCACA,oCACA,2BACA,2BACA,qCACA,wDACA,gCACA,iCACA,uCACA,kB/B2kGF,C8Bx+FE,mBChHA,uBACA,8BACA,0BACA,0BACA,oCACA,sCACA,2BACA,2BACA,qCACA,wDACA,gCACA,iCACA,uCACA,kB/B4lGF,C8Bz/FE,kBChHA,uBACA,8BACA,0BACA,0BACA,oCACA,mCACA,2BACA,2BACA,qCACA,wDACA,gCACA,iCACA,uCACA,kB/B6mGF,C8B9/FA,UACE,yBACA,oCACA,wBACA,kCACA,gDACA,wCACA,iDACA,yCACA,gCACA,2CACA,+BACA,qCAEA,yB9BggGF,C8Bt/FE,wBACE,yB9Bw/FJ,C8Br/FE,gBACE,+B9Bu/FJ,C8B5+FA,2BCjJE,0BACA,wB5B8NI,2BALI,C4BvNR,iD/BioGF,C8B/+FA,2BCrJE,2BACA,0B5B8NI,4BALI,C4BvNR,iD/BwoGF,CgC3sGA,MVgBM,8BtB+rGN,CsB3rGM,uCUpBN,MVqBQ,etB8rGN,CACF,CgCjtGE,iBACE,ShCmtGJ,CgC7sGE,qBACE,YhCgtGJ,CgC5sGA,YACE,SACA,gBVDI,2BtBitGN,CsB7sGM,uCULN,YVMQ,etBgtGN,CACF,CgCltGE,gCAEE,YVNE,0BUOF,CAFA,OhCstGJ,CsBvtGM,uCUAJ,gCVCM,etB0tGN,CACF,CiC/uGA,sEAME,iBjCkvGF,CiC/uGA,iBACE,kBjCkvGF,CkC1tGI,uBA/BF,gBACA,mCAFA,oCADA,sBAqCI,WAHA,qBACA,kB5B6hBwB,C4B5hBxB,qBlCiuGN,CkCxsGI,6BACE,alC0sGN,CiCxvGA,eAEE,0BACA,8BACA,0BACA,+BACA,8B9BuQI,8BALI,C8BhQR,yCACA,mCACA,8DACA,oDACA,kDACA,yFACA,4DACA,sCACA,8CACA,8CACA,oDACA,kDACA,qCACA,qCACA,2DACA,kCACA,qCACA,mCACA,oCACA,sCAcA,4BADA,uCAEA,6E1BzCE,+C0BoCF,+BALA,a9B6OI,sCALI,C8BjOR,gBAJA,SAFA,uCACA,kEAJA,kBAQA,gBAPA,iCjCowGF,CiCrvGE,+BAEE,OACA,qCAFA,QjCyvGJ,CiC/tGI,qBACE,mBjCkuGN,CiChuGM,qCAEE,OADA,UjCmuGR,CiC9tGI,mBACE,iBjCiuGN,CiC/tGM,mCAEE,UADA,OjCkuGR,Ca3wGI,yBoB4BA,wBACE,mBjCmvGJ,CiCjvGI,wCAEE,OADA,UjCovGN,CiC/uGE,sBACE,iBjCivGJ,CiC/uGI,sCAEE,UADA,OjCkvGN,CACF,Ca5xGI,yBoB4BA,wBACE,mBjCmwGJ,CiCjwGI,wCAEE,OADA,UjCowGN,CiC/vGE,sBACE,iBjCiwGJ,CiC/vGI,sCAEE,UADA,OjCkwGN,CACF,Ca5yGI,yBoB4BA,wBACE,mBjCmxGJ,CiCjxGI,wCAEE,OADA,UjCoxGN,CiC/wGE,sBACE,iBjCixGJ,CiC/wGI,sCAEE,UADA,OjCkxGN,CACF,Ca5zGI,0BoB4BA,wBACE,mBjCmyGJ,CiCjyGI,wCAEE,OADA,UjCoyGN,CiC/xGE,sBACE,iBjCiyGJ,CiC/xGI,sCAEE,UADA,OjCkyGN,CACF,Ca50GI,0BoB4BA,yBACE,mBjCmzGJ,CiCjzGI,yCAEE,OADA,UjCozGN,CiC/yGE,uBACE,iBjCizGJ,CiC/yGI,uCAEE,UADA,OjCkzGN,CACF,CiCxyGE,uCAEE,YAEA,wCADA,aAFA,QjC6yGJ,CkC93GI,+BAxBF,yBACA,mCAFA,oCADA,aA8BI,WAHA,qBACA,kB5B6hBwB,C4B5hBxB,qBlCq4GN,CkC52GI,qCACE,alC82GN,CiC9yGE,wCAGE,UAEA,sCADA,aAFA,WADA,KjCqzGJ,CkCn5GI,gCAjBF,qCACA,uBAFA,eADA,kCAuBI,WAHA,qBACA,kB5B6hBwB,C4B5hBxB,qBlC05GN,CkCj4GI,sCACE,alCm4GN,CiCzzGI,gCACE,gBjC2zGN,CiCrzGE,0CAGE,UAEA,uCADA,aAFA,WADA,KjC4zGJ,CkC36GI,kCAIE,WAHA,qBAeE,aAdF,kB5B6hBwB,C4B5hBxB,qBlC86GN,CkC95GM,mCA7BJ,qCADA,wBADA,kCAmCM,WAHA,qBACA,mB5B0gBsB,C4BzgBtB,qBlCu6GR,CkCj6GI,wCACE,alCm6GN,CiCx0GI,mCACE,gBjC00GN,CiCn0GA,kBAIE,mDAHA,SACA,6CAGA,UAFA,ejCw0GF,CiCh0GA,eAUE,6BACA,S1BtKE,sD0B+JF,WAEA,oCALA,cAIA,e3Byb4B,C2B3b5B,4EAIA,mBACA,qBACA,mBAPA,UjC60GF,CiCj0GE,0CVxLA,iDU4LuB,CAFrB,yCjCm0GJ,CiC9zGE,4CV/LA,kDUmMuB,CAFrB,2CACA,oBjCg0GJ,CiC5zGE,gDAIE,6BAFA,6CACA,mBjC8zGJ,CiCvzGA,oBACE,ajC0zGF,CiCtzGA,iBAKE,sCAJA,c9BqEI,kBALI,C8B9DR,gBADA,gFAIA,kBjCyzGF,CiCrzGA,oBAGE,oCAFA,cACA,2EjCyzGF,CiCpzGA,oBAEE,4BACA,yBACA,8DACA,2BACA,iCACA,oCACA,4DACA,gDACA,qCACA,qCACA,0CACA,kCjCszGF,CmC5iHA,+BAGE,oBADA,kBAEA,qBnC+iHF,CmC7iHE,yCAEE,cADA,iBnCijHJ,CmC3iHE,kXAME,SnCmjHJ,CmC9iHA,aACE,aACA,eACA,0BnCijHF,CmC/iHE,0BACE,UnCijHJ,CmC7iHA,W5BhBI,qCPikHJ,CmC7iHE,qFAEE,2CnC+iHJ,CmC3iHE,qJ5BTE,6BADA,yBP2jHJ,CmCviHE,6G5BLE,4BADA,wBPmjHJ,CmC1hHA,uBAEE,sBADA,sBnC8hHF,CmC3hHE,wGAGE,anC2hHJ,CmCxhHE,yCACE,cnC0hHJ,CmCthHA,yEAEE,qBADA,qBnC0hHF,CmCthHA,yEAEE,oBADA,oBnC0hHF,CmCrgHA,oBAEE,uBADA,sBAEA,sBnCwgHF,CmCtgHE,wDAEE,UnCwgHJ,CmCrgHE,4FAEE,0CnCugHJ,CmCngHE,qH5BzFE,4BADA,4BPkmHJ,CmCngHE,oF5B7GE,yBACA,yBPonHJ,CoC5oHA,KAEE,6BACA,+BAEA,4BACA,yCACA,qDACA,uDAGA,aACA,eAGA,gBADA,gBADA,cpC6oHF,CoCxoHA,UAOE,gBACA,SAHA,+BAJA,cjCuQI,sCALI,CiC/PR,2CAFA,kEAIA,qBdbI,iGtB2pHN,CsBvpHM,uCcGN,UdFQ,etB0pHN,CACF,CoC9oHE,gCAEE,oCpC+oHJ,CoC3oHE,wBAEE,4C9BkhBoB,C8BnhBpB,SpC8oHJ,CoCzoHE,sCAEE,wCAEA,eADA,mBpC2oHJ,CoCloHA,UAEE,kDACA,kDACA,oDACA,2GACA,yDACA,+CACA,uGAGA,mFpCkoHF,CoChoHE,oBAEE,yD7B7CA,wDACA,yD6B2CA,sDpCqoHJ,CoCjoHI,oDAIE,wDADA,iBpCkoHN,CoC7nHE,8DAGE,mDACA,yDAFA,0CpCioHJ,CoC5nHE,yB7B/DE,yBACA,0B6BgEA,mDpC+nHJ,CoCpnHA,WAEE,qDACA,sCACA,qCpCsnHF,CoCnnHE,qB7B5FE,+CPktHJ,CoClnHE,uDb/GA,mDakHuB,CADrB,2CpCqnHJ,CoC3mHA,eAEE,4BACA,yCACA,8DAGA,+BpC2mHF,CoCzmHE,yBAGE,qEADA,eADA,epC6mHJ,CoCzmHI,8DAEE,gCpC0mHN,CoCtmHE,+DAIE,iCADA,gDADA,epC0mHJ,CoC9lHE,wCAEE,cACA,iBpCimHJ,CoC5lHE,kDAEE,aACA,YACA,iBpC+lHJ,CoCzlHE,iEACE,UpC6lHJ,CoCnlHE,uBACE,YpCslHJ,CoCplHE,qBACE,apCslHJ,CqCnxHA,QAEE,wBACA,6BACA,0DACA,+DACA,kEACA,8DACA,oCACA,kCACA,qCACA,6DACA,mEACA,sCACA,sCACA,sCACA,uCACA,qRACA,yEACA,0DACA,wCACA,4DAMA,mBAFA,aACA,eAEA,8BACA,8DALA,iBrCwxHF,CqC7wHE,2JAGE,mBAFA,aACA,kBAEA,6BrCqxHJ,CqCjwHA,cAKE,mClC0NI,0CALI,CkCvNR,+CADA,gDADA,6CAKA,qBACA,kBrCmwHF,CqCjwHE,wCAEE,wCrCkwHJ,CqCxvHA,YAEE,0BACA,+BAEA,4BACA,2CACA,uDACA,6DAGA,aACA,sBAGA,gBADA,gBADA,crCyvHF,CqCpvHI,wDAEE,mCrCqvHN,CqCjvHE,2BACE,erCmvHJ,CqC1uHA,aAGE,6BADA,oB/B6gCkC,C+B9gClC,iBrC+uHF,CqC3uHE,yDAGE,mCrC6uHJ,CqChuHA,iBAKE,mBAJA,gBACA,WrCouHF,CqC7tHA,gBAKE,6BACA,0E9BxIE,qD8BsIF,6BlCsII,4CALI,CkClIR,cAFA,8EftII,8CtB62HN,CsBz2HM,uCeiIN,gBfhIQ,etB42HN,CACF,CqCnuHE,sBACE,oBrCquHJ,CqCluHE,sBAGE,sDADA,UADA,oBrCsuHJ,CqC9tHA,qBAKE,kDAEA,wBADA,4BAEA,qBAPA,qBAEA,aACA,sBAFA,WrCuuHF,CqC9tHA,mBACE,wCACA,erCiuHF,Ca31HI,yBwBsIA,kBAEI,iBACA,0BrCwtHN,CqCttHM,8BACE,kBrCwtHR,CqCttHQ,6CACE,iBrCwtHV,CqCrtHQ,wCAEE,iDADA,iDrCwtHV,CqCntHM,qCACE,gBrCqtHR,CqCltHM,mCACE,uBACA,erCotHR,CqCjtHM,kCACE,YrCmtHR,CqChtHM,6BAQE,uCACA,mBALA,YAEA,sBAJA,gBAQA,yBf9NJ,eegOI,CALA,6BAFA,qBAFA,YrCytHR,CqC7sHQ,+CACE,YrC+sHV,CqC5sHQ,6CACE,aACA,YAEA,mBADA,SrC+sHV,CACF,Ca34HI,yBwBsIA,kBAEI,iBACA,0BrCuwHN,CqCrwHM,8BACE,kBrCuwHR,CqCrwHQ,6CACE,iBrCuwHV,CqCpwHQ,wCAEE,iDADA,iDrCuwHV,CqClwHM,qCACE,gBrCowHR,CqCjwHM,mCACE,uBACA,erCmwHR,CqChwHM,kCACE,YrCkwHR,CqC/vHM,6BAQE,uCACA,mBALA,YAEA,sBAJA,gBAQA,yBf9NJ,eegOI,CALA,6BAFA,qBAFA,YrCwwHR,CqC5vHQ,+CACE,YrC8vHV,CqC3vHQ,6CACE,aACA,YAEA,mBADA,SrC8vHV,CACF,Ca17HI,yBwBsIA,kBAEI,iBACA,0BrCszHN,CqCpzHM,8BACE,kBrCszHR,CqCpzHQ,6CACE,iBrCszHV,CqCnzHQ,wCAEE,iDADA,iDrCszHV,CqCjzHM,qCACE,gBrCmzHR,CqChzHM,mCACE,uBACA,erCkzHR,CqC/yHM,kCACE,YrCizHR,CqC9yHM,6BAQE,uCACA,mBALA,YAEA,sBAJA,gBAQA,yBf9NJ,eegOI,CALA,6BAFA,qBAFA,YrCuzHR,CqC3yHQ,+CACE,YrC6yHV,CqC1yHQ,6CACE,aACA,YAEA,mBADA,SrC6yHV,CACF,Caz+HI,0BwBsIA,kBAEI,iBACA,0BrCq2HN,CqCn2HM,8BACE,kBrCq2HR,CqCn2HQ,6CACE,iBrCq2HV,CqCl2HQ,wCAEE,iDADA,iDrCq2HV,CqCh2HM,qCACE,gBrCk2HR,CqC/1HM,mCACE,uBACA,erCi2HR,CqC91HM,kCACE,YrCg2HR,CqC71HM,6BAQE,uCACA,mBALA,YAEA,sBAJA,gBAQA,yBf9NJ,eegOI,CALA,6BAFA,qBAFA,YrCs2HR,CqC11HQ,+CACE,YrC41HV,CqCz1HQ,6CACE,aACA,YAEA,mBADA,SrC41HV,CACF,CaxhII,0BwBsIA,mBAEI,iBACA,0BrCo5HN,CqCl5HM,+BACE,kBrCo5HR,CqCl5HQ,8CACE,iBrCo5HV,CqCj5HQ,yCAEE,iDADA,iDrCo5HV,CqC/4HM,sCACE,gBrCi5HR,CqC94HM,oCACE,uBACA,erCg5HR,CqC74HM,mCACE,YrC+4HR,CqC54HM,8BAQE,uCACA,mBALA,YAEA,sBAJA,gBAQA,yBf9NJ,eegOI,CALA,6BAFA,qBAFA,YrCq5HR,CqCz4HQ,gDACE,YrC24HV,CqCx4HQ,8CACE,aACA,YAEA,mBADA,SrC24HV,CACF,CqCj8HI,eAEI,iBACA,0BrCk8HR,CqCh8HQ,2BACE,kBrCk8HV,CqCh8HU,0CACE,iBrCk8HZ,CqC/7HU,qCAEE,iDADA,iDrCk8HZ,CqC77HQ,kCACE,gBrC+7HV,CqC57HQ,gCACE,uBACA,erC87HV,CqC37HQ,+BACE,YrC67HV,CqC17HQ,0BAQE,uCACA,mBALA,YAEA,sBAJA,gBAQA,yBf9NJ,eegOI,CALA,6BAFA,qBAFA,YrCm8HV,CqCv7HU,4CACE,YrCy7HZ,CqCt7HU,0CACE,aACA,YAEA,mBADA,SrCy7HZ,CqCv6HA,yCAGE,sCACA,4CACA,+CACA,8BACA,6BACA,mCACA,mDrC06HF,CqCn6HI,mFANF,uRrC66HF,CsC/rIA,MAEE,wBACA,wBACA,gCACA,wBACA,2BACA,8CACA,0DACA,gDACA,uBACA,qFACA,+BACA,6BACA,qDACA,sBACA,mBACA,kBACA,+BACA,mCACA,+BASA,qBAEA,2BADA,mCAEA,qE/BjBE,2C+BaF,2BAJA,aACA,sBAEA,6BADA,YAHA,iBtCysIF,CsC5rIE,SAEE,cADA,ctC+rIJ,CsC3rIE,kBAEE,sBADA,kBtC8rIJ,CsC3rII,8B/BrBA,0DACA,2D+BqBE,kBtC+rIN,CsC3rII,6B/BXA,6DADA,8D+BaE,qBtC+rIN,CsCxrIE,8DAEE,YtC0rIJ,CsCtrIA,WAKE,2BAFA,cACA,uDtCwrIF,CsCprIA,YAEE,iCADA,2CtCwrIF,CsCprIA,eAGE,oCAFA,kDtCyrIF,CsCprIA,qCAJE,etC4rIF,CsC/qIE,sBACE,mCtCkrIJ,CsC1qIA,aAIE,uCACA,4EAFA,+BADA,gBADA,iEtCirIF,CsC3qIE,yB/B7FE,uFP2wIJ,CsCzqIA,aAGE,uCACA,yEAFA,+BADA,iEtC+qIF,CsC1qIE,wB/BxGE,uFPqxIJ,CsCnqIA,kBAIE,gBAFA,oDACA,mDAFA,mDtCyqIF,CsCpqIE,mCACE,mCACA,qCtCsqIJ,CsClqIA,mBAEE,mDADA,mDtCsqIF,CsCjqIA,kB/BpII,iD+BwIF,SACA,OACA,2CALA,kBAEA,QADA,KtCyqIF,CsCjqIA,yCAGE,UtCoqIF,CsCjqIA,wB/B3II,0DACA,0DPizIJ,CsClqIA,2B/BjII,6DADA,6DP0yIJ,CsC3pIE,kBACE,yCtC8pIJ,CazxII,yByBuHJ,YAQI,aACA,kBtC8pIF,CsC3pIE,kBAEE,YACA,etC4pIJ,CsC1pII,wBAEE,cADA,atC6pIN,CsCvpIM,mC/B1KJ,6BADA,yBPs0IF,CsCxpIQ,iGAGE,yBtCypIV,CsCvpIQ,oGAGE,4BtCwpIV,CsCppIM,oC/B3KJ,4BADA,wBPo0IF,CsCrpIQ,mGAGE,wBtCspIV,CsCppIQ,sGAGE,2BtCqpIV,CACF,CuC13IA,WAEE,0CACA,oCACA,0KACA,mDACA,mDACA,qDACA,0FACA,qCACA,kCACA,8CACA,6CACA,qPACA,sCACA,kDACA,8DACA,4PACA,uEACA,sCACA,mCACA,4DACA,oDvC43IF,CuCx3IA,kBAGE,mBAMA,4CACA,ShCrBE,gBgCkBF,oCALA,apC+PI,eALI,CoChPR,qBAPA,4EAJA,kBAOA,gBjBtBI,yCiB2BJ,CATA,UvCo4IF,CsBl5IM,uCiBUN,kBjBTQ,etBq5IN,CACF,CuC93IE,kCAEE,+CACA,gGAFA,sCvCk4IJ,CuC93II,wCACE,qDACA,gDvCg4IN,CuC33IE,wBAME,8CACA,4BACA,mDAHA,WAJA,cAEA,0CACA,iBjB7CE,kDiBkDF,CAPA,wCvCo4IJ,CsB36IM,uCiBqCJ,wBjBpCM,etB86IN,CACF,CuC/3IE,wBACE,SvCi4IJ,CuC93IE,wBAGE,oDADA,UADA,SvCk4IJ,CuC53IA,kBACE,evC+3IF,CuC53IA,gBAEE,wCACA,+EAFA,+BvCi4IF,CuC73IE,8BhC7DE,yDACA,yDP67IJ,CuC93II,kEhChEA,+DACA,+DPi8IJ,CuC73IE,oCACE,YvC+3IJ,CuC33IE,6BhC3DE,4DADA,4DP27IJ,CuC33IM,2EhC/DF,kEADA,kEP+7IJ,CuC13II,iDhCpEA,4DADA,4DPm8IJ,CuCx3IA,gBACE,6EvC23IF,CuCl3IE,iCAEE,chC9GA,gBgC6GA,cvCu3IJ,CuCn3II,6CAAgB,YvCs3IpB,CuCr3II,4CAAe,evCw3InB,CuC72II,yMhC7HA,ePg/IJ,CuC32II,6CACE,sTACA,4TvC82IN,CwCxgJA,YAEE,4BACA,4BACA,mCAEA,qBACA,gCACA,wDACA,sCACA,4DASA,yCjCAE,iDiCNF,aACA,erCiRI,wCALI,CqCxQR,gBAFA,iDADA,qExC4gJF,CwClgJE,kCACE,gDxCqgJJ,CwCngJI,yCAGE,yCACA,yCAHA,WACA,iDxCugJN,CwCjgJE,wBACE,4CxCmgJJ,CyCxiJA,YAEE,kCACA,mCtC4RI,gCALI,CsCrRR,2CACA,qCACA,oDACA,oDACA,sDACA,uDACA,+CACA,0DACA,uDACA,gDACA,oEACA,kCACA,kCACA,4CACA,yDACA,mDACA,6DAGA,ajCnBA,gBADA,cR8jJF,CyCtiJA,WAOE,yCACA,iFAHA,iCAHA,ctCiQI,wCALI,CsC3PR,sEAFA,kBAKA,qBnBlBI,6HtB8jJN,CsB1jJM,uCmBQN,WnBPQ,etB6jJN,CACF,CyC5iJE,iBAIE,+CACA,qDAHA,uCADA,SzCijJJ,CyC1iJE,iBAGE,+CAEA,iDAHA,uCAEA,SnC2uCgC,CmC9uChC,SzCgjJJ,CyCziJE,qClBnDA,+CkBuDuB,CACrB,sDAFA,wCADA,SzC6iJJ,CyCviJE,yCAIE,kDACA,wDAHA,0CACA,mBzC0iJJ,CyCniJE,wCACE,2CzCsiJJ,CyCjiJM,kClC7BF,6DADA,yDPmkJJ,CyC/hJM,iClCjDF,8DADA,0DPqlJJ,CyClhJA,eClGE,iCACA,kCvC0RI,kCALI,CuCnRR,wD1CwnJF,CyCrhJA,eCtGE,iCACA,kCvC0RI,mCALI,CuCnRR,wD1C+nJF,C2CjoJA,OAEE,4BACA,4BxCuRI,2BALI,CwChRR,2BACA,sBACA,iDpCOE,4CoCCF,4BALA,qBxCgRI,mCALI,CwCxQR,wCACA,cAHA,4DAKA,kBAEA,wBADA,kB3CmoJF,C2C7nJE,aACE,Y3C+nJJ,C2C1nJA,YACE,kBACA,Q3C6nJF,C4C7pJA,OAEE,0BACA,0BACA,0BACA,8BACA,yBACA,oCACA,4EACA,iDACA,8BAOA,oCACA,8BrCHE,4CqCCF,4BADA,4CADA,4DADA,iB5CmqJF,C4CzpJA,eAEE,a5C2pJF,C4CvpJA,YAEE,iCADA,e5C2pJF,C4ClpJA,mBACE,kB5CqpJF,C4ClpJE,8BAKE,qBAJA,kBAEA,QADA,MAEA,S5CqpJJ,C4C5oJE,eACE,iDACA,0CACA,wDACA,qD5C+oJJ,C4CnpJE,iBACE,mDACA,4CACA,0DACA,uD5CspJJ,C4C1pJE,eACE,iDACA,0CACA,wDACA,qD5C6pJJ,C4CjqJE,YACE,8CACA,uCACA,qDACA,kD5CoqJJ,C4CxqJE,eACE,iDACA,0CACA,wDACA,qD5C2qJJ,C4C/qJE,cACE,gDACA,yCACA,uDACA,oD5CkrJJ,C4CtrJE,aACE,+CACA,wCACA,sDACA,mD5CyrJJ,C4C7rJE,YACE,8CACA,uCACA,qDACA,kD5CgsJJ,C6C5vJE,gCACE,GAAK,0B7CgwJP,CACF,C6C5vJA,4BAGE,0B1CkRI,gCALI,C0C3QR,wCACA,oDACA,oDACA,6BACA,6BACA,6CAOA,uCtCRE,+CsCIF,a1CwQI,sCALI,C0ClQR,iCACA,e7C8vJF,C6CvvJA,cAQE,2CAHA,mCAJA,aACA,sBACA,uBACA,gBAEA,kBvBtBI,4CuByBJ,CAFA,kB7C4vJF,CsB/wJM,uCuBYN,cvBXQ,etBkxJN,CACF,C6C5vJA,sBtBAE,sKsBEA,mE7C+vJF,C6C5vJA,4BACE,gB7C+vJF,C6C5vJA,0CACE,U7C+vJF,C6C3vJE,uBACE,iD7C8vJJ,C6C3vJM,uCAJJ,uBAKM,c7C8vJN,CACF,C8C1zJA,YAEE,2CACA,qCACA,oDACA,oDACA,sDACA,oCACA,sCACA,uDACA,4DACA,sDACA,yDACA,wDACA,yDACA,8CACA,kCACA,kCACA,4CvCHE,iDuCMF,aACA,sBAIA,gBADA,c9C0zJF,C8CrzJA,qBAEE,sBADA,oB9CyzJF,C8CtzJE,6CAEE,mCACA,yB9CuzJJ,C8C9yJA,wBAEE,wCACA,mBAFA,U9CmzJF,C8C9yJE,4DAKE,sDAFA,8CACA,qBAFA,S9CkzJJ,C8C5yJE,+BAEE,uDADA,8C9C+yJJ,C8CtyJA,iBAME,yCACA,iFAHA,iCAFA,cACA,gFAFA,kBAIA,oB9C2yJF,C8CvyJE,6BvCvDE,+BACA,+BPi2JJ,C8CvyJE,4BvC5CE,kCADA,kCPw1JJ,C8CvyJE,oDAIE,kDAFA,0CACA,mB9CyyJJ,C8CpyJE,wBAGE,gDACA,sDAFA,wCADA,S9CyyJJ,C8ClyJE,kCACE,kB9CoyJJ,C8ClyJI,yCAEE,mDADA,qD9CqyJN,C8CvxJI,uBACE,kB9C0xJN,C8CvxJQ,qEvCvDJ,6DAZA,yBP81JJ,C8CtxJQ,qEvC5DJ,4BAZA,0DPk2JJ,C8CrxJQ,+CACE,Y9CuxJV,C8CpxJQ,yDAEE,oBADA,kD9CuxJV,C8CpxJU,gEAEE,oDADA,sD9CuxJZ,Ca52JI,yBiC8DA,0BACE,kB9CkzJJ,C8C/yJM,wEvCvDJ,6DAZA,yBPs3JF,C8C9yJM,wEvC5DJ,4BAZA,0DP03JF,C8C7yJM,kDACE,Y9C+yJR,C8C5yJM,4DAEE,oBADA,kD9C+yJR,C8C5yJQ,mEAEE,oDADA,sD9C+yJV,CACF,Car4JI,yBiC8DA,0BACE,kB9C00JJ,C8Cv0JM,wEvCvDJ,6DAZA,yBP84JF,C8Ct0JM,wEvC5DJ,4BAZA,0DPk5JF,C8Cr0JM,kDACE,Y9Cu0JR,C8Cp0JM,4DAEE,oBADA,kD9Cu0JR,C8Cp0JQ,mEAEE,oDADA,sD9Cu0JV,CACF,Ca75JI,yBiC8DA,0BACE,kB9Ck2JJ,C8C/1JM,wEvCvDJ,6DAZA,yBPs6JF,C8C91JM,wEvC5DJ,4BAZA,0DP06JF,C8C71JM,kDACE,Y9C+1JR,C8C51JM,4DAEE,oBADA,kD9C+1JR,C8C51JQ,mEAEE,oDADA,sD9C+1JV,CACF,Car7JI,0BiC8DA,0BACE,kB9C03JJ,C8Cv3JM,wEvCvDJ,6DAZA,yBP87JF,C8Ct3JM,wEvC5DJ,4BAZA,0DPk8JF,C8Cr3JM,kDACE,Y9Cu3JR,C8Cp3JM,4DAEE,oBADA,kD9Cu3JR,C8Cp3JQ,mEAEE,oDADA,sD9Cu3JV,CACF,Ca78JI,0BiC8DA,2BACE,kB9Ck5JJ,C8C/4JM,yEvCvDJ,6DAZA,yBPs9JF,C8C94JM,yEvC5DJ,4BAZA,0DP09JF,C8C74JM,mDACE,Y9C+4JR,C8C54JM,6DAEE,oBADA,kD9C+4JR,C8C54JQ,oEAEE,oDADA,sD9C+4JV,CACF,C8Cj4JA,kBvChJI,ePohKJ,C8Cj4JE,mCACE,kD9Cm4JJ,C8Cj4JI,8CACE,qB9Cm4JN,C8Ct3JE,yBACE,sDACA,+CACA,6DACA,4DACA,gEACA,6DACA,iEACA,yDACA,0DACA,mE9Cy3JJ,C8Cn4JE,2BACE,wDACA,iDACA,+DACA,4DACA,kEACA,6DACA,mEACA,2DACA,4DACA,qE9Cs4JJ,C8Ch5JE,yBACE,sDACA,+CACA,6DACA,4DACA,gEACA,6DACA,iEACA,yDACA,0DACA,mE9Cm5JJ,C8C75JE,sBACE,mDACA,4CACA,0DACA,4DACA,6DACA,6DACA,8DACA,sDACA,uDACA,gE9Cg6JJ,C8C16JE,yBACE,sDACA,+CACA,6DACA,4DACA,gEACA,6DACA,iEACA,yDACA,0DACA,mE9C66JJ,C8Cv7JE,wBACE,qDACA,8CACA,4DACA,4DACA,+DACA,6DACA,gEACA,wDACA,yDACA,kE9C07JJ,C8Cp8JE,uBACE,oDACA,6CACA,2DACA,4DACA,8DACA,6DACA,+DACA,uDACA,wDACA,iE9Cu8JJ,C8Cj9JE,sBACE,mDACA,4CACA,0DACA,4DACA,6DACA,6DACA,8DACA,sDACA,uDACA,gE9Co9JJ,C+ChpKA,WAEE,0BACA,oVACA,2BACA,kCACA,+DACA,+BACA,qCACA,uEAQA,wEACA,SxCJE,sBwCFF,uBAEA,UzCopD2B,CyC9oD3B,oCALA,cAFA,S/CupKF,C+C7oKE,4BAPA,+B/CypKF,C+ClpKE,iBAGE,0CADA,oB/CgpKJ,C+C5oKE,iBAEE,4CACA,0CAFA,S/CgpKJ,C+C3oKE,wCAIE,6CAFA,oBACA,+D/C6oKJ,C+C9nKI,iDATF,uC/C+oKF,CgDhsKA,OAEE,uBACA,6BACA,4BACA,0BACA,2B7CyRI,6BALI,C6ClRR,mBACA,+CACA,+CACA,2DACA,iDACA,2CACA,kDACA,sDACA,kEASA,4BADA,oCAEA,uEzCPE,4CyCQF,sCALA,4B7CyQI,mCALI,C6CtQR,eAGA,oBAJA,+BhDysKF,CgD9rKE,eACE,ShDgsKJ,CgD7rKE,kBACE,YhD+rKJ,CgD3rKA,iBACE,uBAKA,eACA,oBAJA,kBAEA,yCADA,8BhDgsKF,CgD3rKE,mCACE,qChD6rKJ,CgDzrKA,cAEE,mBAIA,4BADA,2CAEA,qFzChCE,0FACA,2FyC4BF,mCAHA,aAEA,2DhDksKF,CgD3rKE,yBAEE,sCADA,gDhD8rKJ,CgDzrKA,YAEE,qBADA,iChD6rKF,CiD1vKA,OAEE,uBACA,uBACA,wBACA,yBACA,mBACA,gCACA,2DACA,+CACA,oDACA,8CACA,yFACA,iCACA,iCACA,oCACA,sDACA,sDACA,iCACA,6BACA,uBACA,sDACA,sDAOA,aAEA,YAJA,OASA,UAJA,kBACA,gBARA,eACA,MAIA,WAFA,8BjDgwKF,CiDjvKA,cAGE,8BAEA,oBAJA,kBACA,UjDsvKF,CiDhvKE,0BAEE,2B3Ck8CgC,CgBh/C9B,iCtBgyKN,CsB5xKM,uC2BwCJ,0B3BvCM,etB+xKN,CACF,CiDrvKE,0BACE,cjDuvKJ,CiDnvKE,kCACE,qBjDqvKJ,CiDjvKA,yBACE,4CjDovKF,CiDlvKE,wCACE,gBACA,ejDovKJ,CiDjvKE,qCACE,ejDmvKJ,CiD/uKA,uBAEE,mBADA,aAEA,gDjDkvKF,CiD9uKA,eASE,4BADA,oCAEA,uE1CrFE,4C0CiFF,4BAJA,aACA,sBAWA,UAPA,oBANA,kBAGA,UjDwvKF,CiD1uKA,gBAEE,0BACA,sBACA,0BC5GA,sCD+G4D,CChH5D,aAHA,OAFA,eACA,MAGA,YADA,iClDk2KF,CkD51KE,qBAAS,SlD+1KX,CkD91KE,qBAAS,kClDi2KX,CiDjvKA,cAGE,mBAEA,4F1CrGE,2DACA,4D0CgGF,aACA,cAEA,sCjDuvKF,CiDnvKE,yBAEE,6IADA,2FjDsvKJ,CiDhvKA,aAEE,8CADA,ejDovKF,CiD9uKA,YAIE,cACA,gCAJA,iBjDmvKF,CiD3uKA,cAIE,mBAGA,2C1CvHE,8DADA,+D0CyHF,yFAPA,aACA,cACA,eAEA,yBACA,qEjDkvKF,CiD1uKE,gBACE,0CjD4uKJ,Cav1KI,yBoCiHF,OACE,0BACA,0CjD0uKF,CiDtuKA,cAGE,iBADA,kBADA,+BjD0uKF,CiDruKA,UACE,sBjDuuKF,CACF,Cat2KI,yBoCmIF,oBAEE,sBjDsuKF,CACF,Ca52KI,0BoC0IF,UACE,uBjDquKF,CACF,CiD5tKI,kBAGE,YACA,SAFA,eADA,WjDiuKN,CiD5tKM,iCAEE,S1CzMJ,gB0CwMI,WjDguKR,CiD3tKM,gE1C7MF,eP46KJ,CiD1tKM,8BACE,ejD4tKR,Cat3KI,4BoCwIA,0BAGE,YACA,SAFA,eADA,WjDqvKJ,CiDhvKI,yCAEE,S1CzMJ,gB0CwMI,WjDovKN,CiD/uKI,gF1C7MF,ePg8KF,CiD9uKI,sCACE,ejDgvKN,CACF,Ca34KI,4BoCwIA,0BAGE,YACA,SAFA,eADA,WjDywKJ,CiDpwKI,yCAEE,S1CzMJ,gB0CwMI,WjDwwKN,CiDnwKI,gF1C7MF,ePo9KF,CiDlwKI,sCACE,ejDowKN,CACF,Ca/5KI,4BoCwIA,0BAGE,YACA,SAFA,eADA,WjD6xKJ,CiDxxKI,yCAEE,S1CzMJ,gB0CwMI,WjD4xKN,CiDvxKI,gF1C7MF,ePw+KF,CiDtxKI,sCACE,ejDwxKN,CACF,Can7KI,6BoCwIA,0BAGE,YACA,SAFA,eADA,WjDizKJ,CiD5yKI,yCAEE,S1CzMJ,gB0CwMI,WjDgzKN,CiD3yKI,gF1C7MF,eP4/KF,CiD1yKI,sCACE,ejD4yKN,CACF,Cav8KI,6BoCwIA,2BAGE,YACA,SAFA,eADA,WjDq0KJ,CiDh0KI,0CAEE,S1CzMJ,gB0CwMI,WjDo0KN,CiD/zKI,kF1C7MF,ePghLF,CiD9zKI,uCACE,ejDg0KN,CACF,CmDtiLA,SAEE,yBACA,6BACA,8BACA,+BACA,sBhDwRI,gCALI,CgDjRR,qCACA,yCACA,mDACA,yBACA,gCACA,iCAYA,qBARA,cCjBA,qC9C+lB4B,CHjUxB,qCALI,CiDvRR,kBACA,e9CwmB4B,C8CjmB5B,sBAIA,gBAVA,ejCCiB,CgCajB,gCAQA,UCrBA,gBACA,iBACA,qBACA,iBACA,oBAGA,mBADA,kBAEA,oBDGA,gCnDwjLF,CmD5iLE,cAAS,iCnD+iLX,CmD7iLE,wBACE,cAEA,sCADA,mCnDgjLJ,CmD7iLI,+BAGE,yBACA,mBAFA,WADA,iBnDkjLN,CmD1iLA,2FACE,8CnD6iLF,CmD3iLE,yGAGE,sCADA,qFADA,QnD+iLJ,CmDxiLA,6FAGE,qCAFA,6CACA,oCnD6iLF,CmD1iLE,2GAGE,wCADA,4HADA,UnD8iLJ,CmDtiLA,iGACE,2CnD0iLF,CmDxiLE,+GAGE,yCADA,qFADA,WnD4iLJ,CmDriLA,8FAGE,qCAFA,8CACA,oCnD0iLF,CmDviLE,4GAGE,uCADA,4HADA,SnD2iLJ,CmDnhLA,eAKE,sC5CjGE,8C4C+FF,8BAFA,sCACA,gEAEA,iBnDyhLF,CqD5oLA,SAEE,yBACA,6BlD4RI,gCALI,CkDrRR,kCACA,iDACA,6DACA,sDACA,2FACA,6CACA,mCACA,qClDmRI,oCALI,CkD5QR,kCACA,8CACA,iCACA,iCACA,6CACA,8BACA,iCACA,yDAWA,qBAEA,4BADA,sCAEA,2E9ChBE,8C8CMF,cDxBA,qC9C+lB4B,CHjUxB,qCALI,CiDvRR,kBACA,e9CwmB4B,C8CjmB5B,sBAIA,gBAVA,ejCCiB,CkCoBjB,sCDpBA,gBACA,iBACA,qBACA,iBACA,oBAGA,mBADA,kBAEA,oBCUA,gCrDkqLF,CqDnpLE,wBACE,cAEA,sCADA,mCrDspLJ,CqDnpLI,6DAOE,2BAHA,WADA,cADA,iBrDypLN,CqD9oLE,2FACE,iFrDipLJ,CqD/oLI,gNAEE,oFrDgpLN,CqD7oLI,yGAEE,gDADA,QrDgpLN,CqD5oLI,uGAEE,sCADA,qCrD+oLN,CqDvoLE,6FAGE,qCAFA,gFACA,oCrD4oLJ,CqDzoLI,oNAEE,2HrD0oLN,CqDvoLI,2GAEE,kDADA,MrD0oLN,CqDtoLI,yGAEE,wCADA,mCrDyoLN,CqDhoLE,iGACE,8ErDooLJ,CqDloLI,4NAEE,oFrDmoLN,CqDhoLI,+GAEE,mDADA,KrDmoLN,CqD/nLI,6GAEE,yCADA,kCrDkoLN,CqD5nLE,iHAQE,+EADA,WAHA,cADA,SAGA,oDALA,kBACA,MAGA,mCrDioLJ,CqDxnLE,8FAGE,qCAFA,iFACA,oCrD6nLJ,CqD1nLI,sNAEE,2HrD2nLN,CqDxnLI,4GAEE,iDADA,OrD2nLN,CqDvnLI,0GAEE,uCADA,oCrD0nLN,CqDlmLA,gBAKE,6CACA,kF9C5JE,6DACA,8D8CyJF,qClDyGI,4CALI,CkDtGR,gBADA,6ErD6mLF,CqDrmLE,sBACE,YrDumLJ,CqDnmLA,cAEE,mCADA,yErDumLF,CsD3xLA,UACE,iBtD8xLF,CsD3xLA,wBACE,kBtD8xLF,CsD3xLA,gBAGE,gBAFA,kBACA,UtD+xLF,CuDpzLE,sBAEE,WACA,WAFA,avDwzLJ,CsD/xLA,eAME,2BAJA,aACA,WAEA,mBAJA,kBhCbI,oCgCmBJ,CAHA,UtDqyLF,CsBjzLM,uCgCQN,ehCPQ,etBozLN,CACF,CsDpyLA,8DAGE,atDuyLF,CsDpyLA,wEAEE,0BtDuyLF,CsDpyLA,wEAEE,2BtDuyLF,CsD9xLE,8BACE,UAEA,eADA,2BtDkyLJ,CsD9xLE,iJAIE,UADA,StDiyLJ,CsD7xLE,oFAGE,UhC5DE,yBgC6DF,CAFA,StDiyLJ,CsBx1LM,uCgCqDJ,oFhCpDM,etB41LN,CACF,CsD5xLA,8CAQE,mBAMA,gBACA,SAXA,SAQA,UhD1FS,CgDqFT,aAEA,uBAOA,UhD6gDmC,CgDlhDnC,UATA,kBAWA,kBAVA,MhCzEI,4BgCuFJ,CAPA,ShDkhDmC,CgDvhDnC,StD0yLF,CsBj3LM,uCgCkEN,8ChCjEQ,etBq3LN,CACF,CsDjyLE,oHAEE,UhDpGO,CgDuGP,UhDqgDiC,CgDtgDjC,UADA,oBtDsyLJ,CsDjyLA,uBACE,MtDoyLF,CsDjyLA,uBACE,OtDoyLF,CsD/xLA,wDAME,wBADA,4BAEA,0BALA,qBAEA,WhDqgDmC,CgDtgDnC,UtDsyLF,CsD/xLA,4BACE,sRtDkyLF,CsDhyLA,4BACE,uRtDmyLF,CsD3xLA,qBAGE,SAGA,aACA,uBAHA,OAOA,mBACA,ehDo9CmC,CgDt9CnC,gBhDs9CmC,CgDx9CnC,UAPA,kBACA,QAGA,StDoyLF,CsD3xLE,sCAWE,4BADA,qBhDlKO,CgDoKP,SAGA,qCADA,kCAbA,uBAQA,eAPA,cAEA,UhDo9CiC,CgDj9CjC,ehDm9CiC,CgDp9CjC,gBhDo9CiC,CgD18CjC,UhD28CiC,CgDt9CjC,UAGA,mBhCxJE,2BgCiKF,CAdA,UtD0yLJ,CsBz7LM,uCgC4IJ,sChC3IM,etB47LN,CACF,CsD9xLE,6BACE,StDgyLJ,CsDvxLA,kBAGE,chDk8CmC,CgD97CnC,UhD7LS,CgD0LT,SAEA,sBhD87CmC,CgD/7CnC,mBhD+7CmC,CgDn8CnC,kBACA,UAMA,iBtD0xLF,CsDpxLE,sFAEE,+BtDuxLJ,CsDpxLE,qDACE,qBtDsxLJ,CsDnxLE,iCACE,UtDqxLJ,CsD/xLE,0OAEE,+BtDmyLJ,CsDhyLE,yIACE,qBtDkyLJ,CsD/xLE,iGACE,UtDiyLJ,CwDn/LA,8BAQE,6FADA,kBALA,qBAEA,gCACA,gDAFA,6BxD0/LF,CwDj/LA,0BACE,GAAK,uBxDq/LL,CACF,CwDl/LA,gBAEE,wBACA,yBACA,qCACA,iCACA,mCACA,2CAGA,gCACA,yGxDi/LF,CwD9+LA,mBAEE,wBACA,yBACA,+BxDg/LF,CwDv+LA,wBACE,GACE,kBxD0+LF,CwDx+LA,IACE,UACA,cxD0+LF,CACF,CwDt+LA,cAEE,wBACA,yBACA,qCACA,mCACA,yCAGA,8BACA,SxDq+LF,CwDl+LA,iBACE,wBACA,wBxDq+LF,CwDj+LE,uCACE,8BAEE,iCxDo+LJ,CACF,CyDpjMA,kFAEE,2BACA,2BACA,2BACA,8BACA,8BACA,0CACA,oCACA,mDACA,+DACA,kDACA,qDACA,oCzDqjMF,Cax/LI,4B4C5CF,cAWI,4BADA,wCAPA,SAKA,gCAHA,aACA,sBACA,eAKA,UAVA,enClBA,yCmC8BA,CALA,kBALA,kCzDgjMJ,CACF,CsBjkMM,gEmCYJ,cnCXM,etBokMN,CACF,Ca9gMI,4B4C5BE,8BAIE,qFAFA,OADA,MAIA,4BAFA,+BzD+iMN,CyD1iMI,4BAIE,oFAFA,QADA,MAIA,2BAFA,+BzD8iMN,CyDziMI,4BAME,sFALA,MAMA,2BzD2iMN,CyDxiMI,2DANE,kCADA,OAEA,gBAHA,OzDwjMN,CyDhjMI,+BAKE,mFACA,0BzD0iMN,CyDviMI,sDAEE,czDwiMN,CyDriMI,8DAGE,kBzDqiMN,CACF,CalkMI,yB4C/BF,cAiEM,2BACA,8BACA,sCzDoiMN,CyDliMM,gCACE,YzDoiMR,CyDjiMM,8BAME,uCALA,aACA,YAEA,mBADA,SzDqiMR,CACF,CatkMI,4B4C5CF,cAWI,4BADA,wCAPA,SAKA,gCAHA,aACA,sBACA,eAKA,UAVA,enClBA,yCmC8BA,CALA,kBALA,kCzD8nMJ,CACF,CsB/oMM,gEmCYJ,cnCXM,etBkpMN,CACF,Ca5lMI,4B4C5BE,8BAIE,qFAFA,OADA,MAIA,4BAFA,+BzD6nMN,CyDxnMI,4BAIE,oFAFA,QADA,MAIA,2BAFA,+BzD4nMN,CyDvnMI,4BAME,sFALA,MAMA,2BzDynMN,CyDtnMI,2DANE,kCADA,OAEA,gBAHA,OzDsoMN,CyD9nMI,+BAKE,mFACA,0BzDwnMN,CyDrnMI,sDAEE,czDsnMN,CyDnnMI,8DAGE,kBzDmnMN,CACF,CahpMI,yB4C/BF,cAiEM,2BACA,8BACA,sCzDknMN,CyDhnMM,gCACE,YzDknMR,CyD/mMM,8BAME,uCALA,aACA,YAEA,mBADA,SzDmnMR,CACF,CappMI,4B4C5CF,cAWI,4BADA,wCAPA,SAKA,gCAHA,aACA,sBACA,eAKA,UAVA,enClBA,yCmC8BA,CALA,kBALA,kCzD4sMJ,CACF,CsB7tMM,gEmCYJ,cnCXM,etBguMN,CACF,Ca1qMI,4B4C5BE,8BAIE,qFAFA,OADA,MAIA,4BAFA,+BzD2sMN,CyDtsMI,4BAIE,oFAFA,QADA,MAIA,2BAFA,+BzD0sMN,CyDrsMI,4BAME,sFALA,MAMA,2BzDusMN,CyDpsMI,2DANE,kCADA,OAEA,gBAHA,OzDotMN,CyD5sMI,+BAKE,mFACA,0BzDssMN,CyDnsMI,sDAEE,czDosMN,CyDjsMI,8DAGE,kBzDisMN,CACF,Ca9tMI,yB4C/BF,cAiEM,2BACA,8BACA,sCzDgsMN,CyD9rMM,gCACE,YzDgsMR,CyD7rMM,8BAME,uCALA,aACA,YAEA,mBADA,SzDisMR,CACF,CaluMI,6B4C5CF,cAWI,4BADA,wCAPA,SAKA,gCAHA,aACA,sBACA,eAKA,UAVA,enClBA,yCmC8BA,CALA,kBALA,kCzD0xMJ,CACF,CsB3yMM,iEmCYJ,cnCXM,etB8yMN,CACF,CaxvMI,6B4C5BE,8BAIE,qFAFA,OADA,MAIA,4BAFA,+BzDyxMN,CyDpxMI,4BAIE,oFAFA,QADA,MAIA,2BAFA,+BzDwxMN,CyDnxMI,4BAME,sFALA,MAMA,2BzDqxMN,CyDlxMI,2DANE,kCADA,OAEA,gBAHA,OzDkyMN,CyD1xMI,+BAKE,mFACA,0BzDoxMN,CyDjxMI,sDAEE,czDkxMN,CyD/wMI,8DAGE,kBzD+wMN,CACF,Ca5yMI,0B4C/BF,cAiEM,2BACA,8BACA,sCzD8wMN,CyD5wMM,gCACE,YzD8wMR,CyD3wMM,8BAME,uCALA,aACA,YAEA,mBADA,SzD+wMR,CACF,CahzMI,6B4C5CF,eAWI,4BADA,wCAPA,SAKA,gCAHA,aACA,sBACA,eAKA,UAVA,enClBA,yCmC8BA,CALA,kBALA,kCzDw2MJ,CACF,CsBz3MM,iEmCYJ,enCXM,etB43MN,CACF,Cat0MI,6B4C5BE,+BAIE,qFAFA,OADA,MAIA,4BAFA,+BzDu2MN,CyDl2MI,6BAIE,oFAFA,QADA,MAIA,2BAFA,+BzDs2MN,CyDj2MI,6BAME,sFALA,MAMA,2BzDm2MN,CyDh2MI,6DANE,kCADA,OAEA,gBAHA,OzDg3MN,CyDx2MI,gCAKE,mFACA,0BzDk2MN,CyD/1MI,wDAEE,czDg2MN,CyD71MI,iEAGE,kBzD61MN,CACF,Ca13MI,0B4C/BF,eAiEM,2BACA,8BACA,sCzD41MN,CyD11MM,iCACE,YzD41MR,CyDz1MM,+BAME,uCALA,aACA,YAEA,mBADA,SzD61MR,CACF,CyD16ME,WAWI,4BADA,wCAPA,SAKA,gCAHA,aACA,sBACA,eAKA,UAVA,enClBA,yCmC8BA,CALA,kBALA,kCzDq7MN,CsBr8MM,uCmCYJ,WnCXM,etBw8MN,CACF,CyD96MM,2BAIE,qFAFA,OADA,MAIA,4BAFA,+BzDk7MR,CyD76MM,yBAIE,oFAFA,QADA,MAIA,2BAFA,+BzDi7MR,CyD56MM,yBAME,sFALA,MAMA,2BzD86MR,CyD36MM,qDANE,kCADA,OAEA,gBAHA,OzD27MR,CyDn7MM,4BAKE,mFACA,0BzD66MR,CyD16MM,gDAEE,czD26MR,CyDx6MM,qDAGE,kBzDw6MR,CyD74MA,oBP9GE,qB5CUS,C4CXT,aAHA,OAFA,eACA,MAGA,YADA,YlDwgNF,CkDlgNE,yBAAS,SlDqgNX,CkDpgNE,yBAAS,UlDugNX,CyDz5MA,kBAEE,mBADA,aAEA,mEzD45MF,CyD15ME,6BAEE,oIADA,qFzD65MJ,CyDx5MA,iBAEE,kDADA,ezD45MF,CyDx5MA,gBACE,YAEA,gBADA,mEzD45MF,C0DxiNA,aAKE,8BADA,YAHA,qBACA,eAIA,UpDgzCkC,CoDnzClC,qB1D8iNF,C0DziNE,wBAEE,WADA,oB1D4iNJ,C0DtiNA,gBACE,e1DyiNF,C0DtiNA,gBACE,e1DyiNF,C0DtiNA,gBACE,gB1DyiNF,C0DpiNE,+BACE,kD1DuiNJ,C0DniNA,4BACE,IACE,U1DsiNF,CACF,C0DniNA,kBAGE,8CAFA,wJACA,+C1DsiNF,C0DliNA,4BACE,GACE,mD1DqiNF,CACF,CuDplNE,gBAEE,WACA,WAFA,avDwlNJ,C2DzlNE,iBAEE,8EADA,oB3D6lNJ,C2D9lNE,mBAEE,gFADA,oB3DkmNJ,C2DnmNE,iBAEE,8EADA,oB3DumNJ,C2DxmNE,cAEE,2EADA,oB3D4mNJ,C2D7mNE,iBAEE,8EADA,oB3DinNJ,C2DlnNE,gBAEE,6EADA,oB3DsnNJ,C2DvnNE,eAEE,4EADA,oB3D2nNJ,C2D5nNE,cAEE,2EADA,oB3DgoNJ,C4DjoNE,cACE,qEACA,8F5DooNJ,C4DjoNM,wCAGE,yDACA,kF5DioNR,C4D1oNE,gBACE,uEACA,gG5D6oNJ,C4D1oNM,4CAGE,yDACA,kF5D0oNR,C4DnpNE,cACE,qEACA,8F5DspNJ,C4DnpNM,wCAGE,yDACA,kF5DmpNR,C4D5pNE,WACE,kEACA,2F5D+pNJ,C4D5pNM,kCAGE,0DACA,mF5D4pNR,C4DrqNE,cACE,qEACA,8F5DwqNJ,C4DrqNM,wCAGE,0DACA,mF5DqqNR,C4D9qNE,aACE,oEACA,6F5DirNJ,C4D9qNM,sCAGE,yDACA,kF5D8qNR,C4DvrNE,YACE,mEACA,4F5D0rNJ,C4DvrNM,oCAGE,2DACA,oF5DurNR,C4DhsNE,WACE,kEACA,2F5DmsNJ,C4DhsNM,kCAGE,wDACA,iF5DgsNR,C4DzrNA,oBACE,4EACA,qG5D4rNF,C4DzrNI,oDAEE,8EACA,uG5D0rNN,C6DptNA,kBAGE,+IAFA,S7DwtNF,C8DztNA,WAGE,mBAGA,2BALA,oBACA,WxD6c4B,CwD3c5B,+EACA,2B9D6tNF,C8D1tNE,eAIE,kBAHA,cAEA,UxDsc0B,CgBjcxB,oCwCHF,CAHA,S9D+tNJ,CsBrtNM,uCwCZJ,exCaM,etBwtNN,CACF,C8D1tNI,8DACE,8D9D6tNN,C+DhvNA,OACE,kBACA,U/DmvNF,C+DjvNE,cAGE,WAFA,cACA,kC/DovNJ,C+DhvNE,SAKE,YAFA,OAFA,kBACA,MAEA,U/DmvNJ,C+D7uNE,WACE,sB/DgvNJ,C+DjvNE,WACE,qB/DovNJ,C+DrvNE,YACE,wB/DwvNJ,C+DzvNE,YACE,gC/D4vNJ,CgEjxNA,WAEE,KhEuxNF,CgEjxNA,yBAJE,OAHA,eAEA,QAEA,YhE4xNF,CgEzxNA,cAGE,QhEsxNF,CgE5wNI,YAEE,KhEgxNN,CgE5wNI,2BALE,gBAEA,YhEqxNN,CgElxNI,eAEE,QhEgxNN,CahvNI,yBmDxCA,eACE,gBACA,MACA,YhE4xNJ,CgEzxNE,kBAEE,SADA,gBAEA,YhE2xNJ,CACF,Ca7vNI,yBmDxCA,eACE,gBACA,MACA,YhEwyNJ,CgEryNE,kBAEE,SADA,gBAEA,YhEuyNJ,CACF,CazwNI,yBmDxCA,eACE,gBACA,MACA,YhEozNJ,CgEjzNE,kBAEE,SADA,gBAEA,YhEmzNJ,CACF,CarxNI,0BmDxCA,eACE,gBACA,MACA,YhEg0NJ,CgE7zNE,kBAEE,SADA,gBAEA,YhE+zNJ,CACF,CajyNI,0BmDxCA,gBACE,gBACA,MACA,YhE40NJ,CgEz0NE,mBAEE,SADA,gBAEA,YhE20NJ,CACF,CiE32NA,QAGE,mBADA,kBjE+2NF,CiE12NA,gBAHE,mBAHA,YjEu3NF,CiEj3NA,QAEE,cACA,qBjE82NF,CkEr3NA,2ECSE,6BAEA,mBANA,qBAEA,sBACA,0BAFA,oBAIA,6BANA,mBnE63NF,CmEn3NE,qGACE,2BnEs3NJ,CoEp4NE,sBAIE,SAGA,WAFA,OAJA,kBAEA,QADA,MAIA,SpEw4NJ,CqE/4NA,eCAE,gBACA,uBACA,kBtEm5NF,CuEz5NA,IAEE,mBAGA,8BAJA,qBAGA,eAEA,WjE2rB4B,CiE9rB5B,4BvE+5NF,CwEh2NQ,gBAOI,iCxE61NZ,CwEp2NQ,WAOI,4BxEi2NZ,CwEx2NQ,cAOI,+BxEq2NZ,CwE52NQ,cAOI,+BxEy2NZ,CwEh3NQ,mBAOI,oCxE62NZ,CwEp3NQ,gBAOI,iCxEi3NZ,CwEx3NQ,aAOI,oBxEq3NZ,CwE53NQ,WAOI,qBxEy3NZ,CwEh4NQ,YAOI,oBxE63NZ,CwEp4NQ,oBAOI,4DxEi4NZ,CwEx4NQ,kBAOI,wDxEq4NZ,CwE54NQ,iBAOI,sDxEy4NZ,CwEh5NQ,kBAOI,kExE64NZ,CwEp5NQ,iBAOI,sDxEi5NZ,CwEx5NQ,WAOI,mBxEq5NZ,CwE55NQ,YAOI,qBxEy5NZ,CwEh6NQ,YAOI,oBxE65NZ,CwEp6NQ,YAOI,qBxEi6NZ,CwEx6NQ,aAOI,mBxEq6NZ,CwE56NQ,eAOI,uBxEy6NZ,CwEh7NQ,iBAOI,yBxE66NZ,CwEp7NQ,kBAOI,0BxEi7NZ,CwEx7NQ,iBAOI,yBxEq7NZ,CwE57NQ,iBAOI,yBxEy7NZ,CwEh8NQ,mBAOI,2BxE67NZ,CwEp8NQ,oBAOI,4BxEi8NZ,CwEx8NQ,mBAOI,2BxEq8NZ,CwE58NQ,iBAOI,yBxEy8NZ,CwEh9NQ,mBAOI,2BxE68NZ,CwEp9NQ,oBAOI,4BxEi9NZ,CwEx9NQ,mBAOI,2BxEq9NZ,CwE59NQ,UAOI,wBxEy9NZ,CwEh+NQ,gBAOI,8BxE69NZ,CwEp+NQ,SAOI,uBxEi+NZ,CwEx+NQ,QAOI,sBxEq+NZ,CwE5+NQ,eAOI,6BxEy+NZ,CwEh/NQ,SAOI,uBxE6+NZ,CwEp/NQ,aAOI,2BxEi/NZ,CwEx/NQ,cAOI,4BxEq/NZ,CwE5/NQ,QAOI,sBxEy/NZ,CwEhgOQ,eAOI,6BxE6/NZ,CwEpgOQ,QAOI,sBxEigOZ,CwExgOQ,QAOI,yCxEqgOZ,CwE5gOQ,WAOI,4CxEygOZ,CwEhhOQ,WAOI,4CxE6gOZ,CwEphOQ,aAOI,yBxEihOZ,CwEliOQ,oBACE,8ExEqiOV,CwEtiOQ,sBACE,gFxEyiOV,CwE1iOQ,oBACE,8ExE6iOV,CwE9iOQ,iBACE,2ExEijOV,CwEljOQ,oBACE,8ExEqjOV,CwEtjOQ,mBACE,6ExEyjOV,CwE1jOQ,kBACE,4ExE6jOV,CwE9jOQ,iBACE,2ExEikOV,CwExjOQ,iBAOI,yBxEqjOZ,CwE5jOQ,mBAOI,2BxEyjOZ,CwEhkOQ,mBAOI,2BxE6jOZ,CwEpkOQ,gBAOI,wBxEikOZ,CwExkOQ,iBAOI,yBxEqkOZ,CwE5kOQ,OAOI,exEykOZ,CwEhlOQ,QAOI,iBxE6kOZ,CwEplOQ,SAOI,kBxEilOZ,CwExlOQ,UAOI,kBxEqlOZ,CwE5lOQ,WAOI,oBxEylOZ,CwEhmOQ,YAOI,qBxE6lOZ,CwEpmOQ,SAOI,gBxEimOZ,CwExmOQ,UAOI,kBxEqmOZ,CwE5mOQ,WAOI,mBxEymOZ,CwEhnOQ,OAOI,iBxE6mOZ,CwEpnOQ,QAOI,mBxEinOZ,CwExnOQ,SAOI,oBxEqnOZ,CwE5nOQ,kBAOI,wCxEynOZ,CwEhoOQ,oBAOI,oCxE6nOZ,CwEpoOQ,oBAOI,oCxEioOZ,CwExoOQ,QAOI,qFxEqoOZ,CwE5oOQ,UAOI,kBxEyoOZ,CwEhpOQ,YAOI,yFxE6oOZ,CwEppOQ,cAOI,sBxEipOZ,CwExpOQ,YAOI,2FxEqpOZ,CwE5pOQ,cAOI,wBxEypOZ,CwEhqOQ,eAOI,4FxE6pOZ,CwEpqOQ,iBAOI,yBxEiqOZ,CwExqOQ,cAOI,0FxEqqOZ,CwE5qOQ,gBAOI,uBxEyqOZ,CwEhrOQ,gBAIQ,sBAGJ,2ExE8qOZ,CwErrOQ,kBAIQ,sBAGJ,6ExEmrOZ,CwE1rOQ,gBAIQ,sBAGJ,2ExEwrOZ,CwE/rOQ,aAIQ,sBAGJ,wExE6rOZ,CwEpsOQ,gBAIQ,sBAGJ,2ExEksOZ,CwEzsOQ,eAIQ,sBAGJ,0ExEusOZ,CwE9sOQ,cAIQ,sBAGJ,yExE4sOZ,CwEntOQ,aAIQ,sBAGJ,wExEitOZ,CwExtOQ,cAIQ,sBAGJ,yExEstOZ,CwE7tOQ,cAIQ,sBAGJ,yExE2tOZ,CwEluOQ,uBAOI,sDxE+tOZ,CwEtuOQ,yBAOI,wDxEmuOZ,CwE1uOQ,uBAOI,sDxEuuOZ,CwE9uOQ,oBAOI,mDxE2uOZ,CwElvOQ,uBAOI,sDxE+uOZ,CwEtvOQ,sBAOI,qDxEmvOZ,CwE1vOQ,qBAOI,oDxEuvOZ,CwE9vOQ,oBAOI,mDxE2vOZ,CwElwOQ,UAOI,0BxE+vOZ,CwEtwOQ,UAOI,0BxEmwOZ,CwE1wOQ,UAOI,0BxEuwOZ,CwE9wOQ,UAOI,0BxE2wOZ,CwElxOQ,UAOI,0BxE+wOZ,CwEhyOQ,mBACE,uBxEmyOV,CwEpyOQ,mBACE,wBxEuyOV,CwExyOQ,mBACE,uBxE2yOV,CwE5yOQ,mBACE,wBxE+yOV,CwEhzOQ,oBACE,qBxEmzOV,CwE1yOQ,MAOI,mBxEuyOZ,CwE9yOQ,MAOI,mBxE2yOZ,CwElzOQ,MAOI,mBxE+yOZ,CwEtzOQ,OAOI,oBxEmzOZ,CwE1zOQ,QAOI,oBxEuzOZ,CwE9zOQ,QAOI,wBxE2zOZ,CwEl0OQ,QAOI,qBxE+zOZ,CwEt0OQ,YAOI,yBxEm0OZ,CwE10OQ,MAOI,oBxEu0OZ,CwE90OQ,MAOI,oBxE20OZ,CwEl1OQ,MAOI,oBxE+0OZ,CwEt1OQ,OAOI,qBxEm1OZ,CwE11OQ,QAOI,qBxEu1OZ,CwE91OQ,QAOI,yBxE21OZ,CwEl2OQ,QAOI,sBxE+1OZ,CwEt2OQ,YAOI,0BxEm2OZ,CwE12OQ,WAOI,uBxEu2OZ,CwE92OQ,UAOI,4BxE22OZ,CwEl3OQ,aAOI,+BxE+2OZ,CwEt3OQ,kBAOI,oCxEm3OZ,CwE13OQ,qBAOI,uCxEu3OZ,CwE93OQ,aAOI,qBxE23OZ,CwEl4OQ,aAOI,qBxE+3OZ,CwEt4OQ,eAOI,uBxEm4OZ,CwE14OQ,eAOI,uBxEu4OZ,CwE94OQ,WAOI,wBxE24OZ,CwEl5OQ,aAOI,0BxE+4OZ,CwEt5OQ,mBAOI,gCxEm5OZ,CwE15OQ,uBAOI,oCxEu5OZ,CwE95OQ,qBAOI,kCxE25OZ,CwEl6OQ,wBAOI,gCxE+5OZ,CwEt6OQ,yBAOI,uCxEm6OZ,CwE16OQ,wBAOI,sCxEu6OZ,CwE96OQ,wBAOI,sCxE26OZ,CwEl7OQ,mBAOI,gCxE+6OZ,CwEt7OQ,iBAOI,8BxEm7OZ,CwE17OQ,oBAOI,4BxEu7OZ,CwE97OQ,sBAOI,8BxE27OZ,CwEl8OQ,qBAOI,6BxE+7OZ,CwEt8OQ,qBAOI,kCxEm8OZ,CwE18OQ,mBAOI,gCxEu8OZ,CwE98OQ,sBAOI,8BxE28OZ,CwEl9OQ,uBAOI,qCxE+8OZ,CwEt9OQ,sBAOI,oCxEm9OZ,CwE19OQ,uBAOI,+BxEu9OZ,CwE99OQ,iBAOI,yBxE29OZ,CwEl+OQ,kBAOI,+BxE+9OZ,CwEt+OQ,gBAOI,6BxEm+OZ,CwE1+OQ,mBAOI,2BxEu+OZ,CwE9+OQ,qBAOI,6BxE2+OZ,CwEl/OQ,oBAOI,4BxE++OZ,CwEt/OQ,aAOI,kBxEm/OZ,CwE1/OQ,SAOI,iBxEu/OZ,CwE9/OQ,SAOI,iBxE2/OZ,CwElgPQ,SAOI,iBxE+/OZ,CwEtgPQ,SAOI,iBxEmgPZ,CwE1gPQ,SAOI,iBxEugPZ,CwE9gPQ,SAOI,iBxE2gPZ,CwElhPQ,YAOI,iBxE+gPZ,CwEthPQ,KAOI,kBxEmhPZ,CwE1hPQ,KAOI,uBxEuhPZ,CwE9hPQ,KAOI,sBxE2hPZ,CwEliPQ,KAOI,qBxE+hPZ,CwEtiPQ,KAOI,uBxEmiPZ,CwE1iPQ,KAOI,qBxEuiPZ,CwE9iPQ,QAOI,qBxE2iPZ,CwEljPQ,MAOI,gDxEgjPZ,CwEvjPQ,MAOI,0DxEqjPZ,CwE5jPQ,MAOI,wDxE0jPZ,CwEjkPQ,MAOI,sDxE+jPZ,CwEtkPQ,MAOI,0DxEokPZ,CwE3kPQ,MAOI,sDxEykPZ,CwEhlPQ,SAOI,sDxE8kPZ,CwErlPQ,MAOI,gDxEmlPZ,CwE1lPQ,MAOI,0DxEwlPZ,CwE/lPQ,MAOI,wDxE6lPZ,CwEpmPQ,MAOI,sDxEkmPZ,CwEzmPQ,MAOI,0DxEumPZ,CwE9mPQ,MAOI,sDxE4mPZ,CwEnnPQ,SAOI,sDxEinPZ,CwExnPQ,MAOI,sBxEqnPZ,CwE5nPQ,MAOI,2BxEynPZ,CwEhoPQ,MAOI,0BxE6nPZ,CwEpoPQ,MAOI,yBxEioPZ,CwExoPQ,MAOI,2BxEqoPZ,CwE5oPQ,MAOI,yBxEyoPZ,CwEhpPQ,SAOI,yBxE6oPZ,CwEppPQ,MAOI,wBxEipPZ,CwExpPQ,MAOI,6BxEqpPZ,CwE5pPQ,MAOI,4BxEypPZ,CwEhqPQ,MAOI,2BxE6pPZ,CwEpqPQ,MAOI,6BxEiqPZ,CwExqPQ,MAOI,2BxEqqPZ,CwE5qPQ,SAOI,2BxEyqPZ,CwEhrPQ,MAOI,yBxE6qPZ,CwEprPQ,MAOI,8BxEirPZ,CwExrPQ,MAOI,6BxEqrPZ,CwE5rPQ,MAOI,4BxEyrPZ,CwEhsPQ,MAOI,8BxE6rPZ,CwEpsPQ,MAOI,4BxEisPZ,CwExsPQ,SAOI,4BxEqsPZ,CwE5sPQ,MAOI,uBxEysPZ,CwEhtPQ,MAOI,4BxE6sPZ,CwEptPQ,MAOI,2BxEitPZ,CwExtPQ,MAOI,0BxEqtPZ,CwE5tPQ,MAOI,4BxEytPZ,CwEhuPQ,MAOI,0BxE6tPZ,CwEpuPQ,SAOI,0BxEiuPZ,CwExuPQ,KAOI,mBxEquPZ,CwE5uPQ,KAOI,wBxEyuPZ,CwEhvPQ,KAOI,uBxE6uPZ,CwEpvPQ,KAOI,sBxEivPZ,CwExvPQ,KAOI,wBxEqvPZ,CwE5vPQ,KAOI,sBxEyvPZ,CwEhwPQ,MAOI,kDxE8vPZ,CwErwPQ,MAOI,4DxEmwPZ,CwE1wPQ,MAOI,0DxEwwPZ,CwE/wPQ,MAOI,wDxE6wPZ,CwEpxPQ,MAOI,4DxEkxPZ,CwEzxPQ,MAOI,wDxEuxPZ,CwE9xPQ,MAOI,kDxE4xPZ,CwEnyPQ,MAOI,4DxEiyPZ,CwExyPQ,MAOI,0DxEsyPZ,CwE7yPQ,MAOI,wDxE2yPZ,CwElzPQ,MAOI,4DxEgzPZ,CwEvzPQ,MAOI,wDxEqzPZ,CwE5zPQ,MAOI,uBxEyzPZ,CwEh0PQ,MAOI,4BxE6zPZ,CwEp0PQ,MAOI,2BxEi0PZ,CwEx0PQ,MAOI,0BxEq0PZ,CwE50PQ,MAOI,4BxEy0PZ,CwEh1PQ,MAOI,0BxE60PZ,CwEp1PQ,MAOI,yBxEi1PZ,CwEx1PQ,MAOI,8BxEq1PZ,CwE51PQ,MAOI,6BxEy1PZ,CwEh2PQ,MAOI,4BxE61PZ,CwEp2PQ,MAOI,8BxEi2PZ,CwEx2PQ,MAOI,4BxEq2PZ,CwE52PQ,MAOI,0BxEy2PZ,CwEh3PQ,MAOI,+BxE62PZ,CwEp3PQ,MAOI,8BxEi3PZ,CwEx3PQ,MAOI,6BxEq3PZ,CwE53PQ,MAOI,+BxEy3PZ,CwEh4PQ,MAOI,6BxE63PZ,CwEp4PQ,MAOI,wBxEi4PZ,CwEx4PQ,MAOI,6BxEq4PZ,CwE54PQ,MAOI,4BxEy4PZ,CwEh5PQ,MAOI,2BxE64PZ,CwEp5PQ,MAOI,6BxEi5PZ,CwEx5PQ,MAOI,2BxEq5PZ,CwE55PQ,OAOI,exEy5PZ,CwEh6PQ,OAOI,oBxE65PZ,CwEp6PQ,OAOI,mBxEi6PZ,CwEx6PQ,OAOI,kBxEq6PZ,CwE56PQ,OAOI,oBxEy6PZ,CwEh7PQ,OAOI,kBxE66PZ,CwEp7PQ,WAOI,mBxEi7PZ,CwEx7PQ,WAOI,wBxEq7PZ,CwE57PQ,WAOI,uBxEy7PZ,CwEh8PQ,WAOI,sBxE67PZ,CwEp8PQ,WAOI,wBxEi8PZ,CwEx8PQ,WAOI,sBxEq8PZ,CwE58PQ,cAOI,kDxEy8PZ,CwEh9PQ,cAOI,4DxE68PZ,CwEp9PQ,cAOI,0DxEi9PZ,CwEx9PQ,cAOI,wDxEq9PZ,CwE59PQ,cAOI,4DxEy9PZ,CwEh+PQ,cAOI,wDxE69PZ,CwEp+PQ,gBAOI,8CxEi+PZ,CwEx+PQ,MAOI,yCxEq+PZ,CwE5+PQ,MAOI,0CxEy+PZ,CwEh/PQ,MAOI,2CxE6+PZ,CwEp/PQ,MAOI,yCxEi/PZ,CwEx/PQ,MAOI,4BxEq/PZ,CwE5/PQ,MAOI,yBxEy/PZ,CwEhgQQ,YAOI,2BxE6/PZ,CwEpgQQ,YAOI,2BxEigQZ,CwExgQQ,YAOI,6BxEqgQZ,CwE5gQQ,UAOI,yBxEygQZ,CwEhhQQ,WAOI,yBxE6gQZ,CwEphQQ,WAOI,yBxEihQZ,CwExhQQ,aAOI,yBxEqhQZ,CwE5hQQ,SAOI,yBxEyhQZ,CwEhiQQ,WAOI,4BxE6hQZ,CwEpiQQ,MAOI,uBxEiiQZ,CwExiQQ,OAOI,0BxEqiQZ,CwE5iQQ,SAOI,yBxEyiQZ,CwEhjQQ,OAOI,uBxE6iQZ,CwEpjQQ,YAOI,yBxEijQZ,CwExjQQ,UAOI,0BxEqjQZ,CwE5jQQ,aAOI,2BxEyjQZ,CwEhkQQ,sBAOI,8BxE6jQZ,CwEpkQQ,2BAOI,mCxEikQZ,CwExkQQ,8BAOI,sCxEqkQZ,CwE5kQQ,gBAOI,kCxEykQZ,CwEhlQQ,gBAOI,kCxE6kQZ,CwEplQQ,iBAOI,mCxEilQZ,CwExlQQ,WAOI,4BxEqlQZ,CwE5lQQ,aAOI,4BxEylQZ,CwEhmQQ,YAOI,8DxE+lQZ,CwEtmQQ,cAIQ,oBAGJ,kExEqmQZ,CwE5mQQ,gBAIQ,oBAGJ,oExE0mQZ,CwEjnQQ,cAIQ,oBAGJ,kExE+mQZ,CwEtnQQ,WAIQ,oBAGJ,+DxEonQZ,CwE3nQQ,cAIQ,oBAGJ,kExEynQZ,CwEhoQQ,aAIQ,oBAGJ,iExE8nQZ,CwEroQQ,YAIQ,oBAGJ,gExEmoQZ,CwE1oQQ,WAIQ,oBAGJ,+DxEwoQZ,CwE/oQQ,YAIQ,oBAGJ,gExE6oQZ,CwEppQQ,YAIQ,oBAGJ,gExEkpQZ,CwEzpQQ,WAIQ,oBAGJ,qExEupQZ,CwE9pQQ,YAIQ,oBAGJ,yCxE4pQZ,CwEnqQQ,eAIQ,oBAGJ,8BxEiqQZ,CwExqQQ,eAIQ,oBAGJ,kCxEsqQZ,CwE7qQQ,qBAIQ,oBAGJ,yCxE2qQZ,CwElrQQ,oBAIQ,oBAGJ,wCxEgrQZ,CwEvrQQ,oBAIQ,oBAGJ,wCxEqrQZ,CwE5rQQ,YAIQ,oBAGJ,uBxE0rQZ,CwE3sQQ,iBACE,sBxE8sQV,CwE/sQQ,iBACE,qBxEktQV,CwEntQQ,iBACE,sBxEstQV,CwEvtQQ,kBACE,mBxE0tQV,CwEjtQQ,uBAOI,+CxE8sQZ,CwErtQQ,yBAOI,iDxEktQZ,CwEztQQ,uBAOI,+CxEstQZ,CwE7tQQ,oBAOI,4CxE0tQZ,CwEjuQQ,uBAOI,+CxE8tQZ,CwEruQQ,sBAOI,8CxEkuQZ,CwEzuQQ,qBAOI,6CxEsuQZ,CwE7uQQ,oBAOI,4CxE0uQZ,CwEtvQU,8CACE,qBxE6vQZ,CwE9vQU,8CACE,sBxEqwQZ,CwEtwQU,8CACE,qBxE6wQZ,CwE9wQU,8CACE,sBxEqxQZ,CwEtxQU,gDACE,mBxE6xQZ,CwE7wQU,0CAOI,sCxE8wQd,CwErxQU,0CAOI,qCxEsxQd,CwE7xQU,0CAOI,sCxE8xQd,CwEjzQQ,wBAIQ,8BAGJ,4FxE+yQZ,CwEtzQQ,0BAIQ,8BAGJ,8FxEozQZ,CwE3zQQ,wBAIQ,8BAGJ,4FxEyzQZ,CwEh0QQ,qBAIQ,8BAGJ,yFxE8zQZ,CwEr0QQ,wBAIQ,8BAGJ,4FxEm0QZ,CwE10QQ,uBAIQ,8BAGJ,2FxEw0QZ,CwE/0QQ,sBAIQ,8BAGJ,0FxE60QZ,CwEp1QQ,qBAIQ,8BAGJ,yFxEk1QZ,CwEz1QQ,gBAIQ,8BAGJ,iGxEu1QZ,CwEn2QU,gEACE,6BxE02QZ,CwE32QU,kEACE,+BxEk3QZ,CwEn3QU,kEACE,gCxE03QZ,CwE33QU,kEACE,+BxEk4QZ,CwEn4QU,kEACE,gCxE04QZ,CwE34QU,oEACE,6BxEk5QZ,CwE94QQ,YAIQ,kBAGJ,2ExE44QZ,CwEn5QQ,cAIQ,kBAGJ,6ExEi5QZ,CwEx5QQ,YAIQ,kBAGJ,2ExEs5QZ,CwE75QQ,SAIQ,kBAGJ,wExE25QZ,CwEl6QQ,YAIQ,kBAGJ,2ExEg6QZ,CwEv6QQ,WAIQ,kBAGJ,0ExEq6QZ,CwE56QQ,UAIQ,kBAGJ,yExE06QZ,CwEj7QQ,SAIQ,kBAGJ,wExE+6QZ,CwEt7QQ,UAIQ,kBAGJ,yExEo7QZ,CwE37QQ,UAIQ,kBAGJ,yExEy7QZ,CwEh8QQ,SAIQ,kBAGJ,2ExE87QZ,CwEr8QQ,gBAIQ,kBAGJ,sCxEm8QZ,CwE18QQ,mBAIQ,kBAGJ,gFxEw8QZ,CwE/8QQ,kBAIQ,kBAGJ,+ExE68QZ,CwE99QQ,eACE,mBxEi+QV,CwEl+QQ,eACE,oBxEq+QV,CwEt+QQ,eACE,mBxEy+QV,CwE1+QQ,eACE,oBxE6+QV,CwE9+QQ,gBACE,iBxEi/QV,CwEx+QQ,mBAOI,sDxEq+QZ,CwE5+QQ,qBAOI,wDxEy+QZ,CwEh/QQ,mBAOI,sDxE6+QZ,CwEp/QQ,gBAOI,mDxEi/QZ,CwEx/QQ,mBAOI,sDxEq/QZ,CwE5/QQ,kBAOI,qDxEy/QZ,CwEhgRQ,iBAOI,oDxE6/QZ,CwEpgRQ,gBAOI,mDxEigRZ,CwExgRQ,aAOI,6CxEqgRZ,CwE5gRQ,iBAOI,0FxEygRZ,CwEhhRQ,kBAOI,6FxE6gRZ,CwEphRQ,kBAOI,6FxEihRZ,CwExhRQ,SAOI,6BxEqhRZ,CwE5hRQ,SAOI,6BxEyhRZ,CwEhiRQ,SAOI,+CxE6hRZ,CwEpiRQ,WAOI,yBxEiiRZ,CwExiRQ,WAOI,kDxEqiRZ,CwE5iRQ,WAOI,+CxEyiRZ,CwEhjRQ,WAOI,kDxE6iRZ,CwEpjRQ,WAOI,kDxEijRZ,CwExjRQ,WAOI,mDxEqjRZ,CwE5jRQ,gBAOI,2BxEyjRZ,CwEhkRQ,cAOI,oDxE6jRZ,CwEpkRQ,aAOI,kHxEkkRZ,CwEzkRQ,eAOI,sExEukRZ,CwE9kRQ,eAOI,wHxE4kRZ,CwEnlRQ,eAOI,kHxEilRZ,CwExlRQ,eAOI,wHxEslRZ,CwE7lRQ,eAOI,wHxE2lRZ,CwElmRQ,eAOI,0HxEgmRZ,CwEvmRQ,oBAOI,0ExEqmRZ,CwE5mRQ,kBAOI,4HxE0mRZ,CwEjnRQ,aAOI,sHxE+mRZ,CwEtnRQ,eAOI,0ExEonRZ,CwE3nRQ,eAOI,4HxEynRZ,CwEhoRQ,eAOI,sHxE8nRZ,CwEroRQ,eAOI,4HxEmoRZ,CwE1oRQ,eAOI,4HxEwoRZ,CwE/oRQ,eAOI,8HxE6oRZ,CwEppRQ,oBAOI,8ExEkpRZ,CwEzpRQ,kBAOI,gIxEupRZ,CwE9pRQ,gBAOI,wHxE4pRZ,CwEnqRQ,kBAOI,4ExEiqRZ,CwExqRQ,kBAOI,8HxEsqRZ,CwE7qRQ,kBAOI,wHxE2qRZ,CwElrRQ,kBAOI,8HxEgrRZ,CwEvrRQ,kBAOI,8HxEqrRZ,CwE5rRQ,kBAOI,gIxE0rRZ,CwEjsRQ,uBAOI,gFxE+rRZ,CwEtsRQ,qBAOI,kIxEosRZ,CwE3sRQ,eAOI,oHxEysRZ,CwEhtRQ,iBAOI,wExE8sRZ,CwErtRQ,iBAOI,0HxEmtRZ,CwE1tRQ,iBAOI,oHxEwtRZ,CwE/tRQ,iBAOI,0HxE6tRZ,CwEpuRQ,iBAOI,0HxEkuRZ,CwEzuRQ,iBAOI,4HxEuuRZ,CwE9uRQ,sBAOI,4ExE4uRZ,CwEnvRQ,oBAOI,8HxEivRZ,CwExvRQ,SAOI,4BxEqvRZ,CwE5vRQ,WAOI,2BxEyvRZ,CwEhwRQ,MAOI,oBxE6vRZ,CwEpwRQ,KAOI,mBxEiwRZ,CwExwRQ,KAOI,mBxEqwRZ,CwE5wRQ,KAOI,mBxEywRZ,CwEhxRQ,KAOI,mBxE6wRZ,CavxRI,yB2DGI,gBAOI,oBxEkxRV,CwEzxRM,cAOI,qBxEqxRV,CwE5xRM,eAOI,oBxEwxRV,CwE/xRM,uBAOI,4DxE2xRV,CwElyRM,qBAOI,wDxE8xRV,CwEryRM,oBAOI,sDxEiyRV,CwExyRM,qBAOI,kExEoyRV,CwE3yRM,oBAOI,sDxEuyRV,CwE9yRM,aAOI,wBxE0yRV,CwEjzRM,mBAOI,8BxE6yRV,CwEpzRM,YAOI,uBxEgzRV,CwEvzRM,WAOI,sBxEmzRV,CwE1zRM,kBAOI,6BxEszRV,CwE7zRM,YAOI,uBxEyzRV,CwEh0RM,gBAOI,2BxE4zRV,CwEn0RM,iBAOI,4BxE+zRV,CwEt0RM,WAOI,sBxEk0RV,CwEz0RM,kBAOI,6BxEq0RV,CwE50RM,WAOI,sBxEw0RV,CwE/0RM,cAOI,uBxE20RV,CwEl1RM,aAOI,4BxE80RV,CwEr1RM,gBAOI,+BxEi1RV,CwEx1RM,qBAOI,oCxEo1RV,CwE31RM,wBAOI,uCxEu1RV,CwE91RM,gBAOI,qBxE01RV,CwEj2RM,gBAOI,qBxE61RV,CwEp2RM,kBAOI,uBxEg2RV,CwEv2RM,kBAOI,uBxEm2RV,CwE12RM,cAOI,wBxEs2RV,CwE72RM,gBAOI,0BxEy2RV,CwEh3RM,sBAOI,gCxE42RV,CwEn3RM,0BAOI,oCxE+2RV,CwEt3RM,wBAOI,kCxEk3RV,CwEz3RM,2BAOI,gCxEq3RV,CwE53RM,4BAOI,uCxEw3RV,CwE/3RM,2BAOI,sCxE23RV,CwEl4RM,2BAOI,sCxE83RV,CwEr4RM,sBAOI,gCxEi4RV,CwEx4RM,oBAOI,8BxEo4RV,CwE34RM,uBAOI,4BxEu4RV,CwE94RM,yBAOI,8BxE04RV,CwEj5RM,wBAOI,6BxE64RV,CwEp5RM,wBAOI,kCxEg5RV,CwEv5RM,sBAOI,gCxEm5RV,CwE15RM,yBAOI,8BxEs5RV,CwE75RM,0BAOI,qCxEy5RV,CwEh6RM,yBAOI,oCxE45RV,CwEn6RM,0BAOI,+BxE+5RV,CwEt6RM,oBAOI,yBxEk6RV,CwEz6RM,qBAOI,+BxEq6RV,CwE56RM,mBAOI,6BxEw6RV,CwE/6RM,sBAOI,2BxE26RV,CwEl7RM,wBAOI,6BxE86RV,CwEr7RM,uBAOI,4BxEi7RV,CwEx7RM,gBAOI,kBxEo7RV,CwE37RM,YAOI,iBxEu7RV,CwE97RM,YAOI,iBxE07RV,CwEj8RM,YAOI,iBxE67RV,CwEp8RM,YAOI,iBxEg8RV,CwEv8RM,YAOI,iBxEm8RV,CwE18RM,YAOI,iBxEs8RV,CwE78RM,eAOI,iBxEy8RV,CwEh9RM,QAOI,kBxE48RV,CwEn9RM,QAOI,uBxE+8RV,CwEt9RM,QAOI,sBxEk9RV,CwEz9RM,QAOI,qBxEq9RV,CwE59RM,QAOI,uBxEw9RV,CwE/9RM,QAOI,qBxE29RV,CwEl+RM,WAOI,qBxE89RV,CwEr+RM,SAOI,gDxEk+RV,CwEz+RM,SAOI,0DxEs+RV,CwE7+RM,SAOI,wDxE0+RV,CwEj/RM,SAOI,sDxE8+RV,CwEr/RM,SAOI,0DxEk/RV,CwEz/RM,SAOI,sDxEs/RV,CwE7/RM,YAOI,sDxE0/RV,CwEjgSM,SAOI,gDxE8/RV,CwErgSM,SAOI,0DxEkgSV,CwEzgSM,SAOI,wDxEsgSV,CwE7gSM,SAOI,sDxE0gSV,CwEjhSM,SAOI,0DxE8gSV,CwErhSM,SAOI,sDxEkhSV,CwEzhSM,YAOI,sDxEshSV,CwE7hSM,SAOI,sBxEyhSV,CwEhiSM,SAOI,2BxE4hSV,CwEniSM,SAOI,0BxE+hSV,CwEtiSM,SAOI,yBxEkiSV,CwEziSM,SAOI,2BxEqiSV,CwE5iSM,SAOI,yBxEwiSV,CwE/iSM,YAOI,yBxE2iSV,CwEljSM,SAOI,wBxE8iSV,CwErjSM,SAOI,6BxEijSV,CwExjSM,SAOI,4BxEojSV,CwE3jSM,SAOI,2BxEujSV,CwE9jSM,SAOI,6BxE0jSV,CwEjkSM,SAOI,2BxE6jSV,CwEpkSM,YAOI,2BxEgkSV,CwEvkSM,SAOI,yBxEmkSV,CwE1kSM,SAOI,8BxEskSV,CwE7kSM,SAOI,6BxEykSV,CwEhlSM,SAOI,4BxE4kSV,CwEnlSM,SAOI,8BxE+kSV,CwEtlSM,SAOI,4BxEklSV,CwEzlSM,YAOI,4BxEqlSV,CwE5lSM,SAOI,uBxEwlSV,CwE/lSM,SAOI,4BxE2lSV,CwElmSM,SAOI,2BxE8lSV,CwErmSM,SAOI,0BxEimSV,CwExmSM,SAOI,4BxEomSV,CwE3mSM,SAOI,0BxEumSV,CwE9mSM,YAOI,0BxE0mSV,CwEjnSM,QAOI,mBxE6mSV,CwEpnSM,QAOI,wBxEgnSV,CwEvnSM,QAOI,uBxEmnSV,CwE1nSM,QAOI,sBxEsnSV,CwE7nSM,QAOI,wBxEynSV,CwEhoSM,QAOI,sBxE4nSV,CwEnoSM,SAOI,kDxEgoSV,CwEvoSM,SAOI,4DxEooSV,CwE3oSM,SAOI,0DxEwoSV,CwE/oSM,SAOI,wDxE4oSV,CwEnpSM,SAOI,4DxEgpSV,CwEvpSM,SAOI,wDxEopSV,CwE3pSM,SAOI,kDxEwpSV,CwE/pSM,SAOI,4DxE4pSV,CwEnqSM,SAOI,0DxEgqSV,CwEvqSM,SAOI,wDxEoqSV,CwE3qSM,SAOI,4DxEwqSV,CwE/qSM,SAOI,wDxE4qSV,CwEnrSM,SAOI,uBxE+qSV,CwEtrSM,SAOI,4BxEkrSV,CwEzrSM,SAOI,2BxEqrSV,CwE5rSM,SAOI,0BxEwrSV,CwE/rSM,SAOI,4BxE2rSV,CwElsSM,SAOI,0BxE8rSV,CwErsSM,SAOI,yBxEisSV,CwExsSM,SAOI,8BxEosSV,CwE3sSM,SAOI,6BxEusSV,CwE9sSM,SAOI,4BxE0sSV,CwEjtSM,SAOI,8BxE6sSV,CwEptSM,SAOI,4BxEgtSV,CwEvtSM,SAOI,0BxEmtSV,CwE1tSM,SAOI,+BxEstSV,CwE7tSM,SAOI,8BxEytSV,CwEhuSM,SAOI,6BxE4tSV,CwEnuSM,SAOI,+BxE+tSV,CwEtuSM,SAOI,6BxEkuSV,CwEzuSM,SAOI,wBxEquSV,CwE5uSM,SAOI,6BxEwuSV,CwE/uSM,SAOI,4BxE2uSV,CwElvSM,SAOI,2BxE8uSV,CwErvSM,SAOI,6BxEivSV,CwExvSM,SAOI,2BxEovSV,CwE3vSM,UAOI,exEuvSV,CwE9vSM,UAOI,oBxE0vSV,CwEjwSM,UAOI,mBxE6vSV,CwEpwSM,UAOI,kBxEgwSV,CwEvwSM,UAOI,oBxEmwSV,CwE1wSM,UAOI,kBxEswSV,CwE7wSM,cAOI,mBxEywSV,CwEhxSM,cAOI,wBxE4wSV,CwEnxSM,cAOI,uBxE+wSV,CwEtxSM,cAOI,sBxEkxSV,CwEzxSM,cAOI,wBxEqxSV,CwE5xSM,cAOI,sBxEwxSV,CwE/xSM,iBAOI,kDxE2xSV,CwElySM,iBAOI,4DxE8xSV,CwErySM,iBAOI,0DxEiySV,CwExySM,iBAOI,wDxEoySV,CwE3ySM,iBAOI,4DxEuySV,CwE9ySM,iBAOI,wDxE0ySV,CwEjzSM,eAOI,yBxE6ySV,CwEpzSM,aAOI,0BxEgzSV,CwEvzSM,gBAOI,2BxEmzSV,CACF,Ca9zSI,yB2DGI,gBAOI,oBxEwzSV,CwE/zSM,cAOI,qBxE2zSV,CwEl0SM,eAOI,oBxE8zSV,CwEr0SM,uBAOI,4DxEi0SV,CwEx0SM,qBAOI,wDxEo0SV,CwE30SM,oBAOI,sDxEu0SV,CwE90SM,qBAOI,kExE00SV,CwEj1SM,oBAOI,sDxE60SV,CwEp1SM,aAOI,wBxEg1SV,CwEv1SM,mBAOI,8BxEm1SV,CwE11SM,YAOI,uBxEs1SV,CwE71SM,WAOI,sBxEy1SV,CwEh2SM,kBAOI,6BxE41SV,CwEn2SM,YAOI,uBxE+1SV,CwEt2SM,gBAOI,2BxEk2SV,CwEz2SM,iBAOI,4BxEq2SV,CwE52SM,WAOI,sBxEw2SV,CwE/2SM,kBAOI,6BxE22SV,CwEl3SM,WAOI,sBxE82SV,CwEr3SM,cAOI,uBxEi3SV,CwEx3SM,aAOI,4BxEo3SV,CwE33SM,gBAOI,+BxEu3SV,CwE93SM,qBAOI,oCxE03SV,CwEj4SM,wBAOI,uCxE63SV,CwEp4SM,gBAOI,qBxEg4SV,CwEv4SM,gBAOI,qBxEm4SV,CwE14SM,kBAOI,uBxEs4SV,CwE74SM,kBAOI,uBxEy4SV,CwEh5SM,cAOI,wBxE44SV,CwEn5SM,gBAOI,0BxE+4SV,CwEt5SM,sBAOI,gCxEk5SV,CwEz5SM,0BAOI,oCxEq5SV,CwE55SM,wBAOI,kCxEw5SV,CwE/5SM,2BAOI,gCxE25SV,CwEl6SM,4BAOI,uCxE85SV,CwEr6SM,2BAOI,sCxEi6SV,CwEx6SM,2BAOI,sCxEo6SV,CwE36SM,sBAOI,gCxEu6SV,CwE96SM,oBAOI,8BxE06SV,CwEj7SM,uBAOI,4BxE66SV,CwEp7SM,yBAOI,8BxEg7SV,CwEv7SM,wBAOI,6BxEm7SV,CwE17SM,wBAOI,kCxEs7SV,CwE77SM,sBAOI,gCxEy7SV,CwEh8SM,yBAOI,8BxE47SV,CwEn8SM,0BAOI,qCxE+7SV,CwEt8SM,yBAOI,oCxEk8SV,CwEz8SM,0BAOI,+BxEq8SV,CwE58SM,oBAOI,yBxEw8SV,CwE/8SM,qBAOI,+BxE28SV,CwEl9SM,mBAOI,6BxE88SV,CwEr9SM,sBAOI,2BxEi9SV,CwEx9SM,wBAOI,6BxEo9SV,CwE39SM,uBAOI,4BxEu9SV,CwE99SM,gBAOI,kBxE09SV,CwEj+SM,YAOI,iBxE69SV,CwEp+SM,YAOI,iBxEg+SV,CwEv+SM,YAOI,iBxEm+SV,CwE1+SM,YAOI,iBxEs+SV,CwE7+SM,YAOI,iBxEy+SV,CwEh/SM,YAOI,iBxE4+SV,CwEn/SM,eAOI,iBxE++SV,CwEt/SM,QAOI,kBxEk/SV,CwEz/SM,QAOI,uBxEq/SV,CwE5/SM,QAOI,sBxEw/SV,CwE//SM,QAOI,qBxE2/SV,CwElgTM,QAOI,uBxE8/SV,CwErgTM,QAOI,qBxEigTV,CwExgTM,WAOI,qBxEogTV,CwE3gTM,SAOI,gDxEwgTV,CwE/gTM,SAOI,0DxE4gTV,CwEnhTM,SAOI,wDxEghTV,CwEvhTM,SAOI,sDxEohTV,CwE3hTM,SAOI,0DxEwhTV,CwE/hTM,SAOI,sDxE4hTV,CwEniTM,YAOI,sDxEgiTV,CwEviTM,SAOI,gDxEoiTV,CwE3iTM,SAOI,0DxEwiTV,CwE/iTM,SAOI,wDxE4iTV,CwEnjTM,SAOI,sDxEgjTV,CwEvjTM,SAOI,0DxEojTV,CwE3jTM,SAOI,sDxEwjTV,CwE/jTM,YAOI,sDxE4jTV,CwEnkTM,SAOI,sBxE+jTV,CwEtkTM,SAOI,2BxEkkTV,CwEzkTM,SAOI,0BxEqkTV,CwE5kTM,SAOI,yBxEwkTV,CwE/kTM,SAOI,2BxE2kTV,CwEllTM,SAOI,yBxE8kTV,CwErlTM,YAOI,yBxEilTV,CwExlTM,SAOI,wBxEolTV,CwE3lTM,SAOI,6BxEulTV,CwE9lTM,SAOI,4BxE0lTV,CwEjmTM,SAOI,2BxE6lTV,CwEpmTM,SAOI,6BxEgmTV,CwEvmTM,SAOI,2BxEmmTV,CwE1mTM,YAOI,2BxEsmTV,CwE7mTM,SAOI,yBxEymTV,CwEhnTM,SAOI,8BxE4mTV,CwEnnTM,SAOI,6BxE+mTV,CwEtnTM,SAOI,4BxEknTV,CwEznTM,SAOI,8BxEqnTV,CwE5nTM,SAOI,4BxEwnTV,CwE/nTM,YAOI,4BxE2nTV,CwEloTM,SAOI,uBxE8nTV,CwEroTM,SAOI,4BxEioTV,CwExoTM,SAOI,2BxEooTV,CwE3oTM,SAOI,0BxEuoTV,CwE9oTM,SAOI,4BxE0oTV,CwEjpTM,SAOI,0BxE6oTV,CwEppTM,YAOI,0BxEgpTV,CwEvpTM,QAOI,mBxEmpTV,CwE1pTM,QAOI,wBxEspTV,CwE7pTM,QAOI,uBxEypTV,CwEhqTM,QAOI,sBxE4pTV,CwEnqTM,QAOI,wBxE+pTV,CwEtqTM,QAOI,sBxEkqTV,CwEzqTM,SAOI,kDxEsqTV,CwE7qTM,SAOI,4DxE0qTV,CwEjrTM,SAOI,0DxE8qTV,CwErrTM,SAOI,wDxEkrTV,CwEzrTM,SAOI,4DxEsrTV,CwE7rTM,SAOI,wDxE0rTV,CwEjsTM,SAOI,kDxE8rTV,CwErsTM,SAOI,4DxEksTV,CwEzsTM,SAOI,0DxEssTV,CwE7sTM,SAOI,wDxE0sTV,CwEjtTM,SAOI,4DxE8sTV,CwErtTM,SAOI,wDxEktTV,CwEztTM,SAOI,uBxEqtTV,CwE5tTM,SAOI,4BxEwtTV,CwE/tTM,SAOI,2BxE2tTV,CwEluTM,SAOI,0BxE8tTV,CwEruTM,SAOI,4BxEiuTV,CwExuTM,SAOI,0BxEouTV,CwE3uTM,SAOI,yBxEuuTV,CwE9uTM,SAOI,8BxE0uTV,CwEjvTM,SAOI,6BxE6uTV,CwEpvTM,SAOI,4BxEgvTV,CwEvvTM,SAOI,8BxEmvTV,CwE1vTM,SAOI,4BxEsvTV,CwE7vTM,SAOI,0BxEyvTV,CwEhwTM,SAOI,+BxE4vTV,CwEnwTM,SAOI,8BxE+vTV,CwEtwTM,SAOI,6BxEkwTV,CwEzwTM,SAOI,+BxEqwTV,CwE5wTM,SAOI,6BxEwwTV,CwE/wTM,SAOI,wBxE2wTV,CwElxTM,SAOI,6BxE8wTV,CwErxTM,SAOI,4BxEixTV,CwExxTM,SAOI,2BxEoxTV,CwE3xTM,SAOI,6BxEuxTV,CwE9xTM,SAOI,2BxE0xTV,CwEjyTM,UAOI,exE6xTV,CwEpyTM,UAOI,oBxEgyTV,CwEvyTM,UAOI,mBxEmyTV,CwE1yTM,UAOI,kBxEsyTV,CwE7yTM,UAOI,oBxEyyTV,CwEhzTM,UAOI,kBxE4yTV,CwEnzTM,cAOI,mBxE+yTV,CwEtzTM,cAOI,wBxEkzTV,CwEzzTM,cAOI,uBxEqzTV,CwE5zTM,cAOI,sBxEwzTV,CwE/zTM,cAOI,wBxE2zTV,CwEl0TM,cAOI,sBxE8zTV,CwEr0TM,iBAOI,kDxEi0TV,CwEx0TM,iBAOI,4DxEo0TV,CwE30TM,iBAOI,0DxEu0TV,CwE90TM,iBAOI,wDxE00TV,CwEj1TM,iBAOI,4DxE60TV,CwEp1TM,iBAOI,wDxEg1TV,CwEv1TM,eAOI,yBxEm1TV,CwE11TM,aAOI,0BxEs1TV,CwE71TM,gBAOI,2BxEy1TV,CACF,Cap2TI,yB2DGI,gBAOI,oBxE81TV,CwEr2TM,cAOI,qBxEi2TV,CwEx2TM,eAOI,oBxEo2TV,CwE32TM,uBAOI,4DxEu2TV,CwE92TM,qBAOI,wDxE02TV,CwEj3TM,oBAOI,sDxE62TV,CwEp3TM,qBAOI,kExEg3TV,CwEv3TM,oBAOI,sDxEm3TV,CwE13TM,aAOI,wBxEs3TV,CwE73TM,mBAOI,8BxEy3TV,CwEh4TM,YAOI,uBxE43TV,CwEn4TM,WAOI,sBxE+3TV,CwEt4TM,kBAOI,6BxEk4TV,CwEz4TM,YAOI,uBxEq4TV,CwE54TM,gBAOI,2BxEw4TV,CwE/4TM,iBAOI,4BxE24TV,CwEl5TM,WAOI,sBxE84TV,CwEr5TM,kBAOI,6BxEi5TV,CwEx5TM,WAOI,sBxEo5TV,CwE35TM,cAOI,uBxEu5TV,CwE95TM,aAOI,4BxE05TV,CwEj6TM,gBAOI,+BxE65TV,CwEp6TM,qBAOI,oCxEg6TV,CwEv6TM,wBAOI,uCxEm6TV,CwE16TM,gBAOI,qBxEs6TV,CwE76TM,gBAOI,qBxEy6TV,CwEh7TM,kBAOI,uBxE46TV,CwEn7TM,kBAOI,uBxE+6TV,CwEt7TM,cAOI,wBxEk7TV,CwEz7TM,gBAOI,0BxEq7TV,CwE57TM,sBAOI,gCxEw7TV,CwE/7TM,0BAOI,oCxE27TV,CwEl8TM,wBAOI,kCxE87TV,CwEr8TM,2BAOI,gCxEi8TV,CwEx8TM,4BAOI,uCxEo8TV,CwE38TM,2BAOI,sCxEu8TV,CwE98TM,2BAOI,sCxE08TV,CwEj9TM,sBAOI,gCxE68TV,CwEp9TM,oBAOI,8BxEg9TV,CwEv9TM,uBAOI,4BxEm9TV,CwE19TM,yBAOI,8BxEs9TV,CwE79TM,wBAOI,6BxEy9TV,CwEh+TM,wBAOI,kCxE49TV,CwEn+TM,sBAOI,gCxE+9TV,CwEt+TM,yBAOI,8BxEk+TV,CwEz+TM,0BAOI,qCxEq+TV,CwE5+TM,yBAOI,oCxEw+TV,CwE/+TM,0BAOI,+BxE2+TV,CwEl/TM,oBAOI,yBxE8+TV,CwEr/TM,qBAOI,+BxEi/TV,CwEx/TM,mBAOI,6BxEo/TV,CwE3/TM,sBAOI,2BxEu/TV,CwE9/TM,wBAOI,6BxE0/TV,CwEjgUM,uBAOI,4BxE6/TV,CwEpgUM,gBAOI,kBxEggUV,CwEvgUM,YAOI,iBxEmgUV,CwE1gUM,YAOI,iBxEsgUV,CwE7gUM,YAOI,iBxEygUV,CwEhhUM,YAOI,iBxE4gUV,CwEnhUM,YAOI,iBxE+gUV,CwEthUM,YAOI,iBxEkhUV,CwEzhUM,eAOI,iBxEqhUV,CwE5hUM,QAOI,kBxEwhUV,CwE/hUM,QAOI,uBxE2hUV,CwEliUM,QAOI,sBxE8hUV,CwEriUM,QAOI,qBxEiiUV,CwExiUM,QAOI,uBxEoiUV,CwE3iUM,QAOI,qBxEuiUV,CwE9iUM,WAOI,qBxE0iUV,CwEjjUM,SAOI,gDxE8iUV,CwErjUM,SAOI,0DxEkjUV,CwEzjUM,SAOI,wDxEsjUV,CwE7jUM,SAOI,sDxE0jUV,CwEjkUM,SAOI,0DxE8jUV,CwErkUM,SAOI,sDxEkkUV,CwEzkUM,YAOI,sDxEskUV,CwE7kUM,SAOI,gDxE0kUV,CwEjlUM,SAOI,0DxE8kUV,CwErlUM,SAOI,wDxEklUV,CwEzlUM,SAOI,sDxEslUV,CwE7lUM,SAOI,0DxE0lUV,CwEjmUM,SAOI,sDxE8lUV,CwErmUM,YAOI,sDxEkmUV,CwEzmUM,SAOI,sBxEqmUV,CwE5mUM,SAOI,2BxEwmUV,CwE/mUM,SAOI,0BxE2mUV,CwElnUM,SAOI,yBxE8mUV,CwErnUM,SAOI,2BxEinUV,CwExnUM,SAOI,yBxEonUV,CwE3nUM,YAOI,yBxEunUV,CwE9nUM,SAOI,wBxE0nUV,CwEjoUM,SAOI,6BxE6nUV,CwEpoUM,SAOI,4BxEgoUV,CwEvoUM,SAOI,2BxEmoUV,CwE1oUM,SAOI,6BxEsoUV,CwE7oUM,SAOI,2BxEyoUV,CwEhpUM,YAOI,2BxE4oUV,CwEnpUM,SAOI,yBxE+oUV,CwEtpUM,SAOI,8BxEkpUV,CwEzpUM,SAOI,6BxEqpUV,CwE5pUM,SAOI,4BxEwpUV,CwE/pUM,SAOI,8BxE2pUV,CwElqUM,SAOI,4BxE8pUV,CwErqUM,YAOI,4BxEiqUV,CwExqUM,SAOI,uBxEoqUV,CwE3qUM,SAOI,4BxEuqUV,CwE9qUM,SAOI,2BxE0qUV,CwEjrUM,SAOI,0BxE6qUV,CwEprUM,SAOI,4BxEgrUV,CwEvrUM,SAOI,0BxEmrUV,CwE1rUM,YAOI,0BxEsrUV,CwE7rUM,QAOI,mBxEyrUV,CwEhsUM,QAOI,wBxE4rUV,CwEnsUM,QAOI,uBxE+rUV,CwEtsUM,QAOI,sBxEksUV,CwEzsUM,QAOI,wBxEqsUV,CwE5sUM,QAOI,sBxEwsUV,CwE/sUM,SAOI,kDxE4sUV,CwEntUM,SAOI,4DxEgtUV,CwEvtUM,SAOI,0DxEotUV,CwE3tUM,SAOI,wDxEwtUV,CwE/tUM,SAOI,4DxE4tUV,CwEnuUM,SAOI,wDxEguUV,CwEvuUM,SAOI,kDxEouUV,CwE3uUM,SAOI,4DxEwuUV,CwE/uUM,SAOI,0DxE4uUV,CwEnvUM,SAOI,wDxEgvUV,CwEvvUM,SAOI,4DxEovUV,CwE3vUM,SAOI,wDxEwvUV,CwE/vUM,SAOI,uBxE2vUV,CwElwUM,SAOI,4BxE8vUV,CwErwUM,SAOI,2BxEiwUV,CwExwUM,SAOI,0BxEowUV,CwE3wUM,SAOI,4BxEuwUV,CwE9wUM,SAOI,0BxE0wUV,CwEjxUM,SAOI,yBxE6wUV,CwEpxUM,SAOI,8BxEgxUV,CwEvxUM,SAOI,6BxEmxUV,CwE1xUM,SAOI,4BxEsxUV,CwE7xUM,SAOI,8BxEyxUV,CwEhyUM,SAOI,4BxE4xUV,CwEnyUM,SAOI,0BxE+xUV,CwEtyUM,SAOI,+BxEkyUV,CwEzyUM,SAOI,8BxEqyUV,CwE5yUM,SAOI,6BxEwyUV,CwE/yUM,SAOI,+BxE2yUV,CwElzUM,SAOI,6BxE8yUV,CwErzUM,SAOI,wBxEizUV,CwExzUM,SAOI,6BxEozUV,CwE3zUM,SAOI,4BxEuzUV,CwE9zUM,SAOI,2BxE0zUV,CwEj0UM,SAOI,6BxE6zUV,CwEp0UM,SAOI,2BxEg0UV,CwEv0UM,UAOI,exEm0UV,CwE10UM,UAOI,oBxEs0UV,CwE70UM,UAOI,mBxEy0UV,CwEh1UM,UAOI,kBxE40UV,CwEn1UM,UAOI,oBxE+0UV,CwEt1UM,UAOI,kBxEk1UV,CwEz1UM,cAOI,mBxEq1UV,CwE51UM,cAOI,wBxEw1UV,CwE/1UM,cAOI,uBxE21UV,CwEl2UM,cAOI,sBxE81UV,CwEr2UM,cAOI,wBxEi2UV,CwEx2UM,cAOI,sBxEo2UV,CwE32UM,iBAOI,kDxEu2UV,CwE92UM,iBAOI,4DxE02UV,CwEj3UM,iBAOI,0DxE62UV,CwEp3UM,iBAOI,wDxEg3UV,CwEv3UM,iBAOI,4DxEm3UV,CwE13UM,iBAOI,wDxEs3UV,CwE73UM,eAOI,yBxEy3UV,CwEh4UM,aAOI,0BxE43UV,CwEn4UM,gBAOI,2BxE+3UV,CACF,Ca14UI,0B2DGI,gBAOI,oBxEo4UV,CwE34UM,cAOI,qBxEu4UV,CwE94UM,eAOI,oBxE04UV,CwEj5UM,uBAOI,4DxE64UV,CwEp5UM,qBAOI,wDxEg5UV,CwEv5UM,oBAOI,sDxEm5UV,CwE15UM,qBAOI,kExEs5UV,CwE75UM,oBAOI,sDxEy5UV,CwEh6UM,aAOI,wBxE45UV,CwEn6UM,mBAOI,8BxE+5UV,CwEt6UM,YAOI,uBxEk6UV,CwEz6UM,WAOI,sBxEq6UV,CwE56UM,kBAOI,6BxEw6UV,CwE/6UM,YAOI,uBxE26UV,CwEl7UM,gBAOI,2BxE86UV,CwEr7UM,iBAOI,4BxEi7UV,CwEx7UM,WAOI,sBxEo7UV,CwE37UM,kBAOI,6BxEu7UV,CwE97UM,WAOI,sBxE07UV,CwEj8UM,cAOI,uBxE67UV,CwEp8UM,aAOI,4BxEg8UV,CwEv8UM,gBAOI,+BxEm8UV,CwE18UM,qBAOI,oCxEs8UV,CwE78UM,wBAOI,uCxEy8UV,CwEh9UM,gBAOI,qBxE48UV,CwEn9UM,gBAOI,qBxE+8UV,CwEt9UM,kBAOI,uBxEk9UV,CwEz9UM,kBAOI,uBxEq9UV,CwE59UM,cAOI,wBxEw9UV,CwE/9UM,gBAOI,0BxE29UV,CwEl+UM,sBAOI,gCxE89UV,CwEr+UM,0BAOI,oCxEi+UV,CwEx+UM,wBAOI,kCxEo+UV,CwE3+UM,2BAOI,gCxEu+UV,CwE9+UM,4BAOI,uCxE0+UV,CwEj/UM,2BAOI,sCxE6+UV,CwEp/UM,2BAOI,sCxEg/UV,CwEv/UM,sBAOI,gCxEm/UV,CwE1/UM,oBAOI,8BxEs/UV,CwE7/UM,uBAOI,4BxEy/UV,CwEhgVM,yBAOI,8BxE4/UV,CwEngVM,wBAOI,6BxE+/UV,CwEtgVM,wBAOI,kCxEkgVV,CwEzgVM,sBAOI,gCxEqgVV,CwE5gVM,yBAOI,8BxEwgVV,CwE/gVM,0BAOI,qCxE2gVV,CwElhVM,yBAOI,oCxE8gVV,CwErhVM,0BAOI,+BxEihVV,CwExhVM,oBAOI,yBxEohVV,CwE3hVM,qBAOI,+BxEuhVV,CwE9hVM,mBAOI,6BxE0hVV,CwEjiVM,sBAOI,2BxE6hVV,CwEpiVM,wBAOI,6BxEgiVV,CwEviVM,uBAOI,4BxEmiVV,CwE1iVM,gBAOI,kBxEsiVV,CwE7iVM,YAOI,iBxEyiVV,CwEhjVM,YAOI,iBxE4iVV,CwEnjVM,YAOI,iBxE+iVV,CwEtjVM,YAOI,iBxEkjVV,CwEzjVM,YAOI,iBxEqjVV,CwE5jVM,YAOI,iBxEwjVV,CwE/jVM,eAOI,iBxE2jVV,CwElkVM,QAOI,kBxE8jVV,CwErkVM,QAOI,uBxEikVV,CwExkVM,QAOI,sBxEokVV,CwE3kVM,QAOI,qBxEukVV,CwE9kVM,QAOI,uBxE0kVV,CwEjlVM,QAOI,qBxE6kVV,CwEplVM,WAOI,qBxEglVV,CwEvlVM,SAOI,gDxEolVV,CwE3lVM,SAOI,0DxEwlVV,CwE/lVM,SAOI,wDxE4lVV,CwEnmVM,SAOI,sDxEgmVV,CwEvmVM,SAOI,0DxEomVV,CwE3mVM,SAOI,sDxEwmVV,CwE/mVM,YAOI,sDxE4mVV,CwEnnVM,SAOI,gDxEgnVV,CwEvnVM,SAOI,0DxEonVV,CwE3nVM,SAOI,wDxEwnVV,CwE/nVM,SAOI,sDxE4nVV,CwEnoVM,SAOI,0DxEgoVV,CwEvoVM,SAOI,sDxEooVV,CwE3oVM,YAOI,sDxEwoVV,CwE/oVM,SAOI,sBxE2oVV,CwElpVM,SAOI,2BxE8oVV,CwErpVM,SAOI,0BxEipVV,CwExpVM,SAOI,yBxEopVV,CwE3pVM,SAOI,2BxEupVV,CwE9pVM,SAOI,yBxE0pVV,CwEjqVM,YAOI,yBxE6pVV,CwEpqVM,SAOI,wBxEgqVV,CwEvqVM,SAOI,6BxEmqVV,CwE1qVM,SAOI,4BxEsqVV,CwE7qVM,SAOI,2BxEyqVV,CwEhrVM,SAOI,6BxE4qVV,CwEnrVM,SAOI,2BxE+qVV,CwEtrVM,YAOI,2BxEkrVV,CwEzrVM,SAOI,yBxEqrVV,CwE5rVM,SAOI,8BxEwrVV,CwE/rVM,SAOI,6BxE2rVV,CwElsVM,SAOI,4BxE8rVV,CwErsVM,SAOI,8BxEisVV,CwExsVM,SAOI,4BxEosVV,CwE3sVM,YAOI,4BxEusVV,CwE9sVM,SAOI,uBxE0sVV,CwEjtVM,SAOI,4BxE6sVV,CwEptVM,SAOI,2BxEgtVV,CwEvtVM,SAOI,0BxEmtVV,CwE1tVM,SAOI,4BxEstVV,CwE7tVM,SAOI,0BxEytVV,CwEhuVM,YAOI,0BxE4tVV,CwEnuVM,QAOI,mBxE+tVV,CwEtuVM,QAOI,wBxEkuVV,CwEzuVM,QAOI,uBxEquVV,CwE5uVM,QAOI,sBxEwuVV,CwE/uVM,QAOI,wBxE2uVV,CwElvVM,QAOI,sBxE8uVV,CwErvVM,SAOI,kDxEkvVV,CwEzvVM,SAOI,4DxEsvVV,CwE7vVM,SAOI,0DxE0vVV,CwEjwVM,SAOI,wDxE8vVV,CwErwVM,SAOI,4DxEkwVV,CwEzwVM,SAOI,wDxEswVV,CwE7wVM,SAOI,kDxE0wVV,CwEjxVM,SAOI,4DxE8wVV,CwErxVM,SAOI,0DxEkxVV,CwEzxVM,SAOI,wDxEsxVV,CwE7xVM,SAOI,4DxE0xVV,CwEjyVM,SAOI,wDxE8xVV,CwEryVM,SAOI,uBxEiyVV,CwExyVM,SAOI,4BxEoyVV,CwE3yVM,SAOI,2BxEuyVV,CwE9yVM,SAOI,0BxE0yVV,CwEjzVM,SAOI,4BxE6yVV,CwEpzVM,SAOI,0BxEgzVV,CwEvzVM,SAOI,yBxEmzVV,CwE1zVM,SAOI,8BxEszVV,CwE7zVM,SAOI,6BxEyzVV,CwEh0VM,SAOI,4BxE4zVV,CwEn0VM,SAOI,8BxE+zVV,CwEt0VM,SAOI,4BxEk0VV,CwEz0VM,SAOI,0BxEq0VV,CwE50VM,SAOI,+BxEw0VV,CwE/0VM,SAOI,8BxE20VV,CwEl1VM,SAOI,6BxE80VV,CwEr1VM,SAOI,+BxEi1VV,CwEx1VM,SAOI,6BxEo1VV,CwE31VM,SAOI,wBxEu1VV,CwE91VM,SAOI,6BxE01VV,CwEj2VM,SAOI,4BxE61VV,CwEp2VM,SAOI,2BxEg2VV,CwEv2VM,SAOI,6BxEm2VV,CwE12VM,SAOI,2BxEs2VV,CwE72VM,UAOI,exEy2VV,CwEh3VM,UAOI,oBxE42VV,CwEn3VM,UAOI,mBxE+2VV,CwEt3VM,UAOI,kBxEk3VV,CwEz3VM,UAOI,oBxEq3VV,CwE53VM,UAOI,kBxEw3VV,CwE/3VM,cAOI,mBxE23VV,CwEl4VM,cAOI,wBxE83VV,CwEr4VM,cAOI,uBxEi4VV,CwEx4VM,cAOI,sBxEo4VV,CwE34VM,cAOI,wBxEu4VV,CwE94VM,cAOI,sBxE04VV,CwEj5VM,iBAOI,kDxE64VV,CwEp5VM,iBAOI,4DxEg5VV,CwEv5VM,iBAOI,0DxEm5VV,CwE15VM,iBAOI,wDxEs5VV,CwE75VM,iBAOI,4DxEy5VV,CwEh6VM,iBAOI,wDxE45VV,CwEn6VM,eAOI,yBxE+5VV,CwEt6VM,aAOI,0BxEk6VV,CwEz6VM,gBAOI,2BxEq6VV,CACF,Cah7VI,0B2DGI,iBAOI,oBxE06VV,CwEj7VM,eAOI,qBxE66VV,CwEp7VM,gBAOI,oBxEg7VV,CwEv7VM,wBAOI,4DxEm7VV,CwE17VM,sBAOI,wDxEs7VV,CwE77VM,qBAOI,sDxEy7VV,CwEh8VM,sBAOI,kExE47VV,CwEn8VM,qBAOI,sDxE+7VV,CwEt8VM,cAOI,wBxEk8VV,CwEz8VM,oBAOI,8BxEq8VV,CwE58VM,aAOI,uBxEw8VV,CwE/8VM,YAOI,sBxE28VV,CwEl9VM,mBAOI,6BxE88VV,CwEr9VM,aAOI,uBxEi9VV,CwEx9VM,iBAOI,2BxEo9VV,CwE39VM,kBAOI,4BxEu9VV,CwE99VM,YAOI,sBxE09VV,CwEj+VM,mBAOI,6BxE69VV,CwEp+VM,YAOI,sBxEg+VV,CwEv+VM,eAOI,uBxEm+VV,CwE1+VM,cAOI,4BxEs+VV,CwE7+VM,iBAOI,+BxEy+VV,CwEh/VM,sBAOI,oCxE4+VV,CwEn/VM,yBAOI,uCxE++VV,CwEt/VM,iBAOI,qBxEk/VV,CwEz/VM,iBAOI,qBxEq/VV,CwE5/VM,mBAOI,uBxEw/VV,CwE//VM,mBAOI,uBxE2/VV,CwElgWM,eAOI,wBxE8/VV,CwErgWM,iBAOI,0BxEigWV,CwExgWM,uBAOI,gCxEogWV,CwE3gWM,2BAOI,oCxEugWV,CwE9gWM,yBAOI,kCxE0gWV,CwEjhWM,4BAOI,gCxE6gWV,CwEphWM,6BAOI,uCxEghWV,CwEvhWM,4BAOI,sCxEmhWV,CwE1hWM,4BAOI,sCxEshWV,CwE7hWM,uBAOI,gCxEyhWV,CwEhiWM,qBAOI,8BxE4hWV,CwEniWM,wBAOI,4BxE+hWV,CwEtiWM,0BAOI,8BxEkiWV,CwEziWM,yBAOI,6BxEqiWV,CwE5iWM,yBAOI,kCxEwiWV,CwE/iWM,uBAOI,gCxE2iWV,CwEljWM,0BAOI,8BxE8iWV,CwErjWM,2BAOI,qCxEijWV,CwExjWM,0BAOI,oCxEojWV,CwE3jWM,2BAOI,+BxEujWV,CwE9jWM,qBAOI,yBxE0jWV,CwEjkWM,sBAOI,+BxE6jWV,CwEpkWM,oBAOI,6BxEgkWV,CwEvkWM,uBAOI,2BxEmkWV,CwE1kWM,yBAOI,6BxEskWV,CwE7kWM,wBAOI,4BxEykWV,CwEhlWM,iBAOI,kBxE4kWV,CwEnlWM,aAOI,iBxE+kWV,CwEtlWM,aAOI,iBxEklWV,CwEzlWM,aAOI,iBxEqlWV,CwE5lWM,aAOI,iBxEwlWV,CwE/lWM,aAOI,iBxE2lWV,CwElmWM,aAOI,iBxE8lWV,CwErmWM,gBAOI,iBxEimWV,CwExmWM,SAOI,kBxEomWV,CwE3mWM,SAOI,uBxEumWV,CwE9mWM,SAOI,sBxE0mWV,CwEjnWM,SAOI,qBxE6mWV,CwEpnWM,SAOI,uBxEgnWV,CwEvnWM,SAOI,qBxEmnWV,CwE1nWM,YAOI,qBxEsnWV,CwE7nWM,UAOI,gDxE0nWV,CwEjoWM,UAOI,0DxE8nWV,CwEroWM,UAOI,wDxEkoWV,CwEzoWM,UAOI,sDxEsoWV,CwE7oWM,UAOI,0DxE0oWV,CwEjpWM,UAOI,sDxE8oWV,CwErpWM,aAOI,sDxEkpWV,CwEzpWM,UAOI,gDxEspWV,CwE7pWM,UAOI,0DxE0pWV,CwEjqWM,UAOI,wDxE8pWV,CwErqWM,UAOI,sDxEkqWV,CwEzqWM,UAOI,0DxEsqWV,CwE7qWM,UAOI,sDxE0qWV,CwEjrWM,aAOI,sDxE8qWV,CwErrWM,UAOI,sBxEirWV,CwExrWM,UAOI,2BxEorWV,CwE3rWM,UAOI,0BxEurWV,CwE9rWM,UAOI,yBxE0rWV,CwEjsWM,UAOI,2BxE6rWV,CwEpsWM,UAOI,yBxEgsWV,CwEvsWM,aAOI,yBxEmsWV,CwE1sWM,UAOI,wBxEssWV,CwE7sWM,UAOI,6BxEysWV,CwEhtWM,UAOI,4BxE4sWV,CwEntWM,UAOI,2BxE+sWV,CwEttWM,UAOI,6BxEktWV,CwEztWM,UAOI,2BxEqtWV,CwE5tWM,aAOI,2BxEwtWV,CwE/tWM,UAOI,yBxE2tWV,CwEluWM,UAOI,8BxE8tWV,CwEruWM,UAOI,6BxEiuWV,CwExuWM,UAOI,4BxEouWV,CwE3uWM,UAOI,8BxEuuWV,CwE9uWM,UAOI,4BxE0uWV,CwEjvWM,aAOI,4BxE6uWV,CwEpvWM,UAOI,uBxEgvWV,CwEvvWM,UAOI,4BxEmvWV,CwE1vWM,UAOI,2BxEsvWV,CwE7vWM,UAOI,0BxEyvWV,CwEhwWM,UAOI,4BxE4vWV,CwEnwWM,UAOI,0BxE+vWV,CwEtwWM,aAOI,0BxEkwWV,CwEzwWM,SAOI,mBxEqwWV,CwE5wWM,SAOI,wBxEwwWV,CwE/wWM,SAOI,uBxE2wWV,CwElxWM,SAOI,sBxE8wWV,CwErxWM,SAOI,wBxEixWV,CwExxWM,SAOI,sBxEoxWV,CwE3xWM,UAOI,kDxEwxWV,CwE/xWM,UAOI,4DxE4xWV,CwEnyWM,UAOI,0DxEgyWV,CwEvyWM,UAOI,wDxEoyWV,CwE3yWM,UAOI,4DxEwyWV,CwE/yWM,UAOI,wDxE4yWV,CwEnzWM,UAOI,kDxEgzWV,CwEvzWM,UAOI,4DxEozWV,CwE3zWM,UAOI,0DxEwzWV,CwE/zWM,UAOI,wDxE4zWV,CwEn0WM,UAOI,4DxEg0WV,CwEv0WM,UAOI,wDxEo0WV,CwE30WM,UAOI,uBxEu0WV,CwE90WM,UAOI,4BxE00WV,CwEj1WM,UAOI,2BxE60WV,CwEp1WM,UAOI,0BxEg1WV,CwEv1WM,UAOI,4BxEm1WV,CwE11WM,UAOI,0BxEs1WV,CwE71WM,UAOI,yBxEy1WV,CwEh2WM,UAOI,8BxE41WV,CwEn2WM,UAOI,6BxE+1WV,CwEt2WM,UAOI,4BxEk2WV,CwEz2WM,UAOI,8BxEq2WV,CwE52WM,UAOI,4BxEw2WV,CwE/2WM,UAOI,0BxE22WV,CwEl3WM,UAOI,+BxE82WV,CwEr3WM,UAOI,8BxEi3WV,CwEx3WM,UAOI,6BxEo3WV,CwE33WM,UAOI,+BxEu3WV,CwE93WM,UAOI,6BxE03WV,CwEj4WM,UAOI,wBxE63WV,CwEp4WM,UAOI,6BxEg4WV,CwEv4WM,UAOI,4BxEm4WV,CwE14WM,UAOI,2BxEs4WV,CwE74WM,UAOI,6BxEy4WV,CwEh5WM,UAOI,2BxE44WV,CwEn5WM,WAOI,exE+4WV,CwEt5WM,WAOI,oBxEk5WV,CwEz5WM,WAOI,mBxEq5WV,CwE55WM,WAOI,kBxEw5WV,CwE/5WM,WAOI,oBxE25WV,CwEl6WM,WAOI,kBxE85WV,CwEr6WM,eAOI,mBxEi6WV,CwEx6WM,eAOI,wBxEo6WV,CwE36WM,eAOI,uBxEu6WV,CwE96WM,eAOI,sBxE06WV,CwEj7WM,eAOI,wBxE66WV,CwEp7WM,eAOI,sBxEg7WV,CwEv7WM,kBAOI,kDxEm7WV,CwE17WM,kBAOI,4DxEs7WV,CwE77WM,kBAOI,0DxEy7WV,CwEh8WM,kBAOI,wDxE47WV,CwEn8WM,kBAOI,4DxE+7WV,CwEt8WM,kBAOI,wDxEk8WV,CwEz8WM,gBAOI,yBxEq8WV,CwE58WM,cAOI,0BxEw8WV,CwE/8WM,iBAOI,2BxE28WV,CACF,CyElgXA,0BD+CQ,MAOI,2BxEg9WV,CwEv9WM,MAOI,0BxEm9WV,CwE19WM,MAOI,4BxEs9WV,CwE79WM,MAOI,2BxEy9WV,CACF,CyE7/WA,aD4BQ,gBAOI,wBxE89WV,CwEr+WM,sBAOI,8BxEi+WV,CwEx+WM,eAOI,uBxEo+WV,CwE3+WM,cAOI,sBxEu+WV,CwE9+WM,qBAOI,6BxE0+WV,CwEj/WM,eAOI,uBxE6+WV,CwEp/WM,mBAOI,2BxEg/WV,CwEv/WM,oBAOI,4BxEm/WV,CwE1/WM,cAOI,sBxEs/WV,CwE7/WM,qBAOI,6BxEy/WV,CwEhgXM,cAOI,sBxE4/WV,CACF,C", "sources": ["webpack:///./node_modules/bootstrap/scss/_type.scss", "webpack:///./resources/sass/app.scss", "webpack:///./node_modules/bootstrap/scss/mixins/_banner.scss", "webpack:///./node_modules/bootstrap/scss/_root.scss", "webpack:///./node_modules/bootstrap/scss/vendor/_rfs.scss", "webpack:///./node_modules/bootstrap/scss/mixins/_color-mode.scss", "webpack:///./node_modules/bootstrap/scss/_reboot.scss", "webpack:///./node_modules/bootstrap/scss/_variables.scss", "webpack:///./node_modules/bootstrap/scss/mixins/_border-radius.scss", "webpack:///./node_modules/bootstrap/scss/mixins/_lists.scss", "webpack:///./node_modules/bootstrap/scss/_images.scss", "webpack:///./node_modules/bootstrap/scss/mixins/_image.scss", "webpack:///./node_modules/bootstrap/scss/_containers.scss", "webpack:///./node_modules/bootstrap/scss/mixins/_container.scss", "webpack:///./node_modules/bootstrap/scss/mixins/_breakpoints.scss", "webpack:///./node_modules/bootstrap/scss/_grid.scss", "webpack:///./node_modules/bootstrap/scss/mixins/_grid.scss", "webpack:///./node_modules/bootstrap/scss/_tables.scss", "webpack:///./node_modules/bootstrap/scss/mixins/_table-variants.scss", "webpack:///./node_modules/bootstrap/scss/forms/_labels.scss", "webpack:///./resources/sass/_variables.scss", "webpack:///./node_modules/bootstrap/scss/forms/_form-text.scss", "webpack:///./node_modules/bootstrap/scss/forms/_form-control.scss", "webpack:///./node_modules/bootstrap/scss/mixins/_transition.scss", "webpack:///./node_modules/bootstrap/scss/mixins/_gradients.scss", "webpack:///./node_modules/bootstrap/scss/forms/_form-select.scss", "webpack:///./node_modules/bootstrap/scss/forms/_form-check.scss", "webpack:///./node_modules/bootstrap/scss/forms/_form-range.scss", "webpack:///./node_modules/bootstrap/scss/forms/_floating-labels.scss", "webpack:///./node_modules/bootstrap/scss/forms/_input-group.scss", "webpack:///./node_modules/bootstrap/scss/mixins/_forms.scss", "webpack:///./node_modules/bootstrap/scss/_buttons.scss", "webpack:///./node_modules/bootstrap/scss/mixins/_buttons.scss", "webpack:///./node_modules/bootstrap/scss/_transitions.scss", "webpack:///./node_modules/bootstrap/scss/_dropdown.scss", "webpack:///./node_modules/bootstrap/scss/mixins/_caret.scss", "webpack:///./node_modules/bootstrap/scss/_button-group.scss", "webpack:///./node_modules/bootstrap/scss/_nav.scss", "webpack:///./node_modules/bootstrap/scss/_navbar.scss", "webpack:///./node_modules/bootstrap/scss/_card.scss", "webpack:///./node_modules/bootstrap/scss/_accordion.scss", "webpack:///./node_modules/bootstrap/scss/_breadcrumb.scss", "webpack:///./node_modules/bootstrap/scss/_pagination.scss", "webpack:///./node_modules/bootstrap/scss/mixins/_pagination.scss", "webpack:///./node_modules/bootstrap/scss/_badge.scss", "webpack:///./node_modules/bootstrap/scss/_alert.scss", "webpack:///./node_modules/bootstrap/scss/_progress.scss", "webpack:///./node_modules/bootstrap/scss/_list-group.scss", "webpack:///./node_modules/bootstrap/scss/_close.scss", "webpack:///./node_modules/bootstrap/scss/_toasts.scss", "webpack:///./node_modules/bootstrap/scss/_modal.scss", "webpack:///./node_modules/bootstrap/scss/mixins/_backdrop.scss", "webpack:///./node_modules/bootstrap/scss/_tooltip.scss", "webpack:///./node_modules/bootstrap/scss/mixins/_reset-text.scss", "webpack:///./node_modules/bootstrap/scss/_popover.scss", "webpack:///./node_modules/bootstrap/scss/_carousel.scss", "webpack:///./node_modules/bootstrap/scss/mixins/_clearfix.scss", "webpack:///./node_modules/bootstrap/scss/_spinners.scss", "webpack:///./node_modules/bootstrap/scss/_offcanvas.scss", "webpack:///./node_modules/bootstrap/scss/_placeholders.scss", "webpack:///./node_modules/bootstrap/scss/helpers/_color-bg.scss", "webpack:///./node_modules/bootstrap/scss/helpers/_colored-links.scss", "webpack:///./node_modules/bootstrap/scss/helpers/_focus-ring.scss", "webpack:///./node_modules/bootstrap/scss/helpers/_icon-link.scss", "webpack:///./node_modules/bootstrap/scss/helpers/_ratio.scss", "webpack:///./node_modules/bootstrap/scss/helpers/_position.scss", "webpack:///./node_modules/bootstrap/scss/helpers/_stacks.scss", "webpack:///./node_modules/bootstrap/scss/helpers/_visually-hidden.scss", "webpack:///./node_modules/bootstrap/scss/mixins/_visually-hidden.scss", "webpack:///./node_modules/bootstrap/scss/helpers/_stretched-link.scss", "webpack:///./node_modules/bootstrap/scss/helpers/_text-truncation.scss", "webpack:///./node_modules/bootstrap/scss/mixins/_text-truncate.scss", "webpack:///./node_modules/bootstrap/scss/helpers/_vr.scss", "webpack:///./node_modules/bootstrap/scss/mixins/_utilities.scss", "webpack:///./node_modules/bootstrap/scss/utilities/_api.scss"], "sourcesContent": ["//\n// Headings\n//\n.h1 {\n  @extend h1;\n}\n\n.h2 {\n  @extend h2;\n}\n\n.h3 {\n  @extend h3;\n}\n\n.h4 {\n  @extend h4;\n}\n\n.h5 {\n  @extend h5;\n}\n\n.h6 {\n  @extend h6;\n}\n\n\n.lead {\n  @include font-size($lead-font-size);\n  font-weight: $lead-font-weight;\n}\n\n// Type display classes\n@each $display, $font-size in $display-font-sizes {\n  .display-#{$display} {\n    @include font-size($font-size);\n    font-family: $display-font-family;\n    font-style: $display-font-style;\n    font-weight: $display-font-weight;\n    line-height: $display-line-height;\n  }\n}\n\n//\n// Emphasis\n//\n.small {\n  @extend small;\n}\n\n.mark {\n  @extend mark;\n}\n\n//\n// Lists\n//\n\n.list-unstyled {\n  @include list-unstyled();\n}\n\n// Inline turns list items into inline-block\n.list-inline {\n  @include list-unstyled();\n}\n.list-inline-item {\n  display: inline-block;\n\n  &:not(:last-child) {\n    margin-right: $list-inline-padding;\n  }\n}\n\n\n//\n// Misc\n//\n\n// Builds on `abbr`\n.initialism {\n  @include font-size($initialism-font-size);\n  text-transform: uppercase;\n}\n\n// Blockquotes\n.blockquote {\n  margin-bottom: $blockquote-margin-y;\n  @include font-size($blockquote-font-size);\n\n  > :last-child {\n    margin-bottom: 0;\n  }\n}\n\n.blockquote-footer {\n  margin-top: -$blockquote-margin-y;\n  margin-bottom: $blockquote-margin-y;\n  @include font-size($blockquote-footer-font-size);\n  color: $blockquote-footer-color;\n\n  &::before {\n    content: \"\\2014\\00A0\"; // em dash, nbsp\n  }\n}\n", "// Fonts\n@import url('https://fonts.googleapis.com/css?family=Nunito');\n\n// Variables\n@import 'variables';\n\n// Bootstrap\n@import '~bootstrap/scss/bootstrap';\n", "@mixin bsBanner($file) {\n  /*!\n   * Bootstrap #{$file} v5.3.3 (https://getbootstrap.com/)\n   * Copyright 2011-2024 The Bootstrap Authors\n   * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n   */\n}\n", ":root,\n[data-bs-theme=\"light\"] {\n  // Note: Custom variable values only support SassScript inside `#{}`.\n\n  // Colors\n  //\n  // Generate palettes for full colors, grays, and theme colors.\n\n  @each $color, $value in $colors {\n    --#{$prefix}#{$color}: #{$value};\n  }\n\n  @each $color, $value in $grays {\n    --#{$prefix}gray-#{$color}: #{$value};\n  }\n\n  @each $color, $value in $theme-colors {\n    --#{$prefix}#{$color}: #{$value};\n  }\n\n  @each $color, $value in $theme-colors-rgb {\n    --#{$prefix}#{$color}-rgb: #{$value};\n  }\n\n  @each $color, $value in $theme-colors-text {\n    --#{$prefix}#{$color}-text-emphasis: #{$value};\n  }\n\n  @each $color, $value in $theme-colors-bg-subtle {\n    --#{$prefix}#{$color}-bg-subtle: #{$value};\n  }\n\n  @each $color, $value in $theme-colors-border-subtle {\n    --#{$prefix}#{$color}-border-subtle: #{$value};\n  }\n\n  --#{$prefix}white-rgb: #{to-rgb($white)};\n  --#{$prefix}black-rgb: #{to-rgb($black)};\n\n  // Fonts\n\n  // Note: Use `inspect` for lists so that quoted items keep the quotes.\n  // See https://github.com/sass/sass/issues/2383#issuecomment-336349172\n  --#{$prefix}font-sans-serif: #{inspect($font-family-sans-serif)};\n  --#{$prefix}font-monospace: #{inspect($font-family-monospace)};\n  --#{$prefix}gradient: #{$gradient};\n\n  // Root and body\n  // scss-docs-start root-body-variables\n  @if $font-size-root != null {\n    --#{$prefix}root-font-size: #{$font-size-root};\n  }\n  --#{$prefix}body-font-family: #{inspect($font-family-base)};\n  @include rfs($font-size-base, --#{$prefix}body-font-size);\n  --#{$prefix}body-font-weight: #{$font-weight-base};\n  --#{$prefix}body-line-height: #{$line-height-base};\n  @if $body-text-align != null {\n    --#{$prefix}body-text-align: #{$body-text-align};\n  }\n\n  --#{$prefix}body-color: #{$body-color};\n  --#{$prefix}body-color-rgb: #{to-rgb($body-color)};\n  --#{$prefix}body-bg: #{$body-bg};\n  --#{$prefix}body-bg-rgb: #{to-rgb($body-bg)};\n\n  --#{$prefix}emphasis-color: #{$body-emphasis-color};\n  --#{$prefix}emphasis-color-rgb: #{to-rgb($body-emphasis-color)};\n\n  --#{$prefix}secondary-color: #{$body-secondary-color};\n  --#{$prefix}secondary-color-rgb: #{to-rgb($body-secondary-color)};\n  --#{$prefix}secondary-bg: #{$body-secondary-bg};\n  --#{$prefix}secondary-bg-rgb: #{to-rgb($body-secondary-bg)};\n\n  --#{$prefix}tertiary-color: #{$body-tertiary-color};\n  --#{$prefix}tertiary-color-rgb: #{to-rgb($body-tertiary-color)};\n  --#{$prefix}tertiary-bg: #{$body-tertiary-bg};\n  --#{$prefix}tertiary-bg-rgb: #{to-rgb($body-tertiary-bg)};\n  // scss-docs-end root-body-variables\n\n  --#{$prefix}heading-color: #{$headings-color};\n\n  --#{$prefix}link-color: #{$link-color};\n  --#{$prefix}link-color-rgb: #{to-rgb($link-color)};\n  --#{$prefix}link-decoration: #{$link-decoration};\n\n  --#{$prefix}link-hover-color: #{$link-hover-color};\n  --#{$prefix}link-hover-color-rgb: #{to-rgb($link-hover-color)};\n\n  @if $link-hover-decoration != null {\n    --#{$prefix}link-hover-decoration: #{$link-hover-decoration};\n  }\n\n  --#{$prefix}code-color: #{$code-color};\n  --#{$prefix}highlight-color: #{$mark-color};\n  --#{$prefix}highlight-bg: #{$mark-bg};\n\n  // scss-docs-start root-border-var\n  --#{$prefix}border-width: #{$border-width};\n  --#{$prefix}border-style: #{$border-style};\n  --#{$prefix}border-color: #{$border-color};\n  --#{$prefix}border-color-translucent: #{$border-color-translucent};\n\n  --#{$prefix}border-radius: #{$border-radius};\n  --#{$prefix}border-radius-sm: #{$border-radius-sm};\n  --#{$prefix}border-radius-lg: #{$border-radius-lg};\n  --#{$prefix}border-radius-xl: #{$border-radius-xl};\n  --#{$prefix}border-radius-xxl: #{$border-radius-xxl};\n  --#{$prefix}border-radius-2xl: var(--#{$prefix}border-radius-xxl); // Deprecated in v5.3.0 for consistency\n  --#{$prefix}border-radius-pill: #{$border-radius-pill};\n  // scss-docs-end root-border-var\n\n  --#{$prefix}box-shadow: #{$box-shadow};\n  --#{$prefix}box-shadow-sm: #{$box-shadow-sm};\n  --#{$prefix}box-shadow-lg: #{$box-shadow-lg};\n  --#{$prefix}box-shadow-inset: #{$box-shadow-inset};\n\n  // Focus styles\n  // scss-docs-start root-focus-variables\n  --#{$prefix}focus-ring-width: #{$focus-ring-width};\n  --#{$prefix}focus-ring-opacity: #{$focus-ring-opacity};\n  --#{$prefix}focus-ring-color: #{$focus-ring-color};\n  // scss-docs-end root-focus-variables\n\n  // scss-docs-start root-form-validation-variables\n  --#{$prefix}form-valid-color: #{$form-valid-color};\n  --#{$prefix}form-valid-border-color: #{$form-valid-border-color};\n  --#{$prefix}form-invalid-color: #{$form-invalid-color};\n  --#{$prefix}form-invalid-border-color: #{$form-invalid-border-color};\n  // scss-docs-end root-form-validation-variables\n}\n\n@if $enable-dark-mode {\n  @include color-mode(dark, true) {\n    color-scheme: dark;\n\n    // scss-docs-start root-dark-mode-vars\n    --#{$prefix}body-color: #{$body-color-dark};\n    --#{$prefix}body-color-rgb: #{to-rgb($body-color-dark)};\n    --#{$prefix}body-bg: #{$body-bg-dark};\n    --#{$prefix}body-bg-rgb: #{to-rgb($body-bg-dark)};\n\n    --#{$prefix}emphasis-color: #{$body-emphasis-color-dark};\n    --#{$prefix}emphasis-color-rgb: #{to-rgb($body-emphasis-color-dark)};\n\n    --#{$prefix}secondary-color: #{$body-secondary-color-dark};\n    --#{$prefix}secondary-color-rgb: #{to-rgb($body-secondary-color-dark)};\n    --#{$prefix}secondary-bg: #{$body-secondary-bg-dark};\n    --#{$prefix}secondary-bg-rgb: #{to-rgb($body-secondary-bg-dark)};\n\n    --#{$prefix}tertiary-color: #{$body-tertiary-color-dark};\n    --#{$prefix}tertiary-color-rgb: #{to-rgb($body-tertiary-color-dark)};\n    --#{$prefix}tertiary-bg: #{$body-tertiary-bg-dark};\n    --#{$prefix}tertiary-bg-rgb: #{to-rgb($body-tertiary-bg-dark)};\n\n    @each $color, $value in $theme-colors-text-dark {\n      --#{$prefix}#{$color}-text-emphasis: #{$value};\n    }\n\n    @each $color, $value in $theme-colors-bg-subtle-dark {\n      --#{$prefix}#{$color}-bg-subtle: #{$value};\n    }\n\n    @each $color, $value in $theme-colors-border-subtle-dark {\n      --#{$prefix}#{$color}-border-subtle: #{$value};\n    }\n\n    --#{$prefix}heading-color: #{$headings-color-dark};\n\n    --#{$prefix}link-color: #{$link-color-dark};\n    --#{$prefix}link-hover-color: #{$link-hover-color-dark};\n    --#{$prefix}link-color-rgb: #{to-rgb($link-color-dark)};\n    --#{$prefix}link-hover-color-rgb: #{to-rgb($link-hover-color-dark)};\n\n    --#{$prefix}code-color: #{$code-color-dark};\n    --#{$prefix}highlight-color: #{$mark-color-dark};\n    --#{$prefix}highlight-bg: #{$mark-bg-dark};\n\n    --#{$prefix}border-color: #{$border-color-dark};\n    --#{$prefix}border-color-translucent: #{$border-color-translucent-dark};\n\n    --#{$prefix}form-valid-color: #{$form-valid-color-dark};\n    --#{$prefix}form-valid-border-color: #{$form-valid-border-color-dark};\n    --#{$prefix}form-invalid-color: #{$form-invalid-color-dark};\n    --#{$prefix}form-invalid-border-color: #{$form-invalid-border-color-dark};\n    // scss-docs-end root-dark-mode-vars\n  }\n}\n", "// stylelint-disable scss/dimension-no-non-numeric-values\n\n// SCSS RFS mixin\n//\n// Automated responsive values for font sizes, paddings, margins and much more\n//\n// Licensed under MIT (https://github.com/twbs/rfs/blob/main/LICENSE)\n\n// Configuration\n\n// Base value\n$rfs-base-value: 1.25rem !default;\n$rfs-unit: rem !default;\n\n@if $rfs-unit != rem and $rfs-unit != px {\n  @error \"`#{$rfs-unit}` is not a valid unit for $rfs-unit. Use `px` or `rem`.\";\n}\n\n// Breakpoint at where values start decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n@if $rfs-breakpoint-unit != px and $rfs-breakpoint-unit != em and $rfs-breakpoint-unit != rem {\n  @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n}\n\n// Resize values based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) != number or $rfs-factor <= 1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Mode. Possibilities: \"min-media-query\", \"max-media-query\"\n$rfs-mode: min-media-query !default;\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-rfs to false\n$enable-rfs: true !default;\n\n// Cache $rfs-base-value unit\n$rfs-base-value-unit: unit($rfs-base-value);\n\n@function divide($dividend, $divisor, $precision: 10) {\n  $sign: if($dividend > 0 and $divisor > 0 or $dividend < 0 and $divisor < 0, 1, -1);\n  $dividend: abs($dividend);\n  $divisor: abs($divisor);\n  @if $dividend == 0 {\n    @return 0;\n  }\n  @if $divisor == 0 {\n    @error \"Cannot divide by 0\";\n  }\n  $remainder: $dividend;\n  $result: 0;\n  $factor: 10;\n  @while ($remainder > 0 and $precision >= 0) {\n    $quotient: 0;\n    @while ($remainder >= $divisor) {\n      $remainder: $remainder - $divisor;\n      $quotient: $quotient + 1;\n    }\n    $result: $result * 10 + $quotient;\n    $factor: $factor * .1;\n    $remainder: $remainder * 10;\n    $precision: $precision - 1;\n    @if ($precision < 0 and $remainder >= $divisor * 5) {\n      $result: $result + 1;\n    }\n  }\n  $result: $result * $factor * $sign;\n  $dividend-unit: unit($dividend);\n  $divisor-unit: unit($divisor);\n  $unit-map: (\n    \"px\": 1px,\n    \"rem\": 1rem,\n    \"em\": 1em,\n    \"%\": 1%\n  );\n  @if ($dividend-unit != $divisor-unit and map-has-key($unit-map, $dividend-unit)) {\n    $result: $result * map-get($unit-map, $dividend-unit);\n  }\n  @return $result;\n}\n\n// Remove px-unit from $rfs-base-value for calculations\n@if $rfs-base-value-unit == px {\n  $rfs-base-value: divide($rfs-base-value, $rfs-base-value * 0 + 1);\n}\n@else if $rfs-base-value-unit == rem {\n  $rfs-base-value: divide($rfs-base-value, divide($rfs-base-value * 0 + 1, $rfs-rem-value));\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache == px {\n  $rfs-breakpoint: divide($rfs-breakpoint, $rfs-breakpoint * 0 + 1);\n}\n@else if $rfs-breakpoint-unit-cache == rem or $rfs-breakpoint-unit-cache == \"em\" {\n  $rfs-breakpoint: divide($rfs-breakpoint, divide($rfs-breakpoint * 0 + 1, $rfs-rem-value));\n}\n\n// Calculate the media query value\n$rfs-mq-value: if($rfs-breakpoint-unit == px, #{$rfs-breakpoint}px, #{divide($rfs-breakpoint, $rfs-rem-value)}#{$rfs-breakpoint-unit});\n$rfs-mq-property-width: if($rfs-mode == max-media-query, max-width, min-width);\n$rfs-mq-property-height: if($rfs-mode == max-media-query, max-height, min-height);\n\n// Internal mixin used to determine which media query needs to be used\n@mixin _rfs-media-query {\n  @if $rfs-two-dimensional {\n    @if $rfs-mode == max-media-query {\n      @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}), (#{$rfs-mq-property-height}: #{$rfs-mq-value}) {\n        @content;\n      }\n    }\n    @else {\n      @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}) and (#{$rfs-mq-property-height}: #{$rfs-mq-value}) {\n        @content;\n      }\n    }\n  }\n  @else {\n    @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}) {\n      @content;\n    }\n  }\n}\n\n// Internal mixin that adds disable classes to the selector if needed.\n@mixin _rfs-rule {\n  @if $rfs-class == disable and $rfs-mode == max-media-query {\n    // Adding an extra class increases specificity, which prevents the media query to override the property\n    &,\n    .disable-rfs &,\n    &.disable-rfs {\n      @content;\n    }\n  }\n  @else if $rfs-class == enable and $rfs-mode == min-media-query {\n    .enable-rfs &,\n    &.enable-rfs {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Internal mixin that adds enable classes to the selector if needed.\n@mixin _rfs-media-query-rule {\n\n  @if $rfs-class == enable {\n    @if $rfs-mode == min-media-query {\n      @content;\n    }\n\n    @include _rfs-media-query () {\n      .enable-rfs &,\n      &.enable-rfs {\n        @content;\n      }\n    }\n  }\n  @else {\n    @if $rfs-class == disable and $rfs-mode == min-media-query {\n      .disable-rfs &,\n      &.disable-rfs {\n        @content;\n      }\n    }\n    @include _rfs-media-query () {\n      @content;\n    }\n  }\n}\n\n// Helper function to get the formatted non-responsive value\n@function rfs-value($values) {\n  // Convert to list\n  $values: if(type-of($values) != list, ($values,), $values);\n\n  $val: \"\";\n\n  // Loop over each value and calculate value\n  @each $value in $values {\n    @if $value == 0 {\n      $val: $val + \" 0\";\n    }\n    @else {\n      // Cache $value unit\n      $unit: if(type-of($value) == \"number\", unit($value), false);\n\n      @if $unit == px {\n        // Convert to rem if needed\n        $val: $val + \" \" + if($rfs-unit == rem, #{divide($value, $value * 0 + $rfs-rem-value)}rem, $value);\n      }\n      @else if $unit == rem {\n        // Convert to px if needed\n        $val: $val + \" \" + if($rfs-unit == px, #{divide($value, $value * 0 + 1) * $rfs-rem-value}px, $value);\n      } @else {\n        // If $value isn't a number (like inherit) or $value has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n        $val: $val + \" \" + $value;\n      }\n    }\n  }\n\n  // Remove first space\n  @return unquote(str-slice($val, 2));\n}\n\n// Helper function to get the responsive value calculated by RFS\n@function rfs-fluid-value($values) {\n  // Convert to list\n  $values: if(type-of($values) != list, ($values,), $values);\n\n  $val: \"\";\n\n  // Loop over each value and calculate value\n  @each $value in $values {\n    @if $value == 0 {\n      $val: $val + \" 0\";\n    } @else {\n      // Cache $value unit\n      $unit: if(type-of($value) == \"number\", unit($value), false);\n\n      // If $value isn't a number (like inherit) or $value has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n      @if not $unit or $unit != px and $unit != rem {\n        $val: $val + \" \" + $value;\n      } @else {\n        // Remove unit from $value for calculations\n        $value: divide($value, $value * 0 + if($unit == px, 1, divide(1, $rfs-rem-value)));\n\n        // Only add the media query if the value is greater than the minimum value\n        @if abs($value) <= $rfs-base-value or not $enable-rfs {\n          $val: $val + \" \" + if($rfs-unit == rem, #{divide($value, $rfs-rem-value)}rem, #{$value}px);\n        }\n        @else {\n          // Calculate the minimum value\n          $value-min: $rfs-base-value + divide(abs($value) - $rfs-base-value, $rfs-factor);\n\n          // Calculate difference between $value and the minimum value\n          $value-diff: abs($value) - $value-min;\n\n          // Base value formatting\n          $min-width: if($rfs-unit == rem, #{divide($value-min, $rfs-rem-value)}rem, #{$value-min}px);\n\n          // Use negative value if needed\n          $min-width: if($value < 0, -$min-width, $min-width);\n\n          // Use `vmin` if two-dimensional is enabled\n          $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n          // Calculate the variable width between 0 and $rfs-breakpoint\n          $variable-width: #{divide($value-diff * 100, $rfs-breakpoint)}#{$variable-unit};\n\n          // Return the calculated value\n          $val: $val + \" calc(\" + $min-width + if($value < 0, \" - \", \" + \") + $variable-width + \")\";\n        }\n      }\n    }\n  }\n\n  // Remove first space\n  @return unquote(str-slice($val, 2));\n}\n\n// RFS mixin\n@mixin rfs($values, $property: font-size) {\n  @if $values != null {\n    $val: rfs-value($values);\n    $fluid-val: rfs-fluid-value($values);\n\n    // Do not print the media query if responsive & non-responsive values are the same\n    @if $val == $fluid-val {\n      #{$property}: $val;\n    }\n    @else {\n      @include _rfs-rule () {\n        #{$property}: if($rfs-mode == max-media-query, $val, $fluid-val);\n\n        // Include safari iframe resize fix if needed\n        min-width: if($rfs-safari-iframe-resize-bug-fix, (0 * 1vw), null);\n      }\n\n      @include _rfs-media-query-rule () {\n        #{$property}: if($rfs-mode == max-media-query, $fluid-val, $val);\n      }\n    }\n  }\n}\n\n// Shorthand helper mixins\n@mixin font-size($value) {\n  @include rfs($value);\n}\n\n@mixin padding($value) {\n  @include rfs($value, padding);\n}\n\n@mixin padding-top($value) {\n  @include rfs($value, padding-top);\n}\n\n@mixin padding-right($value) {\n  @include rfs($value, padding-right);\n}\n\n@mixin padding-bottom($value) {\n  @include rfs($value, padding-bottom);\n}\n\n@mixin padding-left($value) {\n  @include rfs($value, padding-left);\n}\n\n@mixin margin($value) {\n  @include rfs($value, margin);\n}\n\n@mixin margin-top($value) {\n  @include rfs($value, margin-top);\n}\n\n@mixin margin-right($value) {\n  @include rfs($value, margin-right);\n}\n\n@mixin margin-bottom($value) {\n  @include rfs($value, margin-bottom);\n}\n\n@mixin margin-left($value) {\n  @include rfs($value, margin-left);\n}\n", "// scss-docs-start color-mode-mixin\n@mixin color-mode($mode: light, $root: false) {\n  @if $color-mode-type == \"media-query\" {\n    @if $root == true {\n      @media (prefers-color-scheme: $mode) {\n        :root {\n          @content;\n        }\n      }\n    } @else {\n      @media (prefers-color-scheme: $mode) {\n        @content;\n      }\n    }\n  } @else {\n    [data-bs-theme=\"#{$mode}\"] {\n      @content;\n    }\n  }\n}\n// scss-docs-end color-mode-mixin\n", "// stylelint-disable declaration-no-important, selector-no-qualifying-type, property-no-vendor-prefix\n\n\n// Reboot\n//\n// Normalization of HTML elements, manually forked from Normalize.css to remove\n// styles targeting irrelevant browsers while applying new styles.\n//\n// Normalize is licensed MIT. https://github.com/necolas/normalize.css\n\n\n// Document\n//\n// Change from `box-sizing: content-box` so that `width` is not affected by `padding` or `border`.\n\n*,\n*::before,\n*::after {\n  box-sizing: border-box;\n}\n\n\n// Root\n//\n// Ability to the value of the root font sizes, affecting the value of `rem`.\n// null by default, thus nothing is generated.\n\n:root {\n  @if $font-size-root != null {\n    @include font-size(var(--#{$prefix}root-font-size));\n  }\n\n  @if $enable-smooth-scroll {\n    @media (prefers-reduced-motion: no-preference) {\n      scroll-behavior: smooth;\n    }\n  }\n}\n\n\n// Body\n//\n// 1. Remove the margin in all browsers.\n// 2. As a best practice, apply a default `background-color`.\n// 3. Prevent adjustments of font size after orientation changes in iOS.\n// 4. Change the default tap highlight to be completely transparent in iOS.\n\n// scss-docs-start reboot-body-rules\nbody {\n  margin: 0; // 1\n  font-family: var(--#{$prefix}body-font-family);\n  @include font-size(var(--#{$prefix}body-font-size));\n  font-weight: var(--#{$prefix}body-font-weight);\n  line-height: var(--#{$prefix}body-line-height);\n  color: var(--#{$prefix}body-color);\n  text-align: var(--#{$prefix}body-text-align);\n  background-color: var(--#{$prefix}body-bg); // 2\n  -webkit-text-size-adjust: 100%; // 3\n  -webkit-tap-highlight-color: rgba($black, 0); // 4\n}\n// scss-docs-end reboot-body-rules\n\n\n// Content grouping\n//\n// 1. Reset Firefox's gray color\n\nhr {\n  margin: $hr-margin-y 0;\n  color: $hr-color; // 1\n  border: 0;\n  border-top: $hr-border-width solid $hr-border-color;\n  opacity: $hr-opacity;\n}\n\n\n// Typography\n//\n// 1. Remove top margins from headings\n//    By default, `<h1>`-`<h6>` all receive top and bottom margins. We nuke the top\n//    margin for easier control within type scales as it avoids margin collapsing.\n\n%heading {\n  margin-top: 0; // 1\n  margin-bottom: $headings-margin-bottom;\n  font-family: $headings-font-family;\n  font-style: $headings-font-style;\n  font-weight: $headings-font-weight;\n  line-height: $headings-line-height;\n  color: var(--#{$prefix}heading-color);\n}\n\nh1 {\n  @extend %heading;\n  @include font-size($h1-font-size);\n}\n\nh2 {\n  @extend %heading;\n  @include font-size($h2-font-size);\n}\n\nh3 {\n  @extend %heading;\n  @include font-size($h3-font-size);\n}\n\nh4 {\n  @extend %heading;\n  @include font-size($h4-font-size);\n}\n\nh5 {\n  @extend %heading;\n  @include font-size($h5-font-size);\n}\n\nh6 {\n  @extend %heading;\n  @include font-size($h6-font-size);\n}\n\n\n// Reset margins on paragraphs\n//\n// Similarly, the top margin on `<p>`s get reset. However, we also reset the\n// bottom margin to use `rem` units instead of `em`.\n\np {\n  margin-top: 0;\n  margin-bottom: $paragraph-margin-bottom;\n}\n\n\n// Abbreviations\n//\n// 1. Add the correct text decoration in Chrome, Edge, Opera, and Safari.\n// 2. Add explicit cursor to indicate changed behavior.\n// 3. Prevent the text-decoration to be skipped.\n\nabbr[title] {\n  text-decoration: underline dotted; // 1\n  cursor: help; // 2\n  text-decoration-skip-ink: none; // 3\n}\n\n\n// Address\n\naddress {\n  margin-bottom: 1rem;\n  font-style: normal;\n  line-height: inherit;\n}\n\n\n// Lists\n\nol,\nul {\n  padding-left: 2rem;\n}\n\nol,\nul,\ndl {\n  margin-top: 0;\n  margin-bottom: 1rem;\n}\n\nol ol,\nul ul,\nol ul,\nul ol {\n  margin-bottom: 0;\n}\n\ndt {\n  font-weight: $dt-font-weight;\n}\n\n// 1. Undo browser default\n\ndd {\n  margin-bottom: .5rem;\n  margin-left: 0; // 1\n}\n\n\n// Blockquote\n\nblockquote {\n  margin: 0 0 1rem;\n}\n\n\n// Strong\n//\n// Add the correct font weight in Chrome, Edge, and Safari\n\nb,\nstrong {\n  font-weight: $font-weight-bolder;\n}\n\n\n// Small\n//\n// Add the correct font size in all browsers\n\nsmall {\n  @include font-size($small-font-size);\n}\n\n\n// Mark\n\nmark {\n  padding: $mark-padding;\n  color: var(--#{$prefix}highlight-color);\n  background-color: var(--#{$prefix}highlight-bg);\n}\n\n\n// Sub and Sup\n//\n// Prevent `sub` and `sup` elements from affecting the line height in\n// all browsers.\n\nsub,\nsup {\n  position: relative;\n  @include font-size($sub-sup-font-size);\n  line-height: 0;\n  vertical-align: baseline;\n}\n\nsub { bottom: -.25em; }\nsup { top: -.5em; }\n\n\n// Links\n\na {\n  color: rgba(var(--#{$prefix}link-color-rgb), var(--#{$prefix}link-opacity, 1));\n  text-decoration: $link-decoration;\n\n  &:hover {\n    --#{$prefix}link-color-rgb: var(--#{$prefix}link-hover-color-rgb);\n    text-decoration: $link-hover-decoration;\n  }\n}\n\n// And undo these styles for placeholder links/named anchors (without href).\n// It would be more straightforward to just use a[href] in previous block, but that\n// causes specificity issues in many other styles that are too complex to fix.\n// See https://github.com/twbs/bootstrap/issues/19402\n\na:not([href]):not([class]) {\n  &,\n  &:hover {\n    color: inherit;\n    text-decoration: none;\n  }\n}\n\n\n// Code\n\npre,\ncode,\nkbd,\nsamp {\n  font-family: $font-family-code;\n  @include font-size(1em); // Correct the odd `em` font sizing in all browsers.\n}\n\n// 1. Remove browser default top margin\n// 2. Reset browser default of `1em` to use `rem`s\n// 3. Don't allow content to break outside\n\npre {\n  display: block;\n  margin-top: 0; // 1\n  margin-bottom: 1rem; // 2\n  overflow: auto; // 3\n  @include font-size($code-font-size);\n  color: $pre-color;\n\n  // Account for some code outputs that place code tags in pre tags\n  code {\n    @include font-size(inherit);\n    color: inherit;\n    word-break: normal;\n  }\n}\n\ncode {\n  @include font-size($code-font-size);\n  color: var(--#{$prefix}code-color);\n  word-wrap: break-word;\n\n  // Streamline the style when inside anchors to avoid broken underline and more\n  a > & {\n    color: inherit;\n  }\n}\n\nkbd {\n  padding: $kbd-padding-y $kbd-padding-x;\n  @include font-size($kbd-font-size);\n  color: $kbd-color;\n  background-color: $kbd-bg;\n  @include border-radius($border-radius-sm);\n\n  kbd {\n    padding: 0;\n    @include font-size(1em);\n    font-weight: $nested-kbd-font-weight;\n  }\n}\n\n\n// Figures\n//\n// Apply a consistent margin strategy (matches our type styles).\n\nfigure {\n  margin: 0 0 1rem;\n}\n\n\n// Images and content\n\nimg,\nsvg {\n  vertical-align: middle;\n}\n\n\n// Tables\n//\n// Prevent double borders\n\ntable {\n  caption-side: bottom;\n  border-collapse: collapse;\n}\n\ncaption {\n  padding-top: $table-cell-padding-y;\n  padding-bottom: $table-cell-padding-y;\n  color: $table-caption-color;\n  text-align: left;\n}\n\n// 1. Removes font-weight bold by inheriting\n// 2. Matches default `<td>` alignment by inheriting `text-align`.\n// 3. Fix alignment for Safari\n\nth {\n  font-weight: $table-th-font-weight; // 1\n  text-align: inherit; // 2\n  text-align: -webkit-match-parent; // 3\n}\n\nthead,\ntbody,\ntfoot,\ntr,\ntd,\nth {\n  border-color: inherit;\n  border-style: solid;\n  border-width: 0;\n}\n\n\n// Forms\n//\n// 1. Allow labels to use `margin` for spacing.\n\nlabel {\n  display: inline-block; // 1\n}\n\n// Remove the default `border-radius` that macOS Chrome adds.\n// See https://github.com/twbs/bootstrap/issues/24093\n\nbutton {\n  // stylelint-disable-next-line property-disallowed-list\n  border-radius: 0;\n}\n\n// Explicitly remove focus outline in Chromium when it shouldn't be\n// visible (e.g. as result of mouse click or touch tap). It already\n// should be doing this automatically, but seems to currently be\n// confused and applies its very visible two-tone outline anyway.\n\nbutton:focus:not(:focus-visible) {\n  outline: 0;\n}\n\n// 1. Remove the margin in Firefox and Safari\n\ninput,\nbutton,\nselect,\noptgroup,\ntextarea {\n  margin: 0; // 1\n  font-family: inherit;\n  @include font-size(inherit);\n  line-height: inherit;\n}\n\n// Remove the inheritance of text transform in Firefox\nbutton,\nselect {\n  text-transform: none;\n}\n// Set the cursor for non-`<button>` buttons\n//\n// Details at https://github.com/twbs/bootstrap/pull/30562\n[role=\"button\"] {\n  cursor: pointer;\n}\n\nselect {\n  // Remove the inheritance of word-wrap in Safari.\n  // See https://github.com/twbs/bootstrap/issues/24990\n  word-wrap: normal;\n\n  // Undo the opacity change from Chrome\n  &:disabled {\n    opacity: 1;\n  }\n}\n\n// Remove the dropdown arrow only from text type inputs built with datalists in Chrome.\n// See https://stackoverflow.com/a/54997118\n\n[list]:not([type=\"date\"]):not([type=\"datetime-local\"]):not([type=\"month\"]):not([type=\"week\"]):not([type=\"time\"])::-webkit-calendar-picker-indicator {\n  display: none !important;\n}\n\n// 1. Prevent a WebKit bug where (2) destroys native `audio` and `video`\n//    controls in Android 4.\n// 2. Correct the inability to style clickable types in iOS and Safari.\n// 3. Opinionated: add \"hand\" cursor to non-disabled button elements.\n\nbutton,\n[type=\"button\"], // 1\n[type=\"reset\"],\n[type=\"submit\"] {\n  -webkit-appearance: button; // 2\n\n  @if $enable-button-pointers {\n    &:not(:disabled) {\n      cursor: pointer; // 3\n    }\n  }\n}\n\n// Remove inner border and padding from Firefox, but don't restore the outline like Normalize.\n\n::-moz-focus-inner {\n  padding: 0;\n  border-style: none;\n}\n\n// 1. Textareas should really only resize vertically so they don't break their (horizontal) containers.\n\ntextarea {\n  resize: vertical; // 1\n}\n\n// 1. Browsers set a default `min-width: min-content;` on fieldsets,\n//    unlike e.g. `<div>`s, which have `min-width: 0;` by default.\n//    So we reset that to ensure fieldsets behave more like a standard block element.\n//    See https://github.com/twbs/bootstrap/issues/12359\n//    and https://html.spec.whatwg.org/multipage/#the-fieldset-and-legend-elements\n// 2. Reset the default outline behavior of fieldsets so they don't affect page layout.\n\nfieldset {\n  min-width: 0; // 1\n  padding: 0; // 2\n  margin: 0; // 2\n  border: 0; // 2\n}\n\n// 1. By using `float: left`, the legend will behave like a block element.\n//    This way the border of a fieldset wraps around the legend if present.\n// 2. Fix wrapping bug.\n//    See https://github.com/twbs/bootstrap/issues/29712\n\nlegend {\n  float: left; // 1\n  width: 100%;\n  padding: 0;\n  margin-bottom: $legend-margin-bottom;\n  @include font-size($legend-font-size);\n  font-weight: $legend-font-weight;\n  line-height: inherit;\n\n  + * {\n    clear: left; // 2\n  }\n}\n\n// Fix height of inputs with a type of datetime-local, date, month, week, or time\n// See https://github.com/twbs/bootstrap/issues/18842\n\n::-webkit-datetime-edit-fields-wrapper,\n::-webkit-datetime-edit-text,\n::-webkit-datetime-edit-minute,\n::-webkit-datetime-edit-hour-field,\n::-webkit-datetime-edit-day-field,\n::-webkit-datetime-edit-month-field,\n::-webkit-datetime-edit-year-field {\n  padding: 0;\n}\n\n::-webkit-inner-spin-button {\n  height: auto;\n}\n\n// 1. This overrides the extra rounded corners on search inputs in iOS so that our\n//    `.form-control` class can properly style them. Note that this cannot simply\n//    be added to `.form-control` as it's not specific enough. For details, see\n//    https://github.com/twbs/bootstrap/issues/11586.\n// 2. Correct the outline style in Safari.\n\n[type=\"search\"] {\n  -webkit-appearance: textfield; // 1\n  outline-offset: -2px; // 2\n}\n\n// 1. A few input types should stay LTR\n// See https://rtlstyling.com/posts/rtl-styling#form-inputs\n// 2. RTL only output\n// See https://rtlcss.com/learn/usage-guide/control-directives/#raw\n\n/* rtl:raw:\n[type=\"tel\"],\n[type=\"url\"],\n[type=\"email\"],\n[type=\"number\"] {\n  direction: ltr;\n}\n*/\n\n// Remove the inner padding in Chrome and Safari on macOS.\n\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n// Remove padding around color pickers in webkit browsers\n\n::-webkit-color-swatch-wrapper {\n  padding: 0;\n}\n\n\n// 1. Inherit font family and line height for file input buttons\n// 2. Correct the inability to style clickable types in iOS and Safari.\n\n::file-selector-button {\n  font: inherit; // 1\n  -webkit-appearance: button; // 2\n}\n\n// Correct element displays\n\noutput {\n  display: inline-block;\n}\n\n// Remove border from iframe\n\niframe {\n  border: 0;\n}\n\n// Summary\n//\n// 1. Add the correct display in all browsers\n\nsummary {\n  display: list-item; // 1\n  cursor: pointer;\n}\n\n\n// Progress\n//\n// Add the correct vertical alignment in Chrome, Firefox, and Opera.\n\nprogress {\n  vertical-align: baseline;\n}\n\n\n// Hidden attribute\n//\n// Always hide an element with the `hidden` HTML attribute.\n\n[hidden] {\n  display: none !important;\n}\n", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n// scss-docs-start gray-color-variables\n$white:    #fff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black:    #000 !default;\n// scss-docs-end gray-color-variables\n\n// fusv-disable\n// scss-docs-start gray-colors-map\n$grays: (\n  \"100\": $gray-100,\n  \"200\": $gray-200,\n  \"300\": $gray-300,\n  \"400\": $gray-400,\n  \"500\": $gray-500,\n  \"600\": $gray-600,\n  \"700\": $gray-700,\n  \"800\": $gray-800,\n  \"900\": $gray-900\n) !default;\n// scss-docs-end gray-colors-map\n// fusv-enable\n\n// scss-docs-start color-variables\n$blue:    #0d6efd !default;\n$indigo:  #6610f2 !default;\n$purple:  #6f42c1 !default;\n$pink:    #d63384 !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #198754 !default;\n$teal:    #20c997 !default;\n$cyan:    #0dcaf0 !default;\n// scss-docs-end color-variables\n\n// scss-docs-start colors-map\n$colors: (\n  \"blue\":       $blue,\n  \"indigo\":     $indigo,\n  \"purple\":     $purple,\n  \"pink\":       $pink,\n  \"red\":        $red,\n  \"orange\":     $orange,\n  \"yellow\":     $yellow,\n  \"green\":      $green,\n  \"teal\":       $teal,\n  \"cyan\":       $cyan,\n  \"black\":      $black,\n  \"white\":      $white,\n  \"gray\":       $gray-600,\n  \"gray-dark\":  $gray-800\n) !default;\n// scss-docs-end colors-map\n\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.\n// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast\n$min-contrast-ratio:   4.5 !default;\n\n// Customize the light and dark text colors for use in our color contrast function.\n$color-contrast-dark:      $black !default;\n$color-contrast-light:     $white !default;\n\n// fusv-disable\n$blue-100: tint-color($blue, 80%) !default;\n$blue-200: tint-color($blue, 60%) !default;\n$blue-300: tint-color($blue, 40%) !default;\n$blue-400: tint-color($blue, 20%) !default;\n$blue-500: $blue !default;\n$blue-600: shade-color($blue, 20%) !default;\n$blue-700: shade-color($blue, 40%) !default;\n$blue-800: shade-color($blue, 60%) !default;\n$blue-900: shade-color($blue, 80%) !default;\n\n$indigo-100: tint-color($indigo, 80%) !default;\n$indigo-200: tint-color($indigo, 60%) !default;\n$indigo-300: tint-color($indigo, 40%) !default;\n$indigo-400: tint-color($indigo, 20%) !default;\n$indigo-500: $indigo !default;\n$indigo-600: shade-color($indigo, 20%) !default;\n$indigo-700: shade-color($indigo, 40%) !default;\n$indigo-800: shade-color($indigo, 60%) !default;\n$indigo-900: shade-color($indigo, 80%) !default;\n\n$purple-100: tint-color($purple, 80%) !default;\n$purple-200: tint-color($purple, 60%) !default;\n$purple-300: tint-color($purple, 40%) !default;\n$purple-400: tint-color($purple, 20%) !default;\n$purple-500: $purple !default;\n$purple-600: shade-color($purple, 20%) !default;\n$purple-700: shade-color($purple, 40%) !default;\n$purple-800: shade-color($purple, 60%) !default;\n$purple-900: shade-color($purple, 80%) !default;\n\n$pink-100: tint-color($pink, 80%) !default;\n$pink-200: tint-color($pink, 60%) !default;\n$pink-300: tint-color($pink, 40%) !default;\n$pink-400: tint-color($pink, 20%) !default;\n$pink-500: $pink !default;\n$pink-600: shade-color($pink, 20%) !default;\n$pink-700: shade-color($pink, 40%) !default;\n$pink-800: shade-color($pink, 60%) !default;\n$pink-900: shade-color($pink, 80%) !default;\n\n$red-100: tint-color($red, 80%) !default;\n$red-200: tint-color($red, 60%) !default;\n$red-300: tint-color($red, 40%) !default;\n$red-400: tint-color($red, 20%) !default;\n$red-500: $red !default;\n$red-600: shade-color($red, 20%) !default;\n$red-700: shade-color($red, 40%) !default;\n$red-800: shade-color($red, 60%) !default;\n$red-900: shade-color($red, 80%) !default;\n\n$orange-100: tint-color($orange, 80%) !default;\n$orange-200: tint-color($orange, 60%) !default;\n$orange-300: tint-color($orange, 40%) !default;\n$orange-400: tint-color($orange, 20%) !default;\n$orange-500: $orange !default;\n$orange-600: shade-color($orange, 20%) !default;\n$orange-700: shade-color($orange, 40%) !default;\n$orange-800: shade-color($orange, 60%) !default;\n$orange-900: shade-color($orange, 80%) !default;\n\n$yellow-100: tint-color($yellow, 80%) !default;\n$yellow-200: tint-color($yellow, 60%) !default;\n$yellow-300: tint-color($yellow, 40%) !default;\n$yellow-400: tint-color($yellow, 20%) !default;\n$yellow-500: $yellow !default;\n$yellow-600: shade-color($yellow, 20%) !default;\n$yellow-700: shade-color($yellow, 40%) !default;\n$yellow-800: shade-color($yellow, 60%) !default;\n$yellow-900: shade-color($yellow, 80%) !default;\n\n$green-100: tint-color($green, 80%) !default;\n$green-200: tint-color($green, 60%) !default;\n$green-300: tint-color($green, 40%) !default;\n$green-400: tint-color($green, 20%) !default;\n$green-500: $green !default;\n$green-600: shade-color($green, 20%) !default;\n$green-700: shade-color($green, 40%) !default;\n$green-800: shade-color($green, 60%) !default;\n$green-900: shade-color($green, 80%) !default;\n\n$teal-100: tint-color($teal, 80%) !default;\n$teal-200: tint-color($teal, 60%) !default;\n$teal-300: tint-color($teal, 40%) !default;\n$teal-400: tint-color($teal, 20%) !default;\n$teal-500: $teal !default;\n$teal-600: shade-color($teal, 20%) !default;\n$teal-700: shade-color($teal, 40%) !default;\n$teal-800: shade-color($teal, 60%) !default;\n$teal-900: shade-color($teal, 80%) !default;\n\n$cyan-100: tint-color($cyan, 80%) !default;\n$cyan-200: tint-color($cyan, 60%) !default;\n$cyan-300: tint-color($cyan, 40%) !default;\n$cyan-400: tint-color($cyan, 20%) !default;\n$cyan-500: $cyan !default;\n$cyan-600: shade-color($cyan, 20%) !default;\n$cyan-700: shade-color($cyan, 40%) !default;\n$cyan-800: shade-color($cyan, 60%) !default;\n$cyan-900: shade-color($cyan, 80%) !default;\n\n$blues: (\n  \"blue-100\": $blue-100,\n  \"blue-200\": $blue-200,\n  \"blue-300\": $blue-300,\n  \"blue-400\": $blue-400,\n  \"blue-500\": $blue-500,\n  \"blue-600\": $blue-600,\n  \"blue-700\": $blue-700,\n  \"blue-800\": $blue-800,\n  \"blue-900\": $blue-900\n) !default;\n\n$indigos: (\n  \"indigo-100\": $indigo-100,\n  \"indigo-200\": $indigo-200,\n  \"indigo-300\": $indigo-300,\n  \"indigo-400\": $indigo-400,\n  \"indigo-500\": $indigo-500,\n  \"indigo-600\": $indigo-600,\n  \"indigo-700\": $indigo-700,\n  \"indigo-800\": $indigo-800,\n  \"indigo-900\": $indigo-900\n) !default;\n\n$purples: (\n  \"purple-100\": $purple-100,\n  \"purple-200\": $purple-200,\n  \"purple-300\": $purple-300,\n  \"purple-400\": $purple-400,\n  \"purple-500\": $purple-500,\n  \"purple-600\": $purple-600,\n  \"purple-700\": $purple-700,\n  \"purple-800\": $purple-800,\n  \"purple-900\": $purple-900\n) !default;\n\n$pinks: (\n  \"pink-100\": $pink-100,\n  \"pink-200\": $pink-200,\n  \"pink-300\": $pink-300,\n  \"pink-400\": $pink-400,\n  \"pink-500\": $pink-500,\n  \"pink-600\": $pink-600,\n  \"pink-700\": $pink-700,\n  \"pink-800\": $pink-800,\n  \"pink-900\": $pink-900\n) !default;\n\n$reds: (\n  \"red-100\": $red-100,\n  \"red-200\": $red-200,\n  \"red-300\": $red-300,\n  \"red-400\": $red-400,\n  \"red-500\": $red-500,\n  \"red-600\": $red-600,\n  \"red-700\": $red-700,\n  \"red-800\": $red-800,\n  \"red-900\": $red-900\n) !default;\n\n$oranges: (\n  \"orange-100\": $orange-100,\n  \"orange-200\": $orange-200,\n  \"orange-300\": $orange-300,\n  \"orange-400\": $orange-400,\n  \"orange-500\": $orange-500,\n  \"orange-600\": $orange-600,\n  \"orange-700\": $orange-700,\n  \"orange-800\": $orange-800,\n  \"orange-900\": $orange-900\n) !default;\n\n$yellows: (\n  \"yellow-100\": $yellow-100,\n  \"yellow-200\": $yellow-200,\n  \"yellow-300\": $yellow-300,\n  \"yellow-400\": $yellow-400,\n  \"yellow-500\": $yellow-500,\n  \"yellow-600\": $yellow-600,\n  \"yellow-700\": $yellow-700,\n  \"yellow-800\": $yellow-800,\n  \"yellow-900\": $yellow-900\n) !default;\n\n$greens: (\n  \"green-100\": $green-100,\n  \"green-200\": $green-200,\n  \"green-300\": $green-300,\n  \"green-400\": $green-400,\n  \"green-500\": $green-500,\n  \"green-600\": $green-600,\n  \"green-700\": $green-700,\n  \"green-800\": $green-800,\n  \"green-900\": $green-900\n) !default;\n\n$teals: (\n  \"teal-100\": $teal-100,\n  \"teal-200\": $teal-200,\n  \"teal-300\": $teal-300,\n  \"teal-400\": $teal-400,\n  \"teal-500\": $teal-500,\n  \"teal-600\": $teal-600,\n  \"teal-700\": $teal-700,\n  \"teal-800\": $teal-800,\n  \"teal-900\": $teal-900\n) !default;\n\n$cyans: (\n  \"cyan-100\": $cyan-100,\n  \"cyan-200\": $cyan-200,\n  \"cyan-300\": $cyan-300,\n  \"cyan-400\": $cyan-400,\n  \"cyan-500\": $cyan-500,\n  \"cyan-600\": $cyan-600,\n  \"cyan-700\": $cyan-700,\n  \"cyan-800\": $cyan-800,\n  \"cyan-900\": $cyan-900\n) !default;\n// fusv-enable\n\n// scss-docs-start theme-color-variables\n$primary:       $blue !default;\n$secondary:     $gray-600 !default;\n$success:       $green !default;\n$info:          $cyan !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-100 !default;\n$dark:          $gray-900 !default;\n// scss-docs-end theme-color-variables\n\n// scss-docs-start theme-colors-map\n$theme-colors: (\n  \"primary\":    $primary,\n  \"secondary\":  $secondary,\n  \"success\":    $success,\n  \"info\":       $info,\n  \"warning\":    $warning,\n  \"danger\":     $danger,\n  \"light\":      $light,\n  \"dark\":       $dark\n) !default;\n// scss-docs-end theme-colors-map\n\n// scss-docs-start theme-text-variables\n$primary-text-emphasis:   shade-color($primary, 60%) !default;\n$secondary-text-emphasis: shade-color($secondary, 60%) !default;\n$success-text-emphasis:   shade-color($success, 60%) !default;\n$info-text-emphasis:      shade-color($info, 60%) !default;\n$warning-text-emphasis:   shade-color($warning, 60%) !default;\n$danger-text-emphasis:    shade-color($danger, 60%) !default;\n$light-text-emphasis:     $gray-700 !default;\n$dark-text-emphasis:      $gray-700 !default;\n// scss-docs-end theme-text-variables\n\n// scss-docs-start theme-bg-subtle-variables\n$primary-bg-subtle:       tint-color($primary, 80%) !default;\n$secondary-bg-subtle:     tint-color($secondary, 80%) !default;\n$success-bg-subtle:       tint-color($success, 80%) !default;\n$info-bg-subtle:          tint-color($info, 80%) !default;\n$warning-bg-subtle:       tint-color($warning, 80%) !default;\n$danger-bg-subtle:        tint-color($danger, 80%) !default;\n$light-bg-subtle:         mix($gray-100, $white) !default;\n$dark-bg-subtle:          $gray-400 !default;\n// scss-docs-end theme-bg-subtle-variables\n\n// scss-docs-start theme-border-subtle-variables\n$primary-border-subtle:   tint-color($primary, 60%) !default;\n$secondary-border-subtle: tint-color($secondary, 60%) !default;\n$success-border-subtle:   tint-color($success, 60%) !default;\n$info-border-subtle:      tint-color($info, 60%) !default;\n$warning-border-subtle:   tint-color($warning, 60%) !default;\n$danger-border-subtle:    tint-color($danger, 60%) !default;\n$light-border-subtle:     $gray-200 !default;\n$dark-border-subtle:      $gray-500 !default;\n// scss-docs-end theme-border-subtle-variables\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: (\n  (\"<\", \"%3c\"),\n  (\">\", \"%3e\"),\n  (\"#\", \"%23\"),\n  (\"(\", \"%28\"),\n  (\")\", \"%29\"),\n) !default;\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                true !default;\n$enable-rounded:              true !default;\n$enable-shadows:              false !default;\n$enable-gradients:            false !default;\n$enable-transitions:          true !default;\n$enable-reduced-motion:       true !default;\n$enable-smooth-scroll:        true !default;\n$enable-grid-classes:         true !default;\n$enable-container-classes:    true !default;\n$enable-cssgrid:              false !default;\n$enable-button-pointers:      true !default;\n$enable-rfs:                  true !default;\n$enable-validation-icons:     true !default;\n$enable-negative-margins:     false !default;\n$enable-deprecation-messages: true !default;\n$enable-important-utilities:  true !default;\n\n$enable-dark-mode:            true !default;\n$color-mode-type:             data !default; // `data` or `media-query`\n\n// Prefix for :root CSS variables\n\n$variable-prefix:             bs- !default; // Deprecated in v5.2.0 for the shorter `$prefix`\n$prefix:                      $variable-prefix !default;\n\n// Gradient\n//\n// The gradient which is added to components if `$enable-gradients` is `true`\n// This gradient is also added to elements with `.bg-gradient`\n// scss-docs-start variable-gradient\n$gradient: linear-gradient(180deg, rgba($white, .15), rgba($white, 0)) !default;\n// scss-docs-end variable-gradient\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// scss-docs-start spacer-variables-maps\n$spacer: 1rem !default;\n$spacers: (\n  0: 0,\n  1: $spacer * .25,\n  2: $spacer * .5,\n  3: $spacer,\n  4: $spacer * 1.5,\n  5: $spacer * 3,\n) !default;\n// scss-docs-end spacer-variables-maps\n\n// Position\n//\n// Define the edge positioning anchors of the position utilities.\n\n// scss-docs-start position-map\n$position-values: (\n  0: 0,\n  50: 50%,\n  100: 100%\n) !default;\n// scss-docs-end position-map\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-text-align:           null !default;\n$body-color:                $gray-900 !default;\n$body-bg:                   $white !default;\n\n$body-secondary-color:      rgba($body-color, .75) !default;\n$body-secondary-bg:         $gray-200 !default;\n\n$body-tertiary-color:       rgba($body-color, .5) !default;\n$body-tertiary-bg:          $gray-100 !default;\n\n$body-emphasis-color:       $black !default;\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                              $primary !default;\n$link-decoration:                         underline !default;\n$link-shade-percentage:                   20% !default;\n$link-hover-color:                        shift-color($link-color, $link-shade-percentage) !default;\n$link-hover-decoration:                   null !default;\n\n$stretched-link-pseudo-element:           after !default;\n$stretched-link-z-index:                  1 !default;\n\n// Icon links\n// scss-docs-start icon-link-variables\n$icon-link-gap:               .375rem !default;\n$icon-link-underline-offset:  .25em !default;\n$icon-link-icon-size:         1em !default;\n$icon-link-icon-transition:   .2s ease-in-out transform !default;\n$icon-link-icon-transform:    translate3d(.25em, 0, 0) !default;\n// scss-docs-end icon-link-variables\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n// scss-docs-start grid-breakpoints\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px,\n  xxl: 1400px\n) !default;\n// scss-docs-end grid-breakpoints\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n// scss-docs-start container-max-widths\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px,\n  xxl: 1320px\n) !default;\n// scss-docs-end container-max-widths\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           1.5rem !default;\n$grid-row-columns:            6 !default;\n\n// Container padding\n\n$container-padding-x: $grid-gutter-width !default;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n// scss-docs-start border-variables\n$border-width:                1px !default;\n$border-widths: (\n  1: 1px,\n  2: 2px,\n  3: 3px,\n  4: 4px,\n  5: 5px\n) !default;\n$border-style:                solid !default;\n$border-color:                $gray-300 !default;\n$border-color-translucent:    rgba($black, .175) !default;\n// scss-docs-end border-variables\n\n// scss-docs-start border-radius-variables\n$border-radius:               .375rem !default;\n$border-radius-sm:            .25rem !default;\n$border-radius-lg:            .5rem !default;\n$border-radius-xl:            1rem !default;\n$border-radius-xxl:           2rem !default;\n$border-radius-pill:          50rem !default;\n// scss-docs-end border-radius-variables\n// fusv-disable\n$border-radius-2xl:           $border-radius-xxl !default; // Deprecated in v5.3.0\n// fusv-enable\n\n// scss-docs-start box-shadow-variables\n$box-shadow:                  0 .5rem 1rem rgba($black, .15) !default;\n$box-shadow-sm:               0 .125rem .25rem rgba($black, .075) !default;\n$box-shadow-lg:               0 1rem 3rem rgba($black, .175) !default;\n$box-shadow-inset:            inset 0 1px 2px rgba($black, .075) !default;\n// scss-docs-end box-shadow-variables\n\n$component-active-color:      $white !default;\n$component-active-bg:         $primary !default;\n\n// scss-docs-start focus-ring-variables\n$focus-ring-width:      .25rem !default;\n$focus-ring-opacity:    .25 !default;\n$focus-ring-color:      rgba($primary, $focus-ring-opacity) !default;\n$focus-ring-blur:       0 !default;\n$focus-ring-box-shadow: 0 0 $focus-ring-blur $focus-ring-width $focus-ring-color !default;\n// scss-docs-end focus-ring-variables\n\n// scss-docs-start caret-variables\n$caret-width:                 .3em !default;\n$caret-vertical-align:        $caret-width * .85 !default;\n$caret-spacing:               $caret-width * .85 !default;\n// scss-docs-end caret-variables\n\n$transition-base:             all .2s ease-in-out !default;\n$transition-fade:             opacity .15s linear !default;\n// scss-docs-start collapse-transition\n$transition-collapse:         height .35s ease !default;\n$transition-collapse-width:   width .35s ease !default;\n// scss-docs-end collapse-transition\n\n// stylelint-disable function-disallowed-list\n// scss-docs-start aspect-ratios\n$aspect-ratios: (\n  \"1x1\": 100%,\n  \"4x3\": calc(3 / 4 * 100%),\n  \"16x9\": calc(9 / 16 * 100%),\n  \"21x9\": calc(9 / 21 * 100%)\n) !default;\n// scss-docs-end aspect-ratios\n// stylelint-enable function-disallowed-list\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// scss-docs-start font-variables\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      system-ui, -apple-system, \"Segoe UI\", Roboto, \"Helvetica Neue\", \"Noto Sans\", \"Liberation Sans\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n// stylelint-enable value-keyword-case\n$font-family-base:            var(--#{$prefix}font-sans-serif) !default;\n$font-family-code:            var(--#{$prefix}font-monospace) !default;\n\n// $font-size-root affects the value of `rem`, which is used for as well font sizes, paddings, and margins\n// $font-size-base affects the font size of the body text\n$font-size-root:              null !default;\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-sm:                $font-size-base * .875 !default;\n$font-size-lg:                $font-size-base * 1.25 !default;\n\n$font-weight-lighter:         lighter !default;\n$font-weight-light:           300 !default;\n$font-weight-normal:          400 !default;\n$font-weight-medium:          500 !default;\n$font-weight-semibold:        600 !default;\n$font-weight-bold:            700 !default;\n$font-weight-bolder:          bolder !default;\n\n$font-weight-base:            $font-weight-normal !default;\n\n$line-height-base:            1.5 !default;\n$line-height-sm:              1.25 !default;\n$line-height-lg:              2 !default;\n\n$h1-font-size:                $font-size-base * 2.5 !default;\n$h2-font-size:                $font-size-base * 2 !default;\n$h3-font-size:                $font-size-base * 1.75 !default;\n$h4-font-size:                $font-size-base * 1.5 !default;\n$h5-font-size:                $font-size-base * 1.25 !default;\n$h6-font-size:                $font-size-base !default;\n// scss-docs-end font-variables\n\n// scss-docs-start font-sizes\n$font-sizes: (\n  1: $h1-font-size,\n  2: $h2-font-size,\n  3: $h3-font-size,\n  4: $h4-font-size,\n  5: $h5-font-size,\n  6: $h6-font-size\n) !default;\n// scss-docs-end font-sizes\n\n// scss-docs-start headings-variables\n$headings-margin-bottom:      $spacer * .5 !default;\n$headings-font-family:        null !default;\n$headings-font-style:         null !default;\n$headings-font-weight:        500 !default;\n$headings-line-height:        1.2 !default;\n$headings-color:              inherit !default;\n// scss-docs-end headings-variables\n\n// scss-docs-start display-headings\n$display-font-sizes: (\n  1: 5rem,\n  2: 4.5rem,\n  3: 4rem,\n  4: 3.5rem,\n  5: 3rem,\n  6: 2.5rem\n) !default;\n\n$display-font-family: null !default;\n$display-font-style:  null !default;\n$display-font-weight: 300 !default;\n$display-line-height: $headings-line-height !default;\n// scss-docs-end display-headings\n\n// scss-docs-start type-variables\n$lead-font-size:              $font-size-base * 1.25 !default;\n$lead-font-weight:            300 !default;\n\n$small-font-size:             .875em !default;\n\n$sub-sup-font-size:           .75em !default;\n\n// fusv-disable\n$text-muted:                  var(--#{$prefix}secondary-color) !default; // Deprecated in 5.3.0\n// fusv-enable\n\n$initialism-font-size:        $small-font-size !default;\n\n$blockquote-margin-y:         $spacer !default;\n$blockquote-font-size:        $font-size-base * 1.25 !default;\n$blockquote-footer-color:     $gray-600 !default;\n$blockquote-footer-font-size: $small-font-size !default;\n\n$hr-margin-y:                 $spacer !default;\n$hr-color:                    inherit !default;\n\n// fusv-disable\n$hr-bg-color:                 null !default; // Deprecated in v5.2.0\n$hr-height:                   null !default; // Deprecated in v5.2.0\n// fusv-enable\n\n$hr-border-color:             null !default; // Allows for inherited colors\n$hr-border-width:             var(--#{$prefix}border-width) !default;\n$hr-opacity:                  .25 !default;\n\n// scss-docs-start vr-variables\n$vr-border-width:             var(--#{$prefix}border-width) !default;\n// scss-docs-end vr-variables\n\n$legend-margin-bottom:        .5rem !default;\n$legend-font-size:            1.5rem !default;\n$legend-font-weight:          null !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$list-inline-padding:         .5rem !default;\n\n$mark-padding:                .1875em !default;\n$mark-color:                  $body-color !default;\n$mark-bg:                     $yellow-100 !default;\n// scss-docs-end type-variables\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n// scss-docs-start table-variables\n$table-cell-padding-y:        .5rem !default;\n$table-cell-padding-x:        .5rem !default;\n$table-cell-padding-y-sm:     .25rem !default;\n$table-cell-padding-x-sm:     .25rem !default;\n\n$table-cell-vertical-align:   top !default;\n\n$table-color:                 var(--#{$prefix}emphasis-color) !default;\n$table-bg:                    var(--#{$prefix}body-bg) !default;\n$table-accent-bg:             transparent !default;\n\n$table-th-font-weight:        null !default;\n\n$table-striped-color:         $table-color !default;\n$table-striped-bg-factor:     .05 !default;\n$table-striped-bg:            rgba(var(--#{$prefix}emphasis-color-rgb), $table-striped-bg-factor) !default;\n\n$table-active-color:          $table-color !default;\n$table-active-bg-factor:      .1 !default;\n$table-active-bg:             rgba(var(--#{$prefix}emphasis-color-rgb), $table-active-bg-factor) !default;\n\n$table-hover-color:           $table-color !default;\n$table-hover-bg-factor:       .075 !default;\n$table-hover-bg:              rgba(var(--#{$prefix}emphasis-color-rgb), $table-hover-bg-factor) !default;\n\n$table-border-factor:         .2 !default;\n$table-border-width:          var(--#{$prefix}border-width) !default;\n$table-border-color:          var(--#{$prefix}border-color) !default;\n\n$table-striped-order:         odd !default;\n$table-striped-columns-order: even !default;\n\n$table-group-separator-color: currentcolor !default;\n\n$table-caption-color:         var(--#{$prefix}secondary-color) !default;\n\n$table-bg-scale:              -80% !default;\n// scss-docs-end table-variables\n\n// scss-docs-start table-loop\n$table-variants: (\n  \"primary\":    shift-color($primary, $table-bg-scale),\n  \"secondary\":  shift-color($secondary, $table-bg-scale),\n  \"success\":    shift-color($success, $table-bg-scale),\n  \"info\":       shift-color($info, $table-bg-scale),\n  \"warning\":    shift-color($warning, $table-bg-scale),\n  \"danger\":     shift-color($danger, $table-bg-scale),\n  \"light\":      $light,\n  \"dark\":       $dark,\n) !default;\n// scss-docs-end table-loop\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n// scss-docs-start input-btn-variables\n$input-btn-padding-y:         .375rem !default;\n$input-btn-padding-x:         .75rem !default;\n$input-btn-font-family:       null !default;\n$input-btn-font-size:         $font-size-base !default;\n$input-btn-line-height:       $line-height-base !default;\n\n$input-btn-focus-width:         $focus-ring-width !default;\n$input-btn-focus-color-opacity: $focus-ring-opacity !default;\n$input-btn-focus-color:         $focus-ring-color !default;\n$input-btn-focus-blur:          $focus-ring-blur !default;\n$input-btn-focus-box-shadow:    $focus-ring-box-shadow !default;\n\n$input-btn-padding-y-sm:      .25rem !default;\n$input-btn-padding-x-sm:      .5rem !default;\n$input-btn-font-size-sm:      $font-size-sm !default;\n\n$input-btn-padding-y-lg:      .5rem !default;\n$input-btn-padding-x-lg:      1rem !default;\n$input-btn-font-size-lg:      $font-size-lg !default;\n\n$input-btn-border-width:      var(--#{$prefix}border-width) !default;\n// scss-docs-end input-btn-variables\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n// scss-docs-start btn-variables\n$btn-color:                   var(--#{$prefix}body-color) !default;\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-font-family:             $input-btn-font-family !default;\n$btn-font-size:               $input-btn-font-size !default;\n$btn-line-height:             $input-btn-line-height !default;\n$btn-white-space:             null !default; // Set to `nowrap` to prevent text wrapping\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-font-size-sm:            $input-btn-font-size-sm !default;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-font-size-lg:            $input-btn-font-size-lg !default;\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-font-weight:             $font-weight-normal !default;\n$btn-box-shadow:              inset 0 1px 0 rgba($white, .15), 0 1px 1px rgba($black, .075) !default;\n$btn-focus-width:             $input-btn-focus-width !default;\n$btn-focus-box-shadow:        $input-btn-focus-box-shadow !default;\n$btn-disabled-opacity:        .65 !default;\n$btn-active-box-shadow:       inset 0 3px 5px rgba($black, .125) !default;\n\n$btn-link-color:              var(--#{$prefix}link-color) !default;\n$btn-link-hover-color:        var(--#{$prefix}link-hover-color) !default;\n$btn-link-disabled-color:     $gray-600 !default;\n$btn-link-focus-shadow-rgb:   to-rgb(mix(color-contrast($link-color), $link-color, 15%)) !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           var(--#{$prefix}border-radius) !default;\n$btn-border-radius-sm:        var(--#{$prefix}border-radius-sm) !default;\n$btn-border-radius-lg:        var(--#{$prefix}border-radius-lg) !default;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$btn-hover-bg-shade-amount:       15% !default;\n$btn-hover-bg-tint-amount:        15% !default;\n$btn-hover-border-shade-amount:   20% !default;\n$btn-hover-border-tint-amount:    10% !default;\n$btn-active-bg-shade-amount:      20% !default;\n$btn-active-bg-tint-amount:       20% !default;\n$btn-active-border-shade-amount:  25% !default;\n$btn-active-border-tint-amount:   10% !default;\n// scss-docs-end btn-variables\n\n\n// Forms\n\n// scss-docs-start form-text-variables\n$form-text-margin-top:                  .25rem !default;\n$form-text-font-size:                   $small-font-size !default;\n$form-text-font-style:                  null !default;\n$form-text-font-weight:                 null !default;\n$form-text-color:                       var(--#{$prefix}secondary-color) !default;\n// scss-docs-end form-text-variables\n\n// scss-docs-start form-label-variables\n$form-label-margin-bottom:              .5rem !default;\n$form-label-font-size:                  null !default;\n$form-label-font-style:                 null !default;\n$form-label-font-weight:                null !default;\n$form-label-color:                      null !default;\n// scss-docs-end form-label-variables\n\n// scss-docs-start form-input-variables\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $input-btn-padding-x !default;\n$input-font-family:                     $input-btn-font-family !default;\n$input-font-size:                       $input-btn-font-size !default;\n$input-font-weight:                     $font-weight-base !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-font-size-sm:                    $input-btn-font-size-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-font-size-lg:                    $input-btn-font-size-lg !default;\n\n$input-bg:                              var(--#{$prefix}body-bg) !default;\n$input-disabled-color:                  null !default;\n$input-disabled-bg:                     var(--#{$prefix}secondary-bg) !default;\n$input-disabled-border-color:           null !default;\n\n$input-color:                           var(--#{$prefix}body-color) !default;\n$input-border-color:                    var(--#{$prefix}border-color) !default;\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      var(--#{$prefix}box-shadow-inset) !default;\n\n$input-border-radius:                   var(--#{$prefix}border-radius) !default;\n$input-border-radius-sm:                var(--#{$prefix}border-radius-sm) !default;\n$input-border-radius-lg:                var(--#{$prefix}border-radius-lg) !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              tint-color($component-active-bg, 50%) !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     $input-btn-focus-width !default;\n$input-focus-box-shadow:                $input-btn-focus-box-shadow !default;\n\n$input-placeholder-color:               var(--#{$prefix}secondary-color) !default;\n$input-plaintext-color:                 var(--#{$prefix}body-color) !default;\n\n$input-height-border:                   calc(#{$input-border-width} * 2) !default; // stylelint-disable-line function-disallowed-list\n\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2) !default;\n$input-height-inner-half:               add($input-line-height * .5em, $input-padding-y) !default;\n$input-height-inner-quarter:            add($input-line-height * .25em, $input-padding-y * .5) !default;\n\n$input-height:                          add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false)) !default;\n$input-height-sm:                       add($input-line-height * 1em, add($input-padding-y-sm * 2, $input-height-border, false)) !default;\n$input-height-lg:                       add($input-line-height * 1em, add($input-padding-y-lg * 2, $input-height-border, false)) !default;\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$form-color-width:                      3rem !default;\n// scss-docs-end form-input-variables\n\n// scss-docs-start form-check-variables\n$form-check-input-width:                  1em !default;\n$form-check-min-height:                   $font-size-base * $line-height-base !default;\n$form-check-padding-start:                $form-check-input-width + .5em !default;\n$form-check-margin-bottom:                .125rem !default;\n$form-check-label-color:                  null !default;\n$form-check-label-cursor:                 null !default;\n$form-check-transition:                   null !default;\n\n$form-check-input-active-filter:          brightness(90%) !default;\n\n$form-check-input-bg:                     $input-bg !default;\n$form-check-input-border:                 var(--#{$prefix}border-width) solid var(--#{$prefix}border-color) !default;\n$form-check-input-border-radius:          .25em !default;\n$form-check-radio-border-radius:          50% !default;\n$form-check-input-focus-border:           $input-focus-border-color !default;\n$form-check-input-focus-box-shadow:       $focus-ring-box-shadow !default;\n\n$form-check-input-checked-color:          $component-active-color !default;\n$form-check-input-checked-bg-color:       $component-active-bg !default;\n$form-check-input-checked-border-color:   $form-check-input-checked-bg-color !default;\n$form-check-input-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-checked-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/></svg>\") !default;\n$form-check-radio-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$form-check-input-checked-color}'/></svg>\") !default;\n\n$form-check-input-indeterminate-color:          $component-active-color !default;\n$form-check-input-indeterminate-bg-color:       $component-active-bg !default;\n$form-check-input-indeterminate-border-color:   $form-check-input-indeterminate-bg-color !default;\n$form-check-input-indeterminate-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-indeterminate-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/></svg>\") !default;\n\n$form-check-input-disabled-opacity:        .5 !default;\n$form-check-label-disabled-opacity:        $form-check-input-disabled-opacity !default;\n$form-check-btn-check-disabled-opacity:    $btn-disabled-opacity !default;\n\n$form-check-inline-margin-end:    1rem !default;\n// scss-docs-end form-check-variables\n\n// scss-docs-start form-switch-variables\n$form-switch-color:               rgba($black, .25) !default;\n$form-switch-width:               2em !default;\n$form-switch-padding-start:       $form-switch-width + .5em !default;\n$form-switch-bg-image:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color}'/></svg>\") !default;\n$form-switch-border-radius:       $form-switch-width !default;\n$form-switch-transition:          background-position .15s ease-in-out !default;\n\n$form-switch-focus-color:         $input-focus-border-color !default;\n$form-switch-focus-bg-image:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-focus-color}'/></svg>\") !default;\n\n$form-switch-checked-color:       $component-active-color !default;\n$form-switch-checked-bg-image:    url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-checked-color}'/></svg>\") !default;\n$form-switch-checked-bg-position: right center !default;\n// scss-docs-end form-switch-variables\n\n// scss-docs-start input-group-variables\n$input-group-addon-padding-y:           $input-padding-y !default;\n$input-group-addon-padding-x:           $input-padding-x !default;\n$input-group-addon-font-weight:         $input-font-weight !default;\n$input-group-addon-color:               $input-color !default;\n$input-group-addon-bg:                  var(--#{$prefix}tertiary-bg) !default;\n$input-group-addon-border-color:        $input-border-color !default;\n// scss-docs-end input-group-variables\n\n// scss-docs-start form-select-variables\n$form-select-padding-y:             $input-padding-y !default;\n$form-select-padding-x:             $input-padding-x !default;\n$form-select-font-family:           $input-font-family !default;\n$form-select-font-size:             $input-font-size !default;\n$form-select-indicator-padding:     $form-select-padding-x * 3 !default; // Extra padding for background-image\n$form-select-font-weight:           $input-font-weight !default;\n$form-select-line-height:           $input-line-height !default;\n$form-select-color:                 $input-color !default;\n$form-select-bg:                    $input-bg !default;\n$form-select-disabled-color:        null !default;\n$form-select-disabled-bg:           $input-disabled-bg !default;\n$form-select-disabled-border-color: $input-disabled-border-color !default;\n$form-select-bg-position:           right $form-select-padding-x center !default;\n$form-select-bg-size:               16px 12px !default; // In pixels because image dimensions\n$form-select-indicator-color:       $gray-800 !default;\n$form-select-indicator:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/></svg>\") !default;\n\n$form-select-feedback-icon-padding-end: $form-select-padding-x * 2.5 + $form-select-indicator-padding !default;\n$form-select-feedback-icon-position:    center right $form-select-indicator-padding !default;\n$form-select-feedback-icon-size:        $input-height-inner-half $input-height-inner-half !default;\n\n$form-select-border-width:        $input-border-width !default;\n$form-select-border-color:        $input-border-color !default;\n$form-select-border-radius:       $input-border-radius !default;\n$form-select-box-shadow:          var(--#{$prefix}box-shadow-inset) !default;\n\n$form-select-focus-border-color:  $input-focus-border-color !default;\n$form-select-focus-width:         $input-focus-width !default;\n$form-select-focus-box-shadow:    0 0 0 $form-select-focus-width $input-btn-focus-color !default;\n\n$form-select-padding-y-sm:        $input-padding-y-sm !default;\n$form-select-padding-x-sm:        $input-padding-x-sm !default;\n$form-select-font-size-sm:        $input-font-size-sm !default;\n$form-select-border-radius-sm:    $input-border-radius-sm !default;\n\n$form-select-padding-y-lg:        $input-padding-y-lg !default;\n$form-select-padding-x-lg:        $input-padding-x-lg !default;\n$form-select-font-size-lg:        $input-font-size-lg !default;\n$form-select-border-radius-lg:    $input-border-radius-lg !default;\n\n$form-select-transition:          $input-transition !default;\n// scss-docs-end form-select-variables\n\n// scss-docs-start form-range-variables\n$form-range-track-width:          100% !default;\n$form-range-track-height:         .5rem !default;\n$form-range-track-cursor:         pointer !default;\n$form-range-track-bg:             var(--#{$prefix}secondary-bg) !default;\n$form-range-track-border-radius:  1rem !default;\n$form-range-track-box-shadow:     var(--#{$prefix}box-shadow-inset) !default;\n\n$form-range-thumb-width:                   1rem !default;\n$form-range-thumb-height:                  $form-range-thumb-width !default;\n$form-range-thumb-bg:                      $component-active-bg !default;\n$form-range-thumb-border:                  0 !default;\n$form-range-thumb-border-radius:           1rem !default;\n$form-range-thumb-box-shadow:              0 .1rem .25rem rgba($black, .1) !default;\n$form-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-focus-box-shadow !default;\n$form-range-thumb-focus-box-shadow-width:  $input-focus-width !default; // For focus box shadow issue in Edge\n$form-range-thumb-active-bg:               tint-color($component-active-bg, 70%) !default;\n$form-range-thumb-disabled-bg:             var(--#{$prefix}secondary-color) !default;\n$form-range-thumb-transition:              background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n// scss-docs-end form-range-variables\n\n// scss-docs-start form-file-variables\n$form-file-button-color:          $input-color !default;\n$form-file-button-bg:             var(--#{$prefix}tertiary-bg) !default;\n$form-file-button-hover-bg:       var(--#{$prefix}secondary-bg) !default;\n// scss-docs-end form-file-variables\n\n// scss-docs-start form-floating-variables\n$form-floating-height:                  add(3.5rem, $input-height-border) !default;\n$form-floating-line-height:             1.25 !default;\n$form-floating-padding-x:               $input-padding-x !default;\n$form-floating-padding-y:               1rem !default;\n$form-floating-input-padding-t:         1.625rem !default;\n$form-floating-input-padding-b:         .625rem !default;\n$form-floating-label-height:            1.5em !default;\n$form-floating-label-opacity:           .65 !default;\n$form-floating-label-transform:         scale(.85) translateY(-.5rem) translateX(.15rem) !default;\n$form-floating-label-disabled-color:    $gray-600 !default;\n$form-floating-transition:              opacity .1s ease-in-out, transform .1s ease-in-out !default;\n// scss-docs-end form-floating-variables\n\n// Form validation\n\n// scss-docs-start form-feedback-variables\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $form-text-font-size !default;\n$form-feedback-font-style:          $form-text-font-style !default;\n$form-feedback-valid-color:         $success !default;\n$form-feedback-invalid-color:       $danger !default;\n\n$form-feedback-icon-valid-color:    $form-feedback-valid-color !default;\n$form-feedback-icon-valid:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\") !default;\n$form-feedback-icon-invalid-color:  $form-feedback-invalid-color !default;\n$form-feedback-icon-invalid:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\") !default;\n// scss-docs-end form-feedback-variables\n\n// scss-docs-start form-validation-colors\n$form-valid-color:                  $form-feedback-valid-color !default;\n$form-valid-border-color:           $form-feedback-valid-color !default;\n$form-invalid-color:                $form-feedback-invalid-color !default;\n$form-invalid-border-color:         $form-feedback-invalid-color !default;\n// scss-docs-end form-validation-colors\n\n// scss-docs-start form-validation-states\n$form-validation-states: (\n  \"valid\": (\n    \"color\": var(--#{$prefix}form-valid-color),\n    \"icon\": $form-feedback-icon-valid,\n    \"tooltip-color\": #fff,\n    \"tooltip-bg-color\": var(--#{$prefix}success),\n    \"focus-box-shadow\": 0 0 $input-btn-focus-blur $input-focus-width rgba(var(--#{$prefix}success-rgb), $input-btn-focus-color-opacity),\n    \"border-color\": var(--#{$prefix}form-valid-border-color),\n  ),\n  \"invalid\": (\n    \"color\": var(--#{$prefix}form-invalid-color),\n    \"icon\": $form-feedback-icon-invalid,\n    \"tooltip-color\": #fff,\n    \"tooltip-bg-color\": var(--#{$prefix}danger),\n    \"focus-box-shadow\": 0 0 $input-btn-focus-blur $input-focus-width rgba(var(--#{$prefix}danger-rgb), $input-btn-focus-color-opacity),\n    \"border-color\": var(--#{$prefix}form-invalid-border-color),\n  )\n) !default;\n// scss-docs-end form-validation-states\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n// scss-docs-start zindex-stack\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-offcanvas-backdrop:         1040 !default;\n$zindex-offcanvas:                  1045 !default;\n$zindex-modal-backdrop:             1050 !default;\n$zindex-modal:                      1055 !default;\n$zindex-popover:                    1070 !default;\n$zindex-tooltip:                    1080 !default;\n$zindex-toast:                      1090 !default;\n// scss-docs-end zindex-stack\n\n// scss-docs-start zindex-levels-map\n$zindex-levels: (\n  n1: -1,\n  0: 0,\n  1: 1,\n  2: 2,\n  3: 3\n) !default;\n// scss-docs-end zindex-levels-map\n\n\n// Navs\n\n// scss-docs-start nav-variables\n$nav-link-padding-y:                .5rem !default;\n$nav-link-padding-x:                1rem !default;\n$nav-link-font-size:                null !default;\n$nav-link-font-weight:              null !default;\n$nav-link-color:                    var(--#{$prefix}link-color) !default;\n$nav-link-hover-color:              var(--#{$prefix}link-hover-color) !default;\n$nav-link-transition:               color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out !default;\n$nav-link-disabled-color:           var(--#{$prefix}secondary-color) !default;\n$nav-link-focus-box-shadow:         $focus-ring-box-shadow !default;\n\n$nav-tabs-border-color:             var(--#{$prefix}border-color) !default;\n$nav-tabs-border-width:             var(--#{$prefix}border-width) !default;\n$nav-tabs-border-radius:            var(--#{$prefix}border-radius) !default;\n$nav-tabs-link-hover-border-color:  var(--#{$prefix}secondary-bg) var(--#{$prefix}secondary-bg) $nav-tabs-border-color !default;\n$nav-tabs-link-active-color:        var(--#{$prefix}emphasis-color) !default;\n$nav-tabs-link-active-bg:           var(--#{$prefix}body-bg) !default;\n$nav-tabs-link-active-border-color: var(--#{$prefix}border-color) var(--#{$prefix}border-color) $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius:           var(--#{$prefix}border-radius) !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n\n$nav-underline-gap:                 1rem !default;\n$nav-underline-border-width:        .125rem !default;\n$nav-underline-link-active-color:   var(--#{$prefix}emphasis-color) !default;\n// scss-docs-end nav-variables\n\n\n// Navbar\n\n// scss-docs-start navbar-variables\n$navbar-padding-y:                  $spacer * .5 !default;\n$navbar-padding-x:                  null !default;\n\n$navbar-nav-link-padding-x:         .5rem !default;\n\n$navbar-brand-font-size:            $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   $font-size-base * $line-height-base + $nav-link-padding-y * 2 !default;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) * .5 !default;\n$navbar-brand-margin-end:           1rem !default;\n\n$navbar-toggler-padding-y:          .25rem !default;\n$navbar-toggler-padding-x:          .75rem !default;\n$navbar-toggler-font-size:          $font-size-lg !default;\n$navbar-toggler-border-radius:      $btn-border-radius !default;\n$navbar-toggler-focus-width:        $btn-focus-width !default;\n$navbar-toggler-transition:         box-shadow .15s ease-in-out !default;\n\n$navbar-light-color:                rgba(var(--#{$prefix}emphasis-color-rgb), .65) !default;\n$navbar-light-hover-color:          rgba(var(--#{$prefix}emphasis-color-rgb), .8) !default;\n$navbar-light-active-color:         rgba(var(--#{$prefix}emphasis-color-rgb), 1) !default;\n$navbar-light-disabled-color:       rgba(var(--#{$prefix}emphasis-color-rgb), .3) !default;\n$navbar-light-icon-color:           rgba($body-color, .75) !default;\n$navbar-light-toggler-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-light-icon-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-light-toggler-border-color: rgba(var(--#{$prefix}emphasis-color-rgb), .15) !default;\n$navbar-light-brand-color:          $navbar-light-active-color !default;\n$navbar-light-brand-hover-color:    $navbar-light-active-color !default;\n// scss-docs-end navbar-variables\n\n// scss-docs-start navbar-dark-variables\n$navbar-dark-color:                 rgba($white, .55) !default;\n$navbar-dark-hover-color:           rgba($white, .75) !default;\n$navbar-dark-active-color:          $white !default;\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\n$navbar-dark-icon-color:            $navbar-dark-color !default;\n$navbar-dark-toggler-icon-bg:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-icon-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\n$navbar-dark-brand-color:           $navbar-dark-active-color !default;\n$navbar-dark-brand-hover-color:     $navbar-dark-active-color !default;\n// scss-docs-end navbar-dark-variables\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n// scss-docs-start dropdown-variables\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-x:                0 !default;\n$dropdown-padding-y:                .5rem !default;\n$dropdown-spacer:                   .125rem !default;\n$dropdown-font-size:                $font-size-base !default;\n$dropdown-color:                    var(--#{$prefix}body-color) !default;\n$dropdown-bg:                       var(--#{$prefix}body-bg) !default;\n$dropdown-border-color:             var(--#{$prefix}border-color-translucent) !default;\n$dropdown-border-radius:            var(--#{$prefix}border-radius) !default;\n$dropdown-border-width:             var(--#{$prefix}border-width) !default;\n$dropdown-inner-border-radius:      calc(#{$dropdown-border-radius} - #{$dropdown-border-width}) !default; // stylelint-disable-line function-disallowed-list\n$dropdown-divider-bg:               $dropdown-border-color !default;\n$dropdown-divider-margin-y:         $spacer * .5 !default;\n$dropdown-box-shadow:               var(--#{$prefix}box-shadow) !default;\n\n$dropdown-link-color:               var(--#{$prefix}body-color) !default;\n$dropdown-link-hover-color:         $dropdown-link-color !default;\n$dropdown-link-hover-bg:            var(--#{$prefix}tertiary-bg) !default;\n\n$dropdown-link-active-color:        $component-active-color !default;\n$dropdown-link-active-bg:           $component-active-bg !default;\n\n$dropdown-link-disabled-color:      var(--#{$prefix}tertiary-color) !default;\n\n$dropdown-item-padding-y:           $spacer * .25 !default;\n$dropdown-item-padding-x:           $spacer !default;\n\n$dropdown-header-color:             $gray-600 !default;\n$dropdown-header-padding-x:         $dropdown-item-padding-x !default;\n$dropdown-header-padding-y:         $dropdown-padding-y !default;\n// fusv-disable\n$dropdown-header-padding:           $dropdown-header-padding-y $dropdown-header-padding-x !default; // Deprecated in v5.2.0\n// fusv-enable\n// scss-docs-end dropdown-variables\n\n// scss-docs-start dropdown-dark-variables\n$dropdown-dark-color:               $gray-300 !default;\n$dropdown-dark-bg:                  $gray-800 !default;\n$dropdown-dark-border-color:        $dropdown-border-color !default;\n$dropdown-dark-divider-bg:          $dropdown-divider-bg !default;\n$dropdown-dark-box-shadow:          null !default;\n$dropdown-dark-link-color:          $dropdown-dark-color !default;\n$dropdown-dark-link-hover-color:    $white !default;\n$dropdown-dark-link-hover-bg:       rgba($white, .15) !default;\n$dropdown-dark-link-active-color:   $dropdown-link-active-color !default;\n$dropdown-dark-link-active-bg:      $dropdown-link-active-bg !default;\n$dropdown-dark-link-disabled-color: $gray-500 !default;\n$dropdown-dark-header-color:        $gray-500 !default;\n// scss-docs-end dropdown-dark-variables\n\n\n// Pagination\n\n// scss-docs-start pagination-variables\n$pagination-padding-y:              .375rem !default;\n$pagination-padding-x:              .75rem !default;\n$pagination-padding-y-sm:           .25rem !default;\n$pagination-padding-x-sm:           .5rem !default;\n$pagination-padding-y-lg:           .75rem !default;\n$pagination-padding-x-lg:           1.5rem !default;\n\n$pagination-font-size:              $font-size-base !default;\n\n$pagination-color:                  var(--#{$prefix}link-color) !default;\n$pagination-bg:                     var(--#{$prefix}body-bg) !default;\n$pagination-border-radius:          var(--#{$prefix}border-radius) !default;\n$pagination-border-width:           var(--#{$prefix}border-width) !default;\n$pagination-margin-start:           calc(#{$pagination-border-width} * -1) !default; // stylelint-disable-line function-disallowed-list\n$pagination-border-color:           var(--#{$prefix}border-color) !default;\n\n$pagination-focus-color:            var(--#{$prefix}link-hover-color) !default;\n$pagination-focus-bg:               var(--#{$prefix}secondary-bg) !default;\n$pagination-focus-box-shadow:       $focus-ring-box-shadow !default;\n$pagination-focus-outline:          0 !default;\n\n$pagination-hover-color:            var(--#{$prefix}link-hover-color) !default;\n$pagination-hover-bg:               var(--#{$prefix}tertiary-bg) !default;\n$pagination-hover-border-color:     var(--#{$prefix}border-color) !default; // Todo in v6: remove this?\n\n$pagination-active-color:           $component-active-color !default;\n$pagination-active-bg:              $component-active-bg !default;\n$pagination-active-border-color:    $component-active-bg !default;\n\n$pagination-disabled-color:         var(--#{$prefix}secondary-color) !default;\n$pagination-disabled-bg:            var(--#{$prefix}secondary-bg) !default;\n$pagination-disabled-border-color:  var(--#{$prefix}border-color) !default;\n\n$pagination-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$pagination-border-radius-sm:       var(--#{$prefix}border-radius-sm) !default;\n$pagination-border-radius-lg:       var(--#{$prefix}border-radius-lg) !default;\n// scss-docs-end pagination-variables\n\n\n// Placeholders\n\n// scss-docs-start placeholders\n$placeholder-opacity-max:           .5 !default;\n$placeholder-opacity-min:           .2 !default;\n// scss-docs-end placeholders\n\n// Cards\n\n// scss-docs-start card-variables\n$card-spacer-y:                     $spacer !default;\n$card-spacer-x:                     $spacer !default;\n$card-title-spacer-y:               $spacer * .5 !default;\n$card-title-color:                  null !default;\n$card-subtitle-color:               null !default;\n$card-border-width:                 var(--#{$prefix}border-width) !default;\n$card-border-color:                 var(--#{$prefix}border-color-translucent) !default;\n$card-border-radius:                var(--#{$prefix}border-radius) !default;\n$card-box-shadow:                   null !default;\n$card-inner-border-radius:          subtract($card-border-radius, $card-border-width) !default;\n$card-cap-padding-y:                $card-spacer-y * .5 !default;\n$card-cap-padding-x:                $card-spacer-x !default;\n$card-cap-bg:                       rgba(var(--#{$prefix}body-color-rgb), .03) !default;\n$card-cap-color:                    null !default;\n$card-height:                       null !default;\n$card-color:                        null !default;\n$card-bg:                           var(--#{$prefix}body-bg) !default;\n$card-img-overlay-padding:          $spacer !default;\n$card-group-margin:                 $grid-gutter-width * .5 !default;\n// scss-docs-end card-variables\n\n// Accordion\n\n// scss-docs-start accordion-variables\n$accordion-padding-y:                     1rem !default;\n$accordion-padding-x:                     1.25rem !default;\n$accordion-color:                         var(--#{$prefix}body-color) !default;\n$accordion-bg:                            var(--#{$prefix}body-bg) !default;\n$accordion-border-width:                  var(--#{$prefix}border-width) !default;\n$accordion-border-color:                  var(--#{$prefix}border-color) !default;\n$accordion-border-radius:                 var(--#{$prefix}border-radius) !default;\n$accordion-inner-border-radius:           subtract($accordion-border-radius, $accordion-border-width) !default;\n\n$accordion-body-padding-y:                $accordion-padding-y !default;\n$accordion-body-padding-x:                $accordion-padding-x !default;\n\n$accordion-button-padding-y:              $accordion-padding-y !default;\n$accordion-button-padding-x:              $accordion-padding-x !default;\n$accordion-button-color:                  var(--#{$prefix}body-color) !default;\n$accordion-button-bg:                     var(--#{$prefix}accordion-bg) !default;\n$accordion-transition:                    $btn-transition, border-radius .15s ease !default;\n$accordion-button-active-bg:              var(--#{$prefix}primary-bg-subtle) !default;\n$accordion-button-active-color:           var(--#{$prefix}primary-text-emphasis) !default;\n\n// fusv-disable\n$accordion-button-focus-border-color:     $input-focus-border-color !default; // Deprecated in v5.3.3\n// fusv-enable\n$accordion-button-focus-box-shadow:       $btn-focus-box-shadow !default;\n\n$accordion-icon-width:                    1.25rem !default;\n$accordion-icon-color:                    $body-color !default;\n$accordion-icon-active-color:             $primary-text-emphasis !default;\n$accordion-icon-transition:               transform .2s ease-in-out !default;\n$accordion-icon-transform:                rotate(-180deg) !default;\n\n$accordion-button-icon:         url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='none' stroke='#{$accordion-icon-color}' stroke-linecap='round' stroke-linejoin='round'><path d='M2 5L8 11L14 5'/></svg>\") !default;\n$accordion-button-active-icon:  url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='none' stroke='#{$accordion-icon-active-color}' stroke-linecap='round' stroke-linejoin='round'><path d='M2 5L8 11L14 5'/></svg>\") !default;\n// scss-docs-end accordion-variables\n\n// Tooltips\n\n// scss-docs-start tooltip-variables\n$tooltip-font-size:                 $font-size-sm !default;\n$tooltip-max-width:                 200px !default;\n$tooltip-color:                     var(--#{$prefix}body-bg) !default;\n$tooltip-bg:                        var(--#{$prefix}emphasis-color) !default;\n$tooltip-border-radius:             var(--#{$prefix}border-radius) !default;\n$tooltip-opacity:                   .9 !default;\n$tooltip-padding-y:                 $spacer * .25 !default;\n$tooltip-padding-x:                 $spacer * .5 !default;\n$tooltip-margin:                    null !default; // TODO: remove this in v6\n\n$tooltip-arrow-width:               .8rem !default;\n$tooltip-arrow-height:              .4rem !default;\n// fusv-disable\n$tooltip-arrow-color:               null !default; // Deprecated in Bootstrap 5.2.0 for CSS variables\n// fusv-enable\n// scss-docs-end tooltip-variables\n\n// Form tooltips must come after regular tooltips\n// scss-docs-start tooltip-feedback-variables\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\n$form-feedback-tooltip-line-height:   null !default;\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n// scss-docs-end tooltip-feedback-variables\n\n\n// Popovers\n\n// scss-docs-start popover-variables\n$popover-font-size:                 $font-size-sm !default;\n$popover-bg:                        var(--#{$prefix}body-bg) !default;\n$popover-max-width:                 276px !default;\n$popover-border-width:              var(--#{$prefix}border-width) !default;\n$popover-border-color:              var(--#{$prefix}border-color-translucent) !default;\n$popover-border-radius:             var(--#{$prefix}border-radius-lg) !default;\n$popover-inner-border-radius:       calc(#{$popover-border-radius} - #{$popover-border-width}) !default; // stylelint-disable-line function-disallowed-list\n$popover-box-shadow:                var(--#{$prefix}box-shadow) !default;\n\n$popover-header-font-size:          $font-size-base !default;\n$popover-header-bg:                 var(--#{$prefix}secondary-bg) !default;\n$popover-header-color:              $headings-color !default;\n$popover-header-padding-y:          .5rem !default;\n$popover-header-padding-x:          $spacer !default;\n\n$popover-body-color:                var(--#{$prefix}body-color) !default;\n$popover-body-padding-y:            $spacer !default;\n$popover-body-padding-x:            $spacer !default;\n\n$popover-arrow-width:               1rem !default;\n$popover-arrow-height:              .5rem !default;\n// scss-docs-end popover-variables\n\n// fusv-disable\n// Deprecated in Bootstrap 5.2.0 for CSS variables\n$popover-arrow-color:               $popover-bg !default;\n$popover-arrow-outer-color:         var(--#{$prefix}border-color-translucent) !default;\n// fusv-enable\n\n\n// Toasts\n\n// scss-docs-start toast-variables\n$toast-max-width:                   350px !default;\n$toast-padding-x:                   .75rem !default;\n$toast-padding-y:                   .5rem !default;\n$toast-font-size:                   .875rem !default;\n$toast-color:                       null !default;\n$toast-background-color:            rgba(var(--#{$prefix}body-bg-rgb), .85) !default;\n$toast-border-width:                var(--#{$prefix}border-width) !default;\n$toast-border-color:                var(--#{$prefix}border-color-translucent) !default;\n$toast-border-radius:               var(--#{$prefix}border-radius) !default;\n$toast-box-shadow:                  var(--#{$prefix}box-shadow) !default;\n$toast-spacing:                     $container-padding-x !default;\n\n$toast-header-color:                var(--#{$prefix}secondary-color) !default;\n$toast-header-background-color:     rgba(var(--#{$prefix}body-bg-rgb), .85) !default;\n$toast-header-border-color:         $toast-border-color !default;\n// scss-docs-end toast-variables\n\n\n// Badges\n\n// scss-docs-start badge-variables\n$badge-font-size:                   .75em !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-color:                       $white !default;\n$badge-padding-y:                   .35em !default;\n$badge-padding-x:                   .65em !default;\n$badge-border-radius:               var(--#{$prefix}border-radius) !default;\n// scss-docs-end badge-variables\n\n\n// Modals\n\n// scss-docs-start modal-variables\n$modal-inner-padding:               $spacer !default;\n\n$modal-footer-margin-between:       .5rem !default;\n\n$modal-dialog-margin:               .5rem !default;\n$modal-dialog-margin-y-sm-up:       1.75rem !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-color:               null !default;\n$modal-content-bg:                  var(--#{$prefix}body-bg) !default;\n$modal-content-border-color:        var(--#{$prefix}border-color-translucent) !default;\n$modal-content-border-width:        var(--#{$prefix}border-width) !default;\n$modal-content-border-radius:       var(--#{$prefix}border-radius-lg) !default;\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width) !default;\n$modal-content-box-shadow-xs:       var(--#{$prefix}box-shadow-sm) !default;\n$modal-content-box-shadow-sm-up:    var(--#{$prefix}box-shadow) !default;\n\n$modal-backdrop-bg:                 $black !default;\n$modal-backdrop-opacity:            .5 !default;\n\n$modal-header-border-color:         var(--#{$prefix}border-color) !default;\n$modal-header-border-width:         $modal-content-border-width !default;\n$modal-header-padding-y:            $modal-inner-padding !default;\n$modal-header-padding-x:            $modal-inner-padding !default;\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\n\n$modal-footer-bg:                   null !default;\n$modal-footer-border-color:         $modal-header-border-color !default;\n$modal-footer-border-width:         $modal-header-border-width !default;\n\n$modal-sm:                          300px !default;\n$modal-md:                          500px !default;\n$modal-lg:                          800px !default;\n$modal-xl:                          1140px !default;\n\n$modal-fade-transform:              translate(0, -50px) !default;\n$modal-show-transform:              none !default;\n$modal-transition:                  transform .3s ease-out !default;\n$modal-scale-transform:             scale(1.02) !default;\n// scss-docs-end modal-variables\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n// scss-docs-start alert-variables\n$alert-padding-y:               $spacer !default;\n$alert-padding-x:               $spacer !default;\n$alert-margin-bottom:           1rem !default;\n$alert-border-radius:           var(--#{$prefix}border-radius) !default;\n$alert-link-font-weight:        $font-weight-bold !default;\n$alert-border-width:            var(--#{$prefix}border-width) !default;\n$alert-dismissible-padding-r:   $alert-padding-x * 3 !default; // 3x covers width of x plus default padding on either side\n// scss-docs-end alert-variables\n\n// fusv-disable\n$alert-bg-scale:                -80% !default; // Deprecated in v5.2.0, to be removed in v6\n$alert-border-scale:            -70% !default; // Deprecated in v5.2.0, to be removed in v6\n$alert-color-scale:             40% !default; // Deprecated in v5.2.0, to be removed in v6\n// fusv-enable\n\n// Progress bars\n\n// scss-docs-start progress-variables\n$progress-height:                   1rem !default;\n$progress-font-size:                $font-size-base * .75 !default;\n$progress-bg:                       var(--#{$prefix}secondary-bg) !default;\n$progress-border-radius:            var(--#{$prefix}border-radius) !default;\n$progress-box-shadow:               var(--#{$prefix}box-shadow-inset) !default;\n$progress-bar-color:                $white !default;\n$progress-bar-bg:                   $primary !default;\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n// scss-docs-end progress-variables\n\n\n// List group\n\n// scss-docs-start list-group-variables\n$list-group-color:                  var(--#{$prefix}body-color) !default;\n$list-group-bg:                     var(--#{$prefix}body-bg) !default;\n$list-group-border-color:           var(--#{$prefix}border-color) !default;\n$list-group-border-width:           var(--#{$prefix}border-width) !default;\n$list-group-border-radius:          var(--#{$prefix}border-radius) !default;\n\n$list-group-item-padding-y:         $spacer * .5 !default;\n$list-group-item-padding-x:         $spacer !default;\n// fusv-disable\n$list-group-item-bg-scale:          -80% !default; // Deprecated in v5.3.0\n$list-group-item-color-scale:       40% !default; // Deprecated in v5.3.0\n// fusv-enable\n\n$list-group-hover-bg:               var(--#{$prefix}tertiary-bg) !default;\n$list-group-active-color:           $component-active-color !default;\n$list-group-active-bg:              $component-active-bg !default;\n$list-group-active-border-color:    $list-group-active-bg !default;\n\n$list-group-disabled-color:         var(--#{$prefix}secondary-color) !default;\n$list-group-disabled-bg:            $list-group-bg !default;\n\n$list-group-action-color:           var(--#{$prefix}secondary-color) !default;\n$list-group-action-hover-color:     var(--#{$prefix}emphasis-color) !default;\n\n$list-group-action-active-color:    var(--#{$prefix}body-color) !default;\n$list-group-action-active-bg:       var(--#{$prefix}secondary-bg) !default;\n// scss-docs-end list-group-variables\n\n\n// Image thumbnails\n\n// scss-docs-start thumbnail-variables\n$thumbnail-padding:                 .25rem !default;\n$thumbnail-bg:                      var(--#{$prefix}body-bg) !default;\n$thumbnail-border-width:            var(--#{$prefix}border-width) !default;\n$thumbnail-border-color:            var(--#{$prefix}border-color) !default;\n$thumbnail-border-radius:           var(--#{$prefix}border-radius) !default;\n$thumbnail-box-shadow:              var(--#{$prefix}box-shadow-sm) !default;\n// scss-docs-end thumbnail-variables\n\n\n// Figures\n\n// scss-docs-start figure-variables\n$figure-caption-font-size:          $small-font-size !default;\n$figure-caption-color:              var(--#{$prefix}secondary-color) !default;\n// scss-docs-end figure-variables\n\n\n// Breadcrumbs\n\n// scss-docs-start breadcrumb-variables\n$breadcrumb-font-size:              null !default;\n$breadcrumb-padding-y:              0 !default;\n$breadcrumb-padding-x:              0 !default;\n$breadcrumb-item-padding-x:         .5rem !default;\n$breadcrumb-margin-bottom:          1rem !default;\n$breadcrumb-bg:                     null !default;\n$breadcrumb-divider-color:          var(--#{$prefix}secondary-color) !default;\n$breadcrumb-active-color:           var(--#{$prefix}secondary-color) !default;\n$breadcrumb-divider:                quote(\"/\") !default;\n$breadcrumb-divider-flipped:        $breadcrumb-divider !default;\n$breadcrumb-border-radius:          null !default;\n// scss-docs-end breadcrumb-variables\n\n// Carousel\n\n// scss-docs-start carousel-variables\n$carousel-control-color:             $white !default;\n$carousel-control-width:             15% !default;\n$carousel-control-opacity:           .5 !default;\n$carousel-control-hover-opacity:     .9 !default;\n$carousel-control-transition:        opacity .15s ease !default;\n\n$carousel-indicator-width:           30px !default;\n$carousel-indicator-height:          3px !default;\n$carousel-indicator-hit-area-height: 10px !default;\n$carousel-indicator-spacer:          3px !default;\n$carousel-indicator-opacity:         .5 !default;\n$carousel-indicator-active-bg:       $white !default;\n$carousel-indicator-active-opacity:  1 !default;\n$carousel-indicator-transition:      opacity .6s ease !default;\n\n$carousel-caption-width:             70% !default;\n$carousel-caption-color:             $white !default;\n$carousel-caption-padding-y:         1.25rem !default;\n$carousel-caption-spacer:            1.25rem !default;\n\n$carousel-control-icon-width:        2rem !default;\n\n$carousel-control-prev-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/></svg>\") !default;\n$carousel-control-next-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n\n$carousel-transition-duration:       .6s !default;\n$carousel-transition:                transform $carousel-transition-duration ease-in-out !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n// scss-docs-end carousel-variables\n\n// scss-docs-start carousel-dark-variables\n$carousel-dark-indicator-active-bg:  $black !default;\n$carousel-dark-caption-color:        $black !default;\n$carousel-dark-control-icon-filter:  invert(1) grayscale(100) !default;\n// scss-docs-end carousel-dark-variables\n\n\n// Spinners\n\n// scss-docs-start spinner-variables\n$spinner-width:           2rem !default;\n$spinner-height:          $spinner-width !default;\n$spinner-vertical-align:  -.125em !default;\n$spinner-border-width:    .25em !default;\n$spinner-animation-speed: .75s !default;\n\n$spinner-width-sm:        1rem !default;\n$spinner-height-sm:       $spinner-width-sm !default;\n$spinner-border-width-sm: .2em !default;\n// scss-docs-end spinner-variables\n\n\n// Close\n\n// scss-docs-start close-variables\n$btn-close-width:            1em !default;\n$btn-close-height:           $btn-close-width !default;\n$btn-close-padding-x:        .25em !default;\n$btn-close-padding-y:        $btn-close-padding-x !default;\n$btn-close-color:            $black !default;\n$btn-close-bg:               url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$btn-close-color}'><path d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/></svg>\") !default;\n$btn-close-focus-shadow:     $focus-ring-box-shadow !default;\n$btn-close-opacity:          .5 !default;\n$btn-close-hover-opacity:    .75 !default;\n$btn-close-focus-opacity:    1 !default;\n$btn-close-disabled-opacity: .25 !default;\n$btn-close-white-filter:     invert(1) grayscale(100%) brightness(200%) !default;\n// scss-docs-end close-variables\n\n\n// Offcanvas\n\n// scss-docs-start offcanvas-variables\n$offcanvas-padding-y:               $modal-inner-padding !default;\n$offcanvas-padding-x:               $modal-inner-padding !default;\n$offcanvas-horizontal-width:        400px !default;\n$offcanvas-vertical-height:         30vh !default;\n$offcanvas-transition-duration:     .3s !default;\n$offcanvas-border-color:            $modal-content-border-color !default;\n$offcanvas-border-width:            $modal-content-border-width !default;\n$offcanvas-title-line-height:       $modal-title-line-height !default;\n$offcanvas-bg-color:                var(--#{$prefix}body-bg) !default;\n$offcanvas-color:                   var(--#{$prefix}body-color) !default;\n$offcanvas-box-shadow:              $modal-content-box-shadow-xs !default;\n$offcanvas-backdrop-bg:             $modal-backdrop-bg !default;\n$offcanvas-backdrop-opacity:        $modal-backdrop-opacity !default;\n// scss-docs-end offcanvas-variables\n\n// Code\n\n$code-font-size:                    $small-font-size !default;\n$code-color:                        $pink !default;\n\n$kbd-padding-y:                     .1875rem !default;\n$kbd-padding-x:                     .375rem !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         var(--#{$prefix}body-bg) !default;\n$kbd-bg:                            var(--#{$prefix}body-color) !default;\n$nested-kbd-font-weight:            null !default; // Deprecated in v5.2.0, removing in v6\n\n$pre-color:                         null !default;\n\n@import \"variables-dark\"; // TODO: can be removed safely in v6, only here to avoid breaking changes in v5.3\n", "// stylelint-disable property-disallowed-list\n// Single side border-radius\n\n// Helper function to replace negative values with 0\n@function valid-radius($radius) {\n  $return: ();\n  @each $value in $radius {\n    @if type-of($value) == number {\n      $return: append($return, max($value, 0));\n    } @else {\n      $return: append($return, $value);\n    }\n  }\n  @return $return;\n}\n\n// scss-docs-start border-radius-mixins\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\n  @if $enable-rounded {\n    border-radius: valid-radius($radius);\n  }\n  @else if $fallback-border-radius != false {\n    border-radius: $fallback-border-radius;\n  }\n}\n\n@mixin border-top-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-end-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-start-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-start-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-end-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-end-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-start-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n// scss-docs-end border-radius-mixins\n", "// Lists\n\n// Unstyled keeps list items block level, just removes default browser padding and list-style\n@mixin list-unstyled {\n  padding-left: 0;\n  list-style: none;\n}\n", "// Responsive images (ensure images don't scale beyond their parents)\n//\n// This is purposefully opt-in via an explicit class rather than being the default for all `<img>`s.\n// We previously tried the \"images are responsive by default\" approach in Bootstrap v2,\n// and abandoned it in Bootstrap v3 because it breaks lots of third-party widgets (including Google Maps)\n// which weren't expecting the images within themselves to be involuntarily resized.\n// See also https://github.com/twbs/bootstrap/issues/18178\n.img-fluid {\n  @include img-fluid();\n}\n\n\n// Image thumbnails\n.img-thumbnail {\n  padding: $thumbnail-padding;\n  background-color: $thumbnail-bg;\n  border: $thumbnail-border-width solid $thumbnail-border-color;\n  @include border-radius($thumbnail-border-radius);\n  @include box-shadow($thumbnail-box-shadow);\n\n  // Keep them at most 100% wide\n  @include img-fluid();\n}\n\n//\n// Figures\n//\n\n.figure {\n  // Ensures the caption's text aligns with the image.\n  display: inline-block;\n}\n\n.figure-img {\n  margin-bottom: $spacer * .5;\n  line-height: 1;\n}\n\n.figure-caption {\n  @include font-size($figure-caption-font-size);\n  color: $figure-caption-color;\n}\n", "// Image Mixins\n// - Responsive image\n// - Retina image\n\n\n// Responsive image\n//\n// Keep images from scaling beyond the width of their parents.\n\n@mixin img-fluid {\n  // Part 1: Set a maximum relative to the parent\n  max-width: 100%;\n  // Part 2: Override the height to auto, otherwise images will be stretched\n  // when setting a width and height attribute on the img element.\n  height: auto;\n}\n", "// Container widths\n//\n// Set the container width, and override it for fixed navbars in media queries.\n\n@if $enable-container-classes {\n  // Single container class with breakpoint max-widths\n  .container,\n  // 100% wide container at all breakpoints\n  .container-fluid {\n    @include make-container();\n  }\n\n  // Responsive containers that are 100% wide until a breakpoint\n  @each $breakpoint, $container-max-width in $container-max-widths {\n    .container-#{$breakpoint} {\n      @extend .container-fluid;\n    }\n\n    @include media-breakpoint-up($breakpoint, $grid-breakpoints) {\n      %responsive-container-#{$breakpoint} {\n        max-width: $container-max-width;\n      }\n\n      // Extend each breakpoint which is smaller or equal to the current breakpoint\n      $extend-breakpoint: true;\n\n      @each $name, $width in $grid-breakpoints {\n        @if ($extend-breakpoint) {\n          .container#{breakpoint-infix($name, $grid-breakpoints)} {\n            @extend %responsive-container-#{$breakpoint};\n          }\n\n          // Once the current breakpoint is reached, stop extending\n          @if ($breakpoint == $name) {\n            $extend-breakpoint: false;\n          }\n        }\n      }\n    }\n  }\n}\n", "// Container mixins\n\n@mixin make-container($gutter: $container-padding-x) {\n  --#{$prefix}gutter-x: #{$gutter};\n  --#{$prefix}gutter-y: 0;\n  width: 100%;\n  padding-right: calc(var(--#{$prefix}gutter-x) * .5); // stylelint-disable-line function-disallowed-list\n  padding-left: calc(var(--#{$prefix}gutter-x) * .5); // stylelint-disable-line function-disallowed-list\n  margin-right: auto;\n  margin-left: auto;\n}\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl xxl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @if not $n {\n    @error \"breakpoint `#{$name}` not found in `#{$breakpoints}`\";\n  }\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min:  breakpoint-min($name, $breakpoints);\n  $next: breakpoint-next($name, $breakpoints);\n  $max:  breakpoint-max($next, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($next, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "// Row\n//\n// Rows contain your columns.\n\n:root {\n  @each $name, $value in $grid-breakpoints {\n    --#{$prefix}breakpoint-#{$name}: #{$value};\n  }\n}\n\n@if $enable-grid-classes {\n  .row {\n    @include make-row();\n\n    > * {\n      @include make-col-ready();\n    }\n  }\n}\n\n@if $enable-cssgrid {\n  .grid {\n    display: grid;\n    grid-template-rows: repeat(var(--#{$prefix}rows, 1), 1fr);\n    grid-template-columns: repeat(var(--#{$prefix}columns, #{$grid-columns}), 1fr);\n    gap: var(--#{$prefix}gap, #{$grid-gutter-width});\n\n    @include make-cssgrid();\n  }\n}\n\n\n// Columns\n//\n// Common styles for small and large grid columns\n\n@if $enable-grid-classes {\n  @include make-grid-columns();\n}\n", "// Grid system\n//\n// Generate semantic grid columns with these mixins.\n\n@mixin make-row($gutter: $grid-gutter-width) {\n  --#{$prefix}gutter-x: #{$gutter};\n  --#{$prefix}gutter-y: 0;\n  display: flex;\n  flex-wrap: wrap;\n  // TODO: Revisit calc order after https://github.com/react-bootstrap/react-bootstrap/issues/6039 is fixed\n  margin-top: calc(-1 * var(--#{$prefix}gutter-y)); // stylelint-disable-line function-disallowed-list\n  margin-right: calc(-.5 * var(--#{$prefix}gutter-x)); // stylelint-disable-line function-disallowed-list\n  margin-left: calc(-.5 * var(--#{$prefix}gutter-x)); // stylelint-disable-line function-disallowed-list\n}\n\n@mixin make-col-ready() {\n  // Add box sizing if only the grid is loaded\n  box-sizing: if(variable-exists(include-column-box-sizing) and $include-column-box-sizing, border-box, null);\n  // Prevent columns from becoming too narrow when at smaller grid tiers by\n  // always setting `width: 100%;`. This works because we set the width\n  // later on to override this initial width.\n  flex-shrink: 0;\n  width: 100%;\n  max-width: 100%; // Prevent `.col-auto`, `.col` (& responsive variants) from breaking out the grid\n  padding-right: calc(var(--#{$prefix}gutter-x) * .5); // stylelint-disable-line function-disallowed-list\n  padding-left: calc(var(--#{$prefix}gutter-x) * .5); // stylelint-disable-line function-disallowed-list\n  margin-top: var(--#{$prefix}gutter-y);\n}\n\n@mixin make-col($size: false, $columns: $grid-columns) {\n  @if $size {\n    flex: 0 0 auto;\n    width: percentage(divide($size, $columns));\n\n  } @else {\n    flex: 1 1 0;\n    max-width: 100%;\n  }\n}\n\n@mixin make-col-auto() {\n  flex: 0 0 auto;\n  width: auto;\n}\n\n@mixin make-col-offset($size, $columns: $grid-columns) {\n  $num: divide($size, $columns);\n  margin-left: if($num == 0, 0, percentage($num));\n}\n\n// Row columns\n//\n// Specify on a parent element(e.g., .row) to force immediate children into NN\n// number of columns. Supports wrapping to new lines, but does not do a Masonry\n// style grid.\n@mixin row-cols($count) {\n  > * {\n    flex: 0 0 auto;\n    width: percentage(divide(1, $count));\n  }\n}\n\n// Framework grid generation\n//\n// Used only by Bootstrap to generate the correct number of grid classes given\n// any value of `$grid-columns`.\n\n@mixin make-grid-columns($columns: $grid-columns, $gutter: $grid-gutter-width, $breakpoints: $grid-breakpoints) {\n  @each $breakpoint in map-keys($breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $breakpoints);\n\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      // Provide basic `.col-{bp}` classes for equal-width flexbox columns\n      .col#{$infix} {\n        flex: 1 0 0%; // Flexbugs #4: https://github.com/philipwalton/flexbugs#flexbug-4\n      }\n\n      .row-cols#{$infix}-auto > * {\n        @include make-col-auto();\n      }\n\n      @if $grid-row-columns > 0 {\n        @for $i from 1 through $grid-row-columns {\n          .row-cols#{$infix}-#{$i} {\n            @include row-cols($i);\n          }\n        }\n      }\n\n      .col#{$infix}-auto {\n        @include make-col-auto();\n      }\n\n      @if $columns > 0 {\n        @for $i from 1 through $columns {\n          .col#{$infix}-#{$i} {\n            @include make-col($i, $columns);\n          }\n        }\n\n        // `$columns - 1` because offsetting by the width of an entire row isn't possible\n        @for $i from 0 through ($columns - 1) {\n          @if not ($infix == \"\" and $i == 0) { // Avoid emitting useless .offset-0\n            .offset#{$infix}-#{$i} {\n              @include make-col-offset($i, $columns);\n            }\n          }\n        }\n      }\n\n      // Gutters\n      //\n      // Make use of `.g-*`, `.gx-*` or `.gy-*` utilities to change spacing between the columns.\n      @each $key, $value in $gutters {\n        .g#{$infix}-#{$key},\n        .gx#{$infix}-#{$key} {\n          --#{$prefix}gutter-x: #{$value};\n        }\n\n        .g#{$infix}-#{$key},\n        .gy#{$infix}-#{$key} {\n          --#{$prefix}gutter-y: #{$value};\n        }\n      }\n    }\n  }\n}\n\n@mixin make-cssgrid($columns: $grid-columns, $breakpoints: $grid-breakpoints) {\n  @each $breakpoint in map-keys($breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $breakpoints);\n\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      @if $columns > 0 {\n        @for $i from 1 through $columns {\n          .g-col#{$infix}-#{$i} {\n            grid-column: auto / span $i;\n          }\n        }\n\n        // Start with `1` because `0` is an invalid value.\n        // Ends with `$columns - 1` because offsetting by the width of an entire row isn't possible.\n        @for $i from 1 through ($columns - 1) {\n          .g-start#{$infix}-#{$i} {\n            grid-column-start: $i;\n          }\n        }\n      }\n    }\n  }\n}\n", "//\n// Basic Bootstrap table\n//\n\n.table {\n  // Reset needed for nesting tables\n  --#{$prefix}table-color-type: initial;\n  --#{$prefix}table-bg-type: initial;\n  --#{$prefix}table-color-state: initial;\n  --#{$prefix}table-bg-state: initial;\n  // End of reset\n  --#{$prefix}table-color: #{$table-color};\n  --#{$prefix}table-bg: #{$table-bg};\n  --#{$prefix}table-border-color: #{$table-border-color};\n  --#{$prefix}table-accent-bg: #{$table-accent-bg};\n  --#{$prefix}table-striped-color: #{$table-striped-color};\n  --#{$prefix}table-striped-bg: #{$table-striped-bg};\n  --#{$prefix}table-active-color: #{$table-active-color};\n  --#{$prefix}table-active-bg: #{$table-active-bg};\n  --#{$prefix}table-hover-color: #{$table-hover-color};\n  --#{$prefix}table-hover-bg: #{$table-hover-bg};\n\n  width: 100%;\n  margin-bottom: $spacer;\n  vertical-align: $table-cell-vertical-align;\n  border-color: var(--#{$prefix}table-border-color);\n\n  // Target th & td\n  // We need the child combinator to prevent styles leaking to nested tables which doesn't have a `.table` class.\n  // We use the universal selectors here to simplify the selector (else we would need 6 different selectors).\n  // Another advantage is that this generates less code and makes the selector less specific making it easier to override.\n  // stylelint-disable-next-line selector-max-universal\n  > :not(caption) > * > * {\n    padding: $table-cell-padding-y $table-cell-padding-x;\n    // Following the precept of cascades: https://codepen.io/miriamsuzanne/full/vYNgodb\n    color: var(--#{$prefix}table-color-state, var(--#{$prefix}table-color-type, var(--#{$prefix}table-color)));\n    background-color: var(--#{$prefix}table-bg);\n    border-bottom-width: $table-border-width;\n    box-shadow: inset 0 0 0 9999px var(--#{$prefix}table-bg-state, var(--#{$prefix}table-bg-type, var(--#{$prefix}table-accent-bg)));\n  }\n\n  > tbody {\n    vertical-align: inherit;\n  }\n\n  > thead {\n    vertical-align: bottom;\n  }\n}\n\n.table-group-divider {\n  border-top: calc(#{$table-border-width} * 2) solid $table-group-separator-color; // stylelint-disable-line function-disallowed-list\n}\n\n//\n// Change placement of captions with a class\n//\n\n.caption-top {\n  caption-side: top;\n}\n\n\n//\n// Condensed table w/ half padding\n//\n\n.table-sm {\n  // stylelint-disable-next-line selector-max-universal\n  > :not(caption) > * > * {\n    padding: $table-cell-padding-y-sm $table-cell-padding-x-sm;\n  }\n}\n\n\n// Border versions\n//\n// Add or remove borders all around the table and between all the columns.\n//\n// When borders are added on all sides of the cells, the corners can render odd when\n// these borders do not have the same color or if they are semi-transparent.\n// Therefore we add top and border bottoms to the `tr`s and left and right borders\n// to the `td`s or `th`s\n\n.table-bordered {\n  > :not(caption) > * {\n    border-width: $table-border-width 0;\n\n    // stylelint-disable-next-line selector-max-universal\n    > * {\n      border-width: 0 $table-border-width;\n    }\n  }\n}\n\n.table-borderless {\n  // stylelint-disable-next-line selector-max-universal\n  > :not(caption) > * > * {\n    border-bottom-width: 0;\n  }\n\n  > :not(:first-child) {\n    border-top-width: 0;\n  }\n}\n\n// Zebra-striping\n//\n// Default zebra-stripe styles (alternating gray and transparent backgrounds)\n\n// For rows\n.table-striped {\n  > tbody > tr:nth-of-type(#{$table-striped-order}) > * {\n    --#{$prefix}table-color-type: var(--#{$prefix}table-striped-color);\n    --#{$prefix}table-bg-type: var(--#{$prefix}table-striped-bg);\n  }\n}\n\n// For columns\n.table-striped-columns {\n  > :not(caption) > tr > :nth-child(#{$table-striped-columns-order}) {\n    --#{$prefix}table-color-type: var(--#{$prefix}table-striped-color);\n    --#{$prefix}table-bg-type: var(--#{$prefix}table-striped-bg);\n  }\n}\n\n// Active table\n//\n// The `.table-active` class can be added to highlight rows or cells\n\n.table-active {\n  --#{$prefix}table-color-state: var(--#{$prefix}table-active-color);\n  --#{$prefix}table-bg-state: var(--#{$prefix}table-active-bg);\n}\n\n// Hover effect\n//\n// Placed here since it has to come after the potential zebra striping\n\n.table-hover {\n  > tbody > tr:hover > * {\n    --#{$prefix}table-color-state: var(--#{$prefix}table-hover-color);\n    --#{$prefix}table-bg-state: var(--#{$prefix}table-hover-bg);\n  }\n}\n\n\n// Table variants\n//\n// Table variants set the table cell backgrounds, border colors\n// and the colors of the striped, hovered & active tables\n\n@each $color, $value in $table-variants {\n  @include table-variant($color, $value);\n}\n\n// Responsive tables\n//\n// Generate series of `.table-responsive-*` classes for configuring the screen\n// size of where your table will overflow.\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n  @include media-breakpoint-down($breakpoint) {\n    .table-responsive#{$infix} {\n      overflow-x: auto;\n      -webkit-overflow-scrolling: touch;\n    }\n  }\n}\n", "// scss-docs-start table-variant\n@mixin table-variant($state, $background) {\n  .table-#{$state} {\n    $color: color-contrast(opaque($body-bg, $background));\n    $hover-bg: mix($color, $background, percentage($table-hover-bg-factor));\n    $striped-bg: mix($color, $background, percentage($table-striped-bg-factor));\n    $active-bg: mix($color, $background, percentage($table-active-bg-factor));\n    $table-border-color: mix($color, $background, percentage($table-border-factor));\n\n    --#{$prefix}table-color: #{$color};\n    --#{$prefix}table-bg: #{$background};\n    --#{$prefix}table-border-color: #{$table-border-color};\n    --#{$prefix}table-striped-bg: #{$striped-bg};\n    --#{$prefix}table-striped-color: #{color-contrast($striped-bg)};\n    --#{$prefix}table-active-bg: #{$active-bg};\n    --#{$prefix}table-active-color: #{color-contrast($active-bg)};\n    --#{$prefix}table-hover-bg: #{$hover-bg};\n    --#{$prefix}table-hover-color: #{color-contrast($hover-bg)};\n\n    color: var(--#{$prefix}table-color);\n    border-color: var(--#{$prefix}table-border-color);\n  }\n}\n// scss-docs-end table-variant\n", "//\n// Labels\n//\n\n.form-label {\n  margin-bottom: $form-label-margin-bottom;\n  @include font-size($form-label-font-size);\n  font-style: $form-label-font-style;\n  font-weight: $form-label-font-weight;\n  color: $form-label-color;\n}\n\n// For use with horizontal and inline forms, when you need the label (or legend)\n// text to align with the form controls.\n.col-form-label {\n  padding-top: add($input-padding-y, $input-border-width);\n  padding-bottom: add($input-padding-y, $input-border-width);\n  margin-bottom: 0; // Override the `<legend>` default\n  @include font-size(inherit); // Override the `<legend>` default\n  font-style: $form-label-font-style;\n  font-weight: $form-label-font-weight;\n  line-height: $input-line-height;\n  color: $form-label-color;\n}\n\n.col-form-label-lg {\n  padding-top: add($input-padding-y-lg, $input-border-width);\n  padding-bottom: add($input-padding-y-lg, $input-border-width);\n  @include font-size($input-font-size-lg);\n}\n\n.col-form-label-sm {\n  padding-top: add($input-padding-y-sm, $input-border-width);\n  padding-bottom: add($input-padding-y-sm, $input-border-width);\n  @include font-size($input-font-size-sm);\n}\n", "// Body\n$body-bg: #f8fafc;\n\n// Typography\n$font-family-sans-serif: '<PERSON><PERSON><PERSON>', sans-serif;\n$font-size-base: 0.9rem;\n$line-height-base: 1.6;\n", "//\n// Form text\n//\n\n.form-text {\n  margin-top: $form-text-margin-top;\n  @include font-size($form-text-font-size);\n  font-style: $form-text-font-style;\n  font-weight: $form-text-font-weight;\n  color: $form-text-color;\n}\n", "//\n// General form controls (plus a few specific high-level interventions)\n//\n\n.form-control {\n  display: block;\n  width: 100%;\n  padding: $input-padding-y $input-padding-x;\n  font-family: $input-font-family;\n  @include font-size($input-font-size);\n  font-weight: $input-font-weight;\n  line-height: $input-line-height;\n  color: $input-color;\n  appearance: none; // Fix appearance for date inputs in Safari\n  background-color: $input-bg;\n  background-clip: padding-box;\n  border: $input-border-width solid $input-border-color;\n\n  // Note: This has no effect on <select>s in some browsers, due to the limited stylability of `<select>`s in CSS.\n  @include border-radius($input-border-radius, 0);\n\n  @include box-shadow($input-box-shadow);\n  @include transition($input-transition);\n\n  &[type=\"file\"] {\n    overflow: hidden; // prevent pseudo element button overlap\n\n    &:not(:disabled):not([readonly]) {\n      cursor: pointer;\n    }\n  }\n\n  // Customize the `:focus` state to imitate native WebKit styles.\n  &:focus {\n    color: $input-focus-color;\n    background-color: $input-focus-bg;\n    border-color: $input-focus-border-color;\n    outline: 0;\n    @if $enable-shadows {\n      @include box-shadow($input-box-shadow, $input-focus-box-shadow);\n    } @else {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      box-shadow: $input-focus-box-shadow;\n    }\n  }\n\n  &::-webkit-date-and-time-value {\n    // On Android Chrome, form-control's \"width: 100%\" makes the input width too small\n    // Tested under Android 11 / Chrome 89, Android 12 / Chrome 100, Android 13 / Chrome 109\n    //\n    // On iOS Safari, form-control's \"appearance: none\" + \"width: 100%\" makes the input width too small\n    // Tested under iOS 16.2 / Safari 16.2\n    min-width: 85px; // Seems to be a good minimum safe width\n\n    // Add some height to date inputs on iOS\n    // https://github.com/twbs/bootstrap/issues/23307\n    // TODO: we can remove this workaround once https://bugs.webkit.org/show_bug.cgi?id=198959 is resolved\n    // Multiply line-height by 1em if it has no unit\n    height: if(unit($input-line-height) == \"\", $input-line-height * 1em, $input-line-height);\n\n    // Android Chrome type=\"date\" is taller than the other inputs\n    // because of \"margin: 1px 24px 1px 4px\" inside the shadow DOM\n    // Tested under Android 11 / Chrome 89, Android 12 / Chrome 100, Android 13 / Chrome 109\n    margin: 0;\n  }\n\n  // Prevent excessive date input height in Webkit\n  // https://github.com/twbs/bootstrap/issues/34433\n  &::-webkit-datetime-edit {\n    display: block;\n    padding: 0;\n  }\n\n  // Placeholder\n  &::placeholder {\n    color: $input-placeholder-color;\n    // Override Firefox's unusual default opacity; see https://github.com/twbs/bootstrap/pull/11526.\n    opacity: 1;\n  }\n\n  // Disabled inputs\n  //\n  // HTML5 says that controls under a fieldset > legend:first-child won't be\n  // disabled if the fieldset is disabled. Due to implementation difficulty, we\n  // don't honor that edge case; we style them as disabled anyway.\n  &:disabled {\n    color: $input-disabled-color;\n    background-color: $input-disabled-bg;\n    border-color: $input-disabled-border-color;\n    // iOS fix for unreadable disabled content; see https://github.com/twbs/bootstrap/issues/11655.\n    opacity: 1;\n  }\n\n  // File input buttons theming\n  &::file-selector-button {\n    padding: $input-padding-y $input-padding-x;\n    margin: (-$input-padding-y) (-$input-padding-x);\n    margin-inline-end: $input-padding-x;\n    color: $form-file-button-color;\n    @include gradient-bg($form-file-button-bg);\n    pointer-events: none;\n    border-color: inherit;\n    border-style: solid;\n    border-width: 0;\n    border-inline-end-width: $input-border-width;\n    border-radius: 0; // stylelint-disable-line property-disallowed-list\n    @include transition($btn-transition);\n  }\n\n  &:hover:not(:disabled):not([readonly])::file-selector-button {\n    background-color: $form-file-button-hover-bg;\n  }\n}\n\n// Readonly controls as plain text\n//\n// Apply class to a readonly input to make it appear like regular plain\n// text (without any border, background color, focus indicator)\n\n.form-control-plaintext {\n  display: block;\n  width: 100%;\n  padding: $input-padding-y 0;\n  margin-bottom: 0; // match inputs if this class comes on inputs with default margins\n  line-height: $input-line-height;\n  color: $input-plaintext-color;\n  background-color: transparent;\n  border: solid transparent;\n  border-width: $input-border-width 0;\n\n  &:focus {\n    outline: 0;\n  }\n\n  &.form-control-sm,\n  &.form-control-lg {\n    padding-right: 0;\n    padding-left: 0;\n  }\n}\n\n// Form control sizing\n//\n// Build on `.form-control` with modifier classes to decrease or increase the\n// height and font-size of form controls.\n//\n// Repeated in `_input_group.scss` to avoid Sass extend issues.\n\n.form-control-sm {\n  min-height: $input-height-sm;\n  padding: $input-padding-y-sm $input-padding-x-sm;\n  @include font-size($input-font-size-sm);\n  @include border-radius($input-border-radius-sm);\n\n  &::file-selector-button {\n    padding: $input-padding-y-sm $input-padding-x-sm;\n    margin: (-$input-padding-y-sm) (-$input-padding-x-sm);\n    margin-inline-end: $input-padding-x-sm;\n  }\n}\n\n.form-control-lg {\n  min-height: $input-height-lg;\n  padding: $input-padding-y-lg $input-padding-x-lg;\n  @include font-size($input-font-size-lg);\n  @include border-radius($input-border-radius-lg);\n\n  &::file-selector-button {\n    padding: $input-padding-y-lg $input-padding-x-lg;\n    margin: (-$input-padding-y-lg) (-$input-padding-x-lg);\n    margin-inline-end: $input-padding-x-lg;\n  }\n}\n\n// Make sure textareas don't shrink too much when resized\n// https://github.com/twbs/bootstrap/pull/29124\n// stylelint-disable selector-no-qualifying-type\ntextarea {\n  &.form-control {\n    min-height: $input-height;\n  }\n\n  &.form-control-sm {\n    min-height: $input-height-sm;\n  }\n\n  &.form-control-lg {\n    min-height: $input-height-lg;\n  }\n}\n// stylelint-enable selector-no-qualifying-type\n\n.form-control-color {\n  width: $form-color-width;\n  height: $input-height;\n  padding: $input-padding-y;\n\n  &:not(:disabled):not([readonly]) {\n    cursor: pointer;\n  }\n\n  &::-moz-color-swatch {\n    border: 0 !important; // stylelint-disable-line declaration-no-important\n    @include border-radius($input-border-radius);\n  }\n\n  &::-webkit-color-swatch {\n    border: 0 !important; // stylelint-disable-line declaration-no-important\n    @include border-radius($input-border-radius);\n  }\n\n  &.form-control-sm { height: $input-height-sm; }\n  &.form-control-lg { height: $input-height-lg; }\n}\n", "// stylelint-disable property-disallowed-list\n@mixin transition($transition...) {\n  @if length($transition) == 0 {\n    $transition: $transition-base;\n  }\n\n  @if length($transition) > 1 {\n    @each $value in $transition {\n      @if $value == null or $value == none {\n        @warn \"The keyword 'none' or 'null' must be used as a single argument.\";\n      }\n    }\n  }\n\n  @if $enable-transitions {\n    @if nth($transition, 1) != null {\n      transition: $transition;\n    }\n\n    @if $enable-reduced-motion and nth($transition, 1) != null and nth($transition, 1) != none {\n      @media (prefers-reduced-motion: reduce) {\n        transition: none;\n      }\n    }\n  }\n}\n", "// Gradients\n\n// scss-docs-start gradient-bg-mixin\n@mixin gradient-bg($color: null) {\n  background-color: $color;\n\n  @if $enable-gradients {\n    background-image: var(--#{$prefix}gradient);\n  }\n}\n// scss-docs-end gradient-bg-mixin\n\n// scss-docs-start gradient-mixins\n// Horizontal gradient, from left to right\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-x($start-color: $gray-700, $end-color: $gray-800, $start-percent: 0%, $end-percent: 100%) {\n  background-image: linear-gradient(to right, $start-color $start-percent, $end-color $end-percent);\n}\n\n// Vertical gradient, from top to bottom\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-y($start-color: $gray-700, $end-color: $gray-800, $start-percent: null, $end-percent: null) {\n  background-image: linear-gradient(to bottom, $start-color $start-percent, $end-color $end-percent);\n}\n\n@mixin gradient-directional($start-color: $gray-700, $end-color: $gray-800, $deg: 45deg) {\n  background-image: linear-gradient($deg, $start-color, $end-color);\n}\n\n@mixin gradient-x-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient(to right, $start-color, $mid-color $color-stop, $end-color);\n}\n\n@mixin gradient-y-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient($start-color, $mid-color $color-stop, $end-color);\n}\n\n@mixin gradient-radial($inner-color: $gray-700, $outer-color: $gray-800) {\n  background-image: radial-gradient(circle, $inner-color, $outer-color);\n}\n\n@mixin gradient-striped($color: rgba($white, .15), $angle: 45deg) {\n  background-image: linear-gradient($angle, $color 25%, transparent 25%, transparent 50%, $color 50%, $color 75%, transparent 75%, transparent);\n}\n// scss-docs-end gradient-mixins\n", "// Select\n//\n// Replaces the browser default select with a custom one, mostly pulled from\n// https://primer.github.io/.\n\n.form-select {\n  --#{$prefix}form-select-bg-img: #{escape-svg($form-select-indicator)};\n\n  display: block;\n  width: 100%;\n  padding: $form-select-padding-y $form-select-indicator-padding $form-select-padding-y $form-select-padding-x;\n  font-family: $form-select-font-family;\n  @include font-size($form-select-font-size);\n  font-weight: $form-select-font-weight;\n  line-height: $form-select-line-height;\n  color: $form-select-color;\n  appearance: none;\n  background-color: $form-select-bg;\n  background-image: var(--#{$prefix}form-select-bg-img), var(--#{$prefix}form-select-bg-icon, none);\n  background-repeat: no-repeat;\n  background-position: $form-select-bg-position;\n  background-size: $form-select-bg-size;\n  border: $form-select-border-width solid $form-select-border-color;\n  @include border-radius($form-select-border-radius, 0);\n  @include box-shadow($form-select-box-shadow);\n  @include transition($form-select-transition);\n\n  &:focus {\n    border-color: $form-select-focus-border-color;\n    outline: 0;\n    @if $enable-shadows {\n      @include box-shadow($form-select-box-shadow, $form-select-focus-box-shadow);\n    } @else {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      box-shadow: $form-select-focus-box-shadow;\n    }\n  }\n\n  &[multiple],\n  &[size]:not([size=\"1\"]) {\n    padding-right: $form-select-padding-x;\n    background-image: none;\n  }\n\n  &:disabled {\n    color: $form-select-disabled-color;\n    background-color: $form-select-disabled-bg;\n    border-color: $form-select-disabled-border-color;\n  }\n\n  // Remove outline from select box in FF\n  &:-moz-focusring {\n    color: transparent;\n    text-shadow: 0 0 0 $form-select-color;\n  }\n}\n\n.form-select-sm {\n  padding-top: $form-select-padding-y-sm;\n  padding-bottom: $form-select-padding-y-sm;\n  padding-left: $form-select-padding-x-sm;\n  @include font-size($form-select-font-size-sm);\n  @include border-radius($form-select-border-radius-sm);\n}\n\n.form-select-lg {\n  padding-top: $form-select-padding-y-lg;\n  padding-bottom: $form-select-padding-y-lg;\n  padding-left: $form-select-padding-x-lg;\n  @include font-size($form-select-font-size-lg);\n  @include border-radius($form-select-border-radius-lg);\n}\n\n@if $enable-dark-mode {\n  @include color-mode(dark) {\n    .form-select {\n      --#{$prefix}form-select-bg-img: #{escape-svg($form-select-indicator-dark)};\n    }\n  }\n}\n", "//\n// Check/radio\n//\n\n.form-check {\n  display: block;\n  min-height: $form-check-min-height;\n  padding-left: $form-check-padding-start;\n  margin-bottom: $form-check-margin-bottom;\n\n  .form-check-input {\n    float: left;\n    margin-left: $form-check-padding-start * -1;\n  }\n}\n\n.form-check-reverse {\n  padding-right: $form-check-padding-start;\n  padding-left: 0;\n  text-align: right;\n\n  .form-check-input {\n    float: right;\n    margin-right: $form-check-padding-start * -1;\n    margin-left: 0;\n  }\n}\n\n.form-check-input {\n  --#{$prefix}form-check-bg: #{$form-check-input-bg};\n\n  flex-shrink: 0;\n  width: $form-check-input-width;\n  height: $form-check-input-width;\n  margin-top: ($line-height-base - $form-check-input-width) * .5; // line-height minus check height\n  vertical-align: top;\n  appearance: none;\n  background-color: var(--#{$prefix}form-check-bg);\n  background-image: var(--#{$prefix}form-check-bg-image);\n  background-repeat: no-repeat;\n  background-position: center;\n  background-size: contain;\n  border: $form-check-input-border;\n  print-color-adjust: exact; // Keep themed appearance for print\n  @include transition($form-check-transition);\n\n  &[type=\"checkbox\"] {\n    @include border-radius($form-check-input-border-radius);\n  }\n\n  &[type=\"radio\"] {\n    // stylelint-disable-next-line property-disallowed-list\n    border-radius: $form-check-radio-border-radius;\n  }\n\n  &:active {\n    filter: $form-check-input-active-filter;\n  }\n\n  &:focus {\n    border-color: $form-check-input-focus-border;\n    outline: 0;\n    box-shadow: $form-check-input-focus-box-shadow;\n  }\n\n  &:checked {\n    background-color: $form-check-input-checked-bg-color;\n    border-color: $form-check-input-checked-border-color;\n\n    &[type=\"checkbox\"] {\n      @if $enable-gradients {\n        --#{$prefix}form-check-bg-image: #{escape-svg($form-check-input-checked-bg-image)}, var(--#{$prefix}gradient);\n      } @else {\n        --#{$prefix}form-check-bg-image: #{escape-svg($form-check-input-checked-bg-image)};\n      }\n    }\n\n    &[type=\"radio\"] {\n      @if $enable-gradients {\n        --#{$prefix}form-check-bg-image: #{escape-svg($form-check-radio-checked-bg-image)}, var(--#{$prefix}gradient);\n      } @else {\n        --#{$prefix}form-check-bg-image: #{escape-svg($form-check-radio-checked-bg-image)};\n      }\n    }\n  }\n\n  &[type=\"checkbox\"]:indeterminate {\n    background-color: $form-check-input-indeterminate-bg-color;\n    border-color: $form-check-input-indeterminate-border-color;\n\n    @if $enable-gradients {\n      --#{$prefix}form-check-bg-image: #{escape-svg($form-check-input-indeterminate-bg-image)}, var(--#{$prefix}gradient);\n    } @else {\n      --#{$prefix}form-check-bg-image: #{escape-svg($form-check-input-indeterminate-bg-image)};\n    }\n  }\n\n  &:disabled {\n    pointer-events: none;\n    filter: none;\n    opacity: $form-check-input-disabled-opacity;\n  }\n\n  // Use disabled attribute in addition of :disabled pseudo-class\n  // See: https://github.com/twbs/bootstrap/issues/28247\n  &[disabled],\n  &:disabled {\n    ~ .form-check-label {\n      cursor: default;\n      opacity: $form-check-label-disabled-opacity;\n    }\n  }\n}\n\n.form-check-label {\n  color: $form-check-label-color;\n  cursor: $form-check-label-cursor;\n}\n\n//\n// Switch\n//\n\n.form-switch {\n  padding-left: $form-switch-padding-start;\n\n  .form-check-input {\n    --#{$prefix}form-switch-bg: #{escape-svg($form-switch-bg-image)};\n\n    width: $form-switch-width;\n    margin-left: $form-switch-padding-start * -1;\n    background-image: var(--#{$prefix}form-switch-bg);\n    background-position: left center;\n    @include border-radius($form-switch-border-radius, 0);\n    @include transition($form-switch-transition);\n\n    &:focus {\n      --#{$prefix}form-switch-bg: #{escape-svg($form-switch-focus-bg-image)};\n    }\n\n    &:checked {\n      background-position: $form-switch-checked-bg-position;\n\n      @if $enable-gradients {\n        --#{$prefix}form-switch-bg: #{escape-svg($form-switch-checked-bg-image)}, var(--#{$prefix}gradient);\n      } @else {\n        --#{$prefix}form-switch-bg: #{escape-svg($form-switch-checked-bg-image)};\n      }\n    }\n  }\n\n  &.form-check-reverse {\n    padding-right: $form-switch-padding-start;\n    padding-left: 0;\n\n    .form-check-input {\n      margin-right: $form-switch-padding-start * -1;\n      margin-left: 0;\n    }\n  }\n}\n\n.form-check-inline {\n  display: inline-block;\n  margin-right: $form-check-inline-margin-end;\n}\n\n.btn-check {\n  position: absolute;\n  clip: rect(0, 0, 0, 0);\n  pointer-events: none;\n\n  &[disabled],\n  &:disabled {\n    + .btn {\n      pointer-events: none;\n      filter: none;\n      opacity: $form-check-btn-check-disabled-opacity;\n    }\n  }\n}\n\n@if $enable-dark-mode {\n  @include color-mode(dark) {\n    .form-switch .form-check-input:not(:checked):not(:focus) {\n      --#{$prefix}form-switch-bg: #{escape-svg($form-switch-bg-image-dark)};\n    }\n  }\n}\n", "// Range\n//\n// Style range inputs the same across browsers. Vendor-specific rules for pseudo\n// elements cannot be mixed. As such, there are no shared styles for focus or\n// active states on prefixed selectors.\n\n.form-range {\n  width: 100%;\n  height: add($form-range-thumb-height, $form-range-thumb-focus-box-shadow-width * 2);\n  padding: 0; // Need to reset padding\n  appearance: none;\n  background-color: transparent;\n\n  &:focus {\n    outline: 0;\n\n    // Pseudo-elements must be split across multiple rulesets to have an effect.\n    // No box-shadow() mixin for focus accessibility.\n    &::-webkit-slider-thumb { box-shadow: $form-range-thumb-focus-box-shadow; }\n    &::-moz-range-thumb     { box-shadow: $form-range-thumb-focus-box-shadow; }\n  }\n\n  &::-moz-focus-outer {\n    border: 0;\n  }\n\n  &::-webkit-slider-thumb {\n    width: $form-range-thumb-width;\n    height: $form-range-thumb-height;\n    margin-top: ($form-range-track-height - $form-range-thumb-height) * .5; // Webkit specific\n    appearance: none;\n    @include gradient-bg($form-range-thumb-bg);\n    border: $form-range-thumb-border;\n    @include border-radius($form-range-thumb-border-radius);\n    @include box-shadow($form-range-thumb-box-shadow);\n    @include transition($form-range-thumb-transition);\n\n    &:active {\n      @include gradient-bg($form-range-thumb-active-bg);\n    }\n  }\n\n  &::-webkit-slider-runnable-track {\n    width: $form-range-track-width;\n    height: $form-range-track-height;\n    color: transparent; // Why?\n    cursor: $form-range-track-cursor;\n    background-color: $form-range-track-bg;\n    border-color: transparent;\n    @include border-radius($form-range-track-border-radius);\n    @include box-shadow($form-range-track-box-shadow);\n  }\n\n  &::-moz-range-thumb {\n    width: $form-range-thumb-width;\n    height: $form-range-thumb-height;\n    appearance: none;\n    @include gradient-bg($form-range-thumb-bg);\n    border: $form-range-thumb-border;\n    @include border-radius($form-range-thumb-border-radius);\n    @include box-shadow($form-range-thumb-box-shadow);\n    @include transition($form-range-thumb-transition);\n\n    &:active {\n      @include gradient-bg($form-range-thumb-active-bg);\n    }\n  }\n\n  &::-moz-range-track {\n    width: $form-range-track-width;\n    height: $form-range-track-height;\n    color: transparent;\n    cursor: $form-range-track-cursor;\n    background-color: $form-range-track-bg;\n    border-color: transparent; // Firefox specific?\n    @include border-radius($form-range-track-border-radius);\n    @include box-shadow($form-range-track-box-shadow);\n  }\n\n  &:disabled {\n    pointer-events: none;\n\n    &::-webkit-slider-thumb {\n      background-color: $form-range-thumb-disabled-bg;\n    }\n\n    &::-moz-range-thumb {\n      background-color: $form-range-thumb-disabled-bg;\n    }\n  }\n}\n", ".form-floating {\n  position: relative;\n\n  > .form-control,\n  > .form-control-plaintext,\n  > .form-select {\n    height: $form-floating-height;\n    min-height: $form-floating-height;\n    line-height: $form-floating-line-height;\n  }\n\n  > label {\n    position: absolute;\n    top: 0;\n    left: 0;\n    z-index: 2;\n    height: 100%; // allow textareas\n    padding: $form-floating-padding-y $form-floating-padding-x;\n    overflow: hidden;\n    text-align: start;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n    pointer-events: none;\n    border: $input-border-width solid transparent; // Required for aligning label's text with the input as it affects inner box model\n    transform-origin: 0 0;\n    @include transition($form-floating-transition);\n  }\n\n  > .form-control,\n  > .form-control-plaintext {\n    padding: $form-floating-padding-y $form-floating-padding-x;\n\n    &::placeholder {\n      color: transparent;\n    }\n\n    &:focus,\n    &:not(:placeholder-shown) {\n      padding-top: $form-floating-input-padding-t;\n      padding-bottom: $form-floating-input-padding-b;\n    }\n    // Duplicated because `:-webkit-autofill` invalidates other selectors when grouped\n    &:-webkit-autofill {\n      padding-top: $form-floating-input-padding-t;\n      padding-bottom: $form-floating-input-padding-b;\n    }\n  }\n\n  > .form-select {\n    padding-top: $form-floating-input-padding-t;\n    padding-bottom: $form-floating-input-padding-b;\n  }\n\n  > .form-control:focus,\n  > .form-control:not(:placeholder-shown),\n  > .form-control-plaintext,\n  > .form-select {\n    ~ label {\n      color: rgba(var(--#{$prefix}body-color-rgb), #{$form-floating-label-opacity});\n      transform: $form-floating-label-transform;\n\n      &::after {\n        position: absolute;\n        inset: $form-floating-padding-y ($form-floating-padding-x * .5);\n        z-index: -1;\n        height: $form-floating-label-height;\n        content: \"\";\n        background-color: $input-bg;\n        @include border-radius($input-border-radius);\n      }\n    }\n  }\n  // Duplicated because `:-webkit-autofill` invalidates other selectors when grouped\n  > .form-control:-webkit-autofill {\n    ~ label {\n      color: rgba(var(--#{$prefix}body-color-rgb), #{$form-floating-label-opacity});\n      transform: $form-floating-label-transform;\n    }\n  }\n\n  > .form-control-plaintext {\n    ~ label {\n      border-width: $input-border-width 0; // Required to properly position label text - as explained above\n    }\n  }\n\n  > :disabled ~ label,\n  > .form-control:disabled ~ label { // Required for `.form-control`s because of specificity\n    color: $form-floating-label-disabled-color;\n\n    &::after {\n      background-color: $input-disabled-bg;\n    }\n  }\n}\n", "//\n// Base styles\n//\n\n.input-group {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap; // For form validation feedback\n  align-items: stretch;\n  width: 100%;\n\n  > .form-control,\n  > .form-select,\n  > .form-floating {\n    position: relative; // For focus state's z-index\n    flex: 1 1 auto;\n    width: 1%;\n    min-width: 0; // https://stackoverflow.com/questions/36247140/why-dont-flex-items-shrink-past-content-size\n  }\n\n  // Bring the \"active\" form control to the top of surrounding elements\n  > .form-control:focus,\n  > .form-select:focus,\n  > .form-floating:focus-within {\n    z-index: 5;\n  }\n\n  // Ensure buttons are always above inputs for more visually pleasing borders.\n  // This isn't needed for `.input-group-text` since it shares the same border-color\n  // as our inputs.\n  .btn {\n    position: relative;\n    z-index: 2;\n\n    &:focus {\n      z-index: 5;\n    }\n  }\n}\n\n\n// Textual addons\n//\n// Serves as a catch-all element for any text or radio/checkbox input you wish\n// to prepend or append to an input.\n\n.input-group-text {\n  display: flex;\n  align-items: center;\n  padding: $input-group-addon-padding-y $input-group-addon-padding-x;\n  @include font-size($input-font-size); // Match inputs\n  font-weight: $input-group-addon-font-weight;\n  line-height: $input-line-height;\n  color: $input-group-addon-color;\n  text-align: center;\n  white-space: nowrap;\n  background-color: $input-group-addon-bg;\n  border: $input-border-width solid $input-group-addon-border-color;\n  @include border-radius($input-border-radius);\n}\n\n\n// Sizing\n//\n// Remix the default form control sizing classes into new ones for easier\n// manipulation.\n\n.input-group-lg > .form-control,\n.input-group-lg > .form-select,\n.input-group-lg > .input-group-text,\n.input-group-lg > .btn {\n  padding: $input-padding-y-lg $input-padding-x-lg;\n  @include font-size($input-font-size-lg);\n  @include border-radius($input-border-radius-lg);\n}\n\n.input-group-sm > .form-control,\n.input-group-sm > .form-select,\n.input-group-sm > .input-group-text,\n.input-group-sm > .btn {\n  padding: $input-padding-y-sm $input-padding-x-sm;\n  @include font-size($input-font-size-sm);\n  @include border-radius($input-border-radius-sm);\n}\n\n.input-group-lg > .form-select,\n.input-group-sm > .form-select {\n  padding-right: $form-select-padding-x + $form-select-indicator-padding;\n}\n\n\n// Rounded corners\n//\n// These rulesets must come after the sizing ones to properly override sm and lg\n// border-radius values when extending. They're more specific than we'd like\n// with the `.input-group >` part, but without it, we cannot override the sizing.\n\n// stylelint-disable-next-line no-duplicate-selectors\n.input-group {\n  &:not(.has-validation) {\n    > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating),\n    > .dropdown-toggle:nth-last-child(n + 3),\n    > .form-floating:not(:last-child) > .form-control,\n    > .form-floating:not(:last-child) > .form-select {\n      @include border-end-radius(0);\n    }\n  }\n\n  &.has-validation {\n    > :nth-last-child(n + 3):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating),\n    > .dropdown-toggle:nth-last-child(n + 4),\n    > .form-floating:nth-last-child(n + 3) > .form-control,\n    > .form-floating:nth-last-child(n + 3) > .form-select {\n      @include border-end-radius(0);\n    }\n  }\n\n  $validation-messages: \"\";\n  @each $state in map-keys($form-validation-states) {\n    $validation-messages: $validation-messages + \":not(.\" + unquote($state) + \"-tooltip)\" + \":not(.\" + unquote($state) + \"-feedback)\";\n  }\n\n  > :not(:first-child):not(.dropdown-menu)#{$validation-messages} {\n    margin-left: calc(#{$input-border-width} * -1); // stylelint-disable-line function-disallowed-list\n    @include border-start-radius(0);\n  }\n\n  > .form-floating:not(:first-child) > .form-control,\n  > .form-floating:not(:first-child) > .form-select {\n    @include border-start-radius(0);\n  }\n}\n", "// This mixin uses an `if()` technique to be compatible with Dart Sass\n// See https://github.com/sass/sass/issues/1873#issuecomment-152293725 for more details\n\n// scss-docs-start form-validation-mixins\n@mixin form-validation-state-selector($state) {\n  @if ($state == \"valid\" or $state == \"invalid\") {\n    .was-validated #{if(&, \"&\", \"\")}:#{$state},\n    #{if(&, \"&\", \"\")}.is-#{$state} {\n      @content;\n    }\n  } @else {\n    #{if(&, \"&\", \"\")}.is-#{$state} {\n      @content;\n    }\n  }\n}\n\n@mixin form-validation-state(\n  $state,\n  $color,\n  $icon,\n  $tooltip-color: color-contrast($color),\n  $tooltip-bg-color: rgba($color, $form-feedback-tooltip-opacity),\n  $focus-box-shadow: 0 0 $input-btn-focus-blur $input-focus-width rgba($color, $input-btn-focus-color-opacity),\n  $border-color: $color\n) {\n  .#{$state}-feedback {\n    display: none;\n    width: 100%;\n    margin-top: $form-feedback-margin-top;\n    @include font-size($form-feedback-font-size);\n    font-style: $form-feedback-font-style;\n    color: $color;\n  }\n\n  .#{$state}-tooltip {\n    position: absolute;\n    top: 100%;\n    z-index: 5;\n    display: none;\n    max-width: 100%; // Contain to parent when possible\n    padding: $form-feedback-tooltip-padding-y $form-feedback-tooltip-padding-x;\n    margin-top: .1rem;\n    @include font-size($form-feedback-tooltip-font-size);\n    line-height: $form-feedback-tooltip-line-height;\n    color: $tooltip-color;\n    background-color: $tooltip-bg-color;\n    @include border-radius($form-feedback-tooltip-border-radius);\n  }\n\n  @include form-validation-state-selector($state) {\n    ~ .#{$state}-feedback,\n    ~ .#{$state}-tooltip {\n      display: block;\n    }\n  }\n\n  .form-control {\n    @include form-validation-state-selector($state) {\n      border-color: $border-color;\n\n      @if $enable-validation-icons {\n        padding-right: $input-height-inner;\n        background-image: escape-svg($icon);\n        background-repeat: no-repeat;\n        background-position: right $input-height-inner-quarter center;\n        background-size: $input-height-inner-half $input-height-inner-half;\n      }\n\n      &:focus {\n        border-color: $border-color;\n        @if $enable-shadows {\n          @include box-shadow($input-box-shadow, $focus-box-shadow);\n        } @else {\n          // Avoid using mixin so we can pass custom focus shadow properly\n          box-shadow: $focus-box-shadow;\n        }\n      }\n    }\n  }\n\n  // stylelint-disable-next-line selector-no-qualifying-type\n  textarea.form-control {\n    @include form-validation-state-selector($state) {\n      @if $enable-validation-icons {\n        padding-right: $input-height-inner;\n        background-position: top $input-height-inner-quarter right $input-height-inner-quarter;\n      }\n    }\n  }\n\n  .form-select {\n    @include form-validation-state-selector($state) {\n      border-color: $border-color;\n\n      @if $enable-validation-icons {\n        &:not([multiple]):not([size]),\n        &:not([multiple])[size=\"1\"] {\n          --#{$prefix}form-select-bg-icon: #{escape-svg($icon)};\n          padding-right: $form-select-feedback-icon-padding-end;\n          background-position: $form-select-bg-position, $form-select-feedback-icon-position;\n          background-size: $form-select-bg-size, $form-select-feedback-icon-size;\n        }\n      }\n\n      &:focus {\n        border-color: $border-color;\n        @if $enable-shadows {\n          @include box-shadow($form-select-box-shadow, $focus-box-shadow);\n        } @else {\n          // Avoid using mixin so we can pass custom focus shadow properly\n          box-shadow: $focus-box-shadow;\n        }\n      }\n    }\n  }\n\n  .form-control-color {\n    @include form-validation-state-selector($state) {\n      @if $enable-validation-icons {\n        width: add($form-color-width, $input-height-inner);\n      }\n    }\n  }\n\n  .form-check-input {\n    @include form-validation-state-selector($state) {\n      border-color: $border-color;\n\n      &:checked {\n        background-color: $color;\n      }\n\n      &:focus {\n        box-shadow: $focus-box-shadow;\n      }\n\n      ~ .form-check-label {\n        color: $color;\n      }\n    }\n  }\n  .form-check-inline .form-check-input {\n    ~ .#{$state}-feedback {\n      margin-left: .5em;\n    }\n  }\n\n  .input-group {\n    > .form-control:not(:focus),\n    > .form-select:not(:focus),\n    > .form-floating:not(:focus-within) {\n      @include form-validation-state-selector($state) {\n        @if $state == \"valid\" {\n          z-index: 3;\n        } @else if $state == \"invalid\" {\n          z-index: 4;\n        }\n      }\n    }\n  }\n}\n// scss-docs-end form-validation-mixins\n", "//\n// Base styles\n//\n\n.btn {\n  // scss-docs-start btn-css-vars\n  --#{$prefix}btn-padding-x: #{$btn-padding-x};\n  --#{$prefix}btn-padding-y: #{$btn-padding-y};\n  --#{$prefix}btn-font-family: #{$btn-font-family};\n  @include rfs($btn-font-size, --#{$prefix}btn-font-size);\n  --#{$prefix}btn-font-weight: #{$btn-font-weight};\n  --#{$prefix}btn-line-height: #{$btn-line-height};\n  --#{$prefix}btn-color: #{$btn-color};\n  --#{$prefix}btn-bg: transparent;\n  --#{$prefix}btn-border-width: #{$btn-border-width};\n  --#{$prefix}btn-border-color: transparent;\n  --#{$prefix}btn-border-radius: #{$btn-border-radius};\n  --#{$prefix}btn-hover-border-color: transparent;\n  --#{$prefix}btn-box-shadow: #{$btn-box-shadow};\n  --#{$prefix}btn-disabled-opacity: #{$btn-disabled-opacity};\n  --#{$prefix}btn-focus-box-shadow: 0 0 0 #{$btn-focus-width} rgba(var(--#{$prefix}btn-focus-shadow-rgb), .5);\n  // scss-docs-end btn-css-vars\n\n  display: inline-block;\n  padding: var(--#{$prefix}btn-padding-y) var(--#{$prefix}btn-padding-x);\n  font-family: var(--#{$prefix}btn-font-family);\n  @include font-size(var(--#{$prefix}btn-font-size));\n  font-weight: var(--#{$prefix}btn-font-weight);\n  line-height: var(--#{$prefix}btn-line-height);\n  color: var(--#{$prefix}btn-color);\n  text-align: center;\n  text-decoration: if($link-decoration == none, null, none);\n  white-space: $btn-white-space;\n  vertical-align: middle;\n  cursor: if($enable-button-pointers, pointer, null);\n  user-select: none;\n  border: var(--#{$prefix}btn-border-width) solid var(--#{$prefix}btn-border-color);\n  @include border-radius(var(--#{$prefix}btn-border-radius));\n  @include gradient-bg(var(--#{$prefix}btn-bg));\n  @include box-shadow(var(--#{$prefix}btn-box-shadow));\n  @include transition($btn-transition);\n\n  &:hover {\n    color: var(--#{$prefix}btn-hover-color);\n    text-decoration: if($link-hover-decoration == underline, none, null);\n    background-color: var(--#{$prefix}btn-hover-bg);\n    border-color: var(--#{$prefix}btn-hover-border-color);\n  }\n\n  .btn-check + &:hover {\n    // override for the checkbox/radio buttons\n    color: var(--#{$prefix}btn-color);\n    background-color: var(--#{$prefix}btn-bg);\n    border-color: var(--#{$prefix}btn-border-color);\n  }\n\n  &:focus-visible {\n    color: var(--#{$prefix}btn-hover-color);\n    @include gradient-bg(var(--#{$prefix}btn-hover-bg));\n    border-color: var(--#{$prefix}btn-hover-border-color);\n    outline: 0;\n    // Avoid using mixin so we can pass custom focus shadow properly\n    @if $enable-shadows {\n      box-shadow: var(--#{$prefix}btn-box-shadow), var(--#{$prefix}btn-focus-box-shadow);\n    } @else {\n      box-shadow: var(--#{$prefix}btn-focus-box-shadow);\n    }\n  }\n\n  .btn-check:focus-visible + & {\n    border-color: var(--#{$prefix}btn-hover-border-color);\n    outline: 0;\n    // Avoid using mixin so we can pass custom focus shadow properly\n    @if $enable-shadows {\n      box-shadow: var(--#{$prefix}btn-box-shadow), var(--#{$prefix}btn-focus-box-shadow);\n    } @else {\n      box-shadow: var(--#{$prefix}btn-focus-box-shadow);\n    }\n  }\n\n  .btn-check:checked + &,\n  :not(.btn-check) + &:active,\n  &:first-child:active,\n  &.active,\n  &.show {\n    color: var(--#{$prefix}btn-active-color);\n    background-color: var(--#{$prefix}btn-active-bg);\n    // Remove CSS gradients if they're enabled\n    background-image: if($enable-gradients, none, null);\n    border-color: var(--#{$prefix}btn-active-border-color);\n    @include box-shadow(var(--#{$prefix}btn-active-shadow));\n\n    &:focus-visible {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      @if $enable-shadows {\n        box-shadow: var(--#{$prefix}btn-active-shadow), var(--#{$prefix}btn-focus-box-shadow);\n      } @else {\n        box-shadow: var(--#{$prefix}btn-focus-box-shadow);\n      }\n    }\n  }\n\n  .btn-check:checked:focus-visible + & {\n    // Avoid using mixin so we can pass custom focus shadow properly\n    @if $enable-shadows {\n      box-shadow: var(--#{$prefix}btn-active-shadow), var(--#{$prefix}btn-focus-box-shadow);\n    } @else {\n      box-shadow: var(--#{$prefix}btn-focus-box-shadow);\n    }\n  }\n\n  &:disabled,\n  &.disabled,\n  fieldset:disabled & {\n    color: var(--#{$prefix}btn-disabled-color);\n    pointer-events: none;\n    background-color: var(--#{$prefix}btn-disabled-bg);\n    background-image: if($enable-gradients, none, null);\n    border-color: var(--#{$prefix}btn-disabled-border-color);\n    opacity: var(--#{$prefix}btn-disabled-opacity);\n    @include box-shadow(none);\n  }\n}\n\n\n//\n// Alternate buttons\n//\n\n// scss-docs-start btn-variant-loops\n@each $color, $value in $theme-colors {\n  .btn-#{$color} {\n    @if $color == \"light\" {\n      @include button-variant(\n        $value,\n        $value,\n        $hover-background: shade-color($value, $btn-hover-bg-shade-amount),\n        $hover-border: shade-color($value, $btn-hover-border-shade-amount),\n        $active-background: shade-color($value, $btn-active-bg-shade-amount),\n        $active-border: shade-color($value, $btn-active-border-shade-amount)\n      );\n    } @else if $color == \"dark\" {\n      @include button-variant(\n        $value,\n        $value,\n        $hover-background: tint-color($value, $btn-hover-bg-tint-amount),\n        $hover-border: tint-color($value, $btn-hover-border-tint-amount),\n        $active-background: tint-color($value, $btn-active-bg-tint-amount),\n        $active-border: tint-color($value, $btn-active-border-tint-amount)\n      );\n    } @else {\n      @include button-variant($value, $value);\n    }\n  }\n}\n\n@each $color, $value in $theme-colors {\n  .btn-outline-#{$color} {\n    @include button-outline-variant($value);\n  }\n}\n// scss-docs-end btn-variant-loops\n\n\n//\n// Link buttons\n//\n\n// Make a button look and behave like a link\n.btn-link {\n  --#{$prefix}btn-font-weight: #{$font-weight-normal};\n  --#{$prefix}btn-color: #{$btn-link-color};\n  --#{$prefix}btn-bg: transparent;\n  --#{$prefix}btn-border-color: transparent;\n  --#{$prefix}btn-hover-color: #{$btn-link-hover-color};\n  --#{$prefix}btn-hover-border-color: transparent;\n  --#{$prefix}btn-active-color: #{$btn-link-hover-color};\n  --#{$prefix}btn-active-border-color: transparent;\n  --#{$prefix}btn-disabled-color: #{$btn-link-disabled-color};\n  --#{$prefix}btn-disabled-border-color: transparent;\n  --#{$prefix}btn-box-shadow: 0 0 0 #000; // Can't use `none` as keyword negates all values when used with multiple shadows\n  --#{$prefix}btn-focus-shadow-rgb: #{$btn-link-focus-shadow-rgb};\n\n  text-decoration: $link-decoration;\n  @if $enable-gradients {\n    background-image: none;\n  }\n\n  &:hover,\n  &:focus-visible {\n    text-decoration: $link-hover-decoration;\n  }\n\n  &:focus-visible {\n    color: var(--#{$prefix}btn-color);\n  }\n\n  &:hover {\n    color: var(--#{$prefix}btn-hover-color);\n  }\n\n  // No need for an active state here\n}\n\n\n//\n// Button Sizes\n//\n\n.btn-lg {\n  @include button-size($btn-padding-y-lg, $btn-padding-x-lg, $btn-font-size-lg, $btn-border-radius-lg);\n}\n\n.btn-sm {\n  @include button-size($btn-padding-y-sm, $btn-padding-x-sm, $btn-font-size-sm, $btn-border-radius-sm);\n}\n", "// Button variants\n//\n// Easily pump out default styles, as well as :hover, :focus, :active,\n// and disabled options for all buttons\n\n// scss-docs-start btn-variant-mixin\n@mixin button-variant(\n  $background,\n  $border,\n  $color: color-contrast($background),\n  $hover-background: if($color == $color-contrast-light, shade-color($background, $btn-hover-bg-shade-amount), tint-color($background, $btn-hover-bg-tint-amount)),\n  $hover-border: if($color == $color-contrast-light, shade-color($border, $btn-hover-border-shade-amount), tint-color($border, $btn-hover-border-tint-amount)),\n  $hover-color: color-contrast($hover-background),\n  $active-background: if($color == $color-contrast-light, shade-color($background, $btn-active-bg-shade-amount), tint-color($background, $btn-active-bg-tint-amount)),\n  $active-border: if($color == $color-contrast-light, shade-color($border, $btn-active-border-shade-amount), tint-color($border, $btn-active-border-tint-amount)),\n  $active-color: color-contrast($active-background),\n  $disabled-background: $background,\n  $disabled-border: $border,\n  $disabled-color: color-contrast($disabled-background)\n) {\n  --#{$prefix}btn-color: #{$color};\n  --#{$prefix}btn-bg: #{$background};\n  --#{$prefix}btn-border-color: #{$border};\n  --#{$prefix}btn-hover-color: #{$hover-color};\n  --#{$prefix}btn-hover-bg: #{$hover-background};\n  --#{$prefix}btn-hover-border-color: #{$hover-border};\n  --#{$prefix}btn-focus-shadow-rgb: #{to-rgb(mix($color, $border, 15%))};\n  --#{$prefix}btn-active-color: #{$active-color};\n  --#{$prefix}btn-active-bg: #{$active-background};\n  --#{$prefix}btn-active-border-color: #{$active-border};\n  --#{$prefix}btn-active-shadow: #{$btn-active-box-shadow};\n  --#{$prefix}btn-disabled-color: #{$disabled-color};\n  --#{$prefix}btn-disabled-bg: #{$disabled-background};\n  --#{$prefix}btn-disabled-border-color: #{$disabled-border};\n}\n// scss-docs-end btn-variant-mixin\n\n// scss-docs-start btn-outline-variant-mixin\n@mixin button-outline-variant(\n  $color,\n  $color-hover: color-contrast($color),\n  $active-background: $color,\n  $active-border: $color,\n  $active-color: color-contrast($active-background)\n) {\n  --#{$prefix}btn-color: #{$color};\n  --#{$prefix}btn-border-color: #{$color};\n  --#{$prefix}btn-hover-color: #{$color-hover};\n  --#{$prefix}btn-hover-bg: #{$active-background};\n  --#{$prefix}btn-hover-border-color: #{$active-border};\n  --#{$prefix}btn-focus-shadow-rgb: #{to-rgb($color)};\n  --#{$prefix}btn-active-color: #{$active-color};\n  --#{$prefix}btn-active-bg: #{$active-background};\n  --#{$prefix}btn-active-border-color: #{$active-border};\n  --#{$prefix}btn-active-shadow: #{$btn-active-box-shadow};\n  --#{$prefix}btn-disabled-color: #{$color};\n  --#{$prefix}btn-disabled-bg: transparent;\n  --#{$prefix}btn-disabled-border-color: #{$color};\n  --#{$prefix}gradient: none;\n}\n// scss-docs-end btn-outline-variant-mixin\n\n// scss-docs-start btn-size-mixin\n@mixin button-size($padding-y, $padding-x, $font-size, $border-radius) {\n  --#{$prefix}btn-padding-y: #{$padding-y};\n  --#{$prefix}btn-padding-x: #{$padding-x};\n  @include rfs($font-size, --#{$prefix}btn-font-size);\n  --#{$prefix}btn-border-radius: #{$border-radius};\n}\n// scss-docs-end btn-size-mixin\n", ".fade {\n  @include transition($transition-fade);\n\n  &:not(.show) {\n    opacity: 0;\n  }\n}\n\n// scss-docs-start collapse-classes\n.collapse {\n  &:not(.show) {\n    display: none;\n  }\n}\n\n.collapsing {\n  height: 0;\n  overflow: hidden;\n  @include transition($transition-collapse);\n\n  &.collapse-horizontal {\n    width: 0;\n    height: auto;\n    @include transition($transition-collapse-width);\n  }\n}\n// scss-docs-end collapse-classes\n", "// The dropdown wrapper (`<div>`)\n.dropup,\n.dropend,\n.dropdown,\n.dropstart,\n.dropup-center,\n.dropdown-center {\n  position: relative;\n}\n\n.dropdown-toggle {\n  white-space: nowrap;\n\n  // Generate the caret automatically\n  @include caret();\n}\n\n// The dropdown menu\n.dropdown-menu {\n  // scss-docs-start dropdown-css-vars\n  --#{$prefix}dropdown-zindex: #{$zindex-dropdown};\n  --#{$prefix}dropdown-min-width: #{$dropdown-min-width};\n  --#{$prefix}dropdown-padding-x: #{$dropdown-padding-x};\n  --#{$prefix}dropdown-padding-y: #{$dropdown-padding-y};\n  --#{$prefix}dropdown-spacer: #{$dropdown-spacer};\n  @include rfs($dropdown-font-size, --#{$prefix}dropdown-font-size);\n  --#{$prefix}dropdown-color: #{$dropdown-color};\n  --#{$prefix}dropdown-bg: #{$dropdown-bg};\n  --#{$prefix}dropdown-border-color: #{$dropdown-border-color};\n  --#{$prefix}dropdown-border-radius: #{$dropdown-border-radius};\n  --#{$prefix}dropdown-border-width: #{$dropdown-border-width};\n  --#{$prefix}dropdown-inner-border-radius: #{$dropdown-inner-border-radius};\n  --#{$prefix}dropdown-divider-bg: #{$dropdown-divider-bg};\n  --#{$prefix}dropdown-divider-margin-y: #{$dropdown-divider-margin-y};\n  --#{$prefix}dropdown-box-shadow: #{$dropdown-box-shadow};\n  --#{$prefix}dropdown-link-color: #{$dropdown-link-color};\n  --#{$prefix}dropdown-link-hover-color: #{$dropdown-link-hover-color};\n  --#{$prefix}dropdown-link-hover-bg: #{$dropdown-link-hover-bg};\n  --#{$prefix}dropdown-link-active-color: #{$dropdown-link-active-color};\n  --#{$prefix}dropdown-link-active-bg: #{$dropdown-link-active-bg};\n  --#{$prefix}dropdown-link-disabled-color: #{$dropdown-link-disabled-color};\n  --#{$prefix}dropdown-item-padding-x: #{$dropdown-item-padding-x};\n  --#{$prefix}dropdown-item-padding-y: #{$dropdown-item-padding-y};\n  --#{$prefix}dropdown-header-color: #{$dropdown-header-color};\n  --#{$prefix}dropdown-header-padding-x: #{$dropdown-header-padding-x};\n  --#{$prefix}dropdown-header-padding-y: #{$dropdown-header-padding-y};\n  // scss-docs-end dropdown-css-vars\n\n  position: absolute;\n  z-index: var(--#{$prefix}dropdown-zindex);\n  display: none; // none by default, but block on \"open\" of the menu\n  min-width: var(--#{$prefix}dropdown-min-width);\n  padding: var(--#{$prefix}dropdown-padding-y) var(--#{$prefix}dropdown-padding-x);\n  margin: 0; // Override default margin of ul\n  @include font-size(var(--#{$prefix}dropdown-font-size));\n  color: var(--#{$prefix}dropdown-color);\n  text-align: left; // Ensures proper alignment if parent has it changed (e.g., modal footer)\n  list-style: none;\n  background-color: var(--#{$prefix}dropdown-bg);\n  background-clip: padding-box;\n  border: var(--#{$prefix}dropdown-border-width) solid var(--#{$prefix}dropdown-border-color);\n  @include border-radius(var(--#{$prefix}dropdown-border-radius));\n  @include box-shadow(var(--#{$prefix}dropdown-box-shadow));\n\n  &[data-bs-popper] {\n    top: 100%;\n    left: 0;\n    margin-top: var(--#{$prefix}dropdown-spacer);\n  }\n\n  @if $dropdown-padding-y == 0 {\n    > .dropdown-item:first-child,\n    > li:first-child .dropdown-item {\n      @include border-top-radius(var(--#{$prefix}dropdown-inner-border-radius));\n    }\n    > .dropdown-item:last-child,\n    > li:last-child .dropdown-item {\n      @include border-bottom-radius(var(--#{$prefix}dropdown-inner-border-radius));\n    }\n\n  }\n}\n\n// scss-docs-start responsive-breakpoints\n// We deliberately hardcode the `bs-` prefix because we check\n// this custom property in JS to determine Popper's positioning\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .dropdown-menu#{$infix}-start {\n      --bs-position: start;\n\n      &[data-bs-popper] {\n        right: auto;\n        left: 0;\n      }\n    }\n\n    .dropdown-menu#{$infix}-end {\n      --bs-position: end;\n\n      &[data-bs-popper] {\n        right: 0;\n        left: auto;\n      }\n    }\n  }\n}\n// scss-docs-end responsive-breakpoints\n\n// Allow for dropdowns to go bottom up (aka, dropup-menu)\n// Just add .dropup after the standard .dropdown class and you're set.\n.dropup {\n  .dropdown-menu[data-bs-popper] {\n    top: auto;\n    bottom: 100%;\n    margin-top: 0;\n    margin-bottom: var(--#{$prefix}dropdown-spacer);\n  }\n\n  .dropdown-toggle {\n    @include caret(up);\n  }\n}\n\n.dropend {\n  .dropdown-menu[data-bs-popper] {\n    top: 0;\n    right: auto;\n    left: 100%;\n    margin-top: 0;\n    margin-left: var(--#{$prefix}dropdown-spacer);\n  }\n\n  .dropdown-toggle {\n    @include caret(end);\n    &::after {\n      vertical-align: 0;\n    }\n  }\n}\n\n.dropstart {\n  .dropdown-menu[data-bs-popper] {\n    top: 0;\n    right: 100%;\n    left: auto;\n    margin-top: 0;\n    margin-right: var(--#{$prefix}dropdown-spacer);\n  }\n\n  .dropdown-toggle {\n    @include caret(start);\n    &::before {\n      vertical-align: 0;\n    }\n  }\n}\n\n\n// Dividers (basically an `<hr>`) within the dropdown\n.dropdown-divider {\n  height: 0;\n  margin: var(--#{$prefix}dropdown-divider-margin-y) 0;\n  overflow: hidden;\n  border-top: 1px solid var(--#{$prefix}dropdown-divider-bg);\n  opacity: 1; // Revisit in v6 to de-dupe styles that conflict with <hr> element\n}\n\n// Links, buttons, and more within the dropdown menu\n//\n// `<button>`-specific styles are denoted with `// For <button>s`\n.dropdown-item {\n  display: block;\n  width: 100%; // For `<button>`s\n  padding: var(--#{$prefix}dropdown-item-padding-y) var(--#{$prefix}dropdown-item-padding-x);\n  clear: both;\n  font-weight: $font-weight-normal;\n  color: var(--#{$prefix}dropdown-link-color);\n  text-align: inherit; // For `<button>`s\n  text-decoration: if($link-decoration == none, null, none);\n  white-space: nowrap; // prevent links from randomly breaking onto new lines\n  background-color: transparent; // For `<button>`s\n  border: 0; // For `<button>`s\n  @include border-radius(var(--#{$prefix}dropdown-item-border-radius, 0));\n\n  &:hover,\n  &:focus {\n    color: var(--#{$prefix}dropdown-link-hover-color);\n    text-decoration: if($link-hover-decoration == underline, none, null);\n    @include gradient-bg(var(--#{$prefix}dropdown-link-hover-bg));\n  }\n\n  &.active,\n  &:active {\n    color: var(--#{$prefix}dropdown-link-active-color);\n    text-decoration: none;\n    @include gradient-bg(var(--#{$prefix}dropdown-link-active-bg));\n  }\n\n  &.disabled,\n  &:disabled {\n    color: var(--#{$prefix}dropdown-link-disabled-color);\n    pointer-events: none;\n    background-color: transparent;\n    // Remove CSS gradients if they're enabled\n    background-image: if($enable-gradients, none, null);\n  }\n}\n\n.dropdown-menu.show {\n  display: block;\n}\n\n// Dropdown section headers\n.dropdown-header {\n  display: block;\n  padding: var(--#{$prefix}dropdown-header-padding-y) var(--#{$prefix}dropdown-header-padding-x);\n  margin-bottom: 0; // for use with heading elements\n  @include font-size($font-size-sm);\n  color: var(--#{$prefix}dropdown-header-color);\n  white-space: nowrap; // as with > li > a\n}\n\n// Dropdown text\n.dropdown-item-text {\n  display: block;\n  padding: var(--#{$prefix}dropdown-item-padding-y) var(--#{$prefix}dropdown-item-padding-x);\n  color: var(--#{$prefix}dropdown-link-color);\n}\n\n// Dark dropdowns\n.dropdown-menu-dark {\n  // scss-docs-start dropdown-dark-css-vars\n  --#{$prefix}dropdown-color: #{$dropdown-dark-color};\n  --#{$prefix}dropdown-bg: #{$dropdown-dark-bg};\n  --#{$prefix}dropdown-border-color: #{$dropdown-dark-border-color};\n  --#{$prefix}dropdown-box-shadow: #{$dropdown-dark-box-shadow};\n  --#{$prefix}dropdown-link-color: #{$dropdown-dark-link-color};\n  --#{$prefix}dropdown-link-hover-color: #{$dropdown-dark-link-hover-color};\n  --#{$prefix}dropdown-divider-bg: #{$dropdown-dark-divider-bg};\n  --#{$prefix}dropdown-link-hover-bg: #{$dropdown-dark-link-hover-bg};\n  --#{$prefix}dropdown-link-active-color: #{$dropdown-dark-link-active-color};\n  --#{$prefix}dropdown-link-active-bg: #{$dropdown-dark-link-active-bg};\n  --#{$prefix}dropdown-link-disabled-color: #{$dropdown-dark-link-disabled-color};\n  --#{$prefix}dropdown-header-color: #{$dropdown-dark-header-color};\n  // scss-docs-end dropdown-dark-css-vars\n}\n", "// scss-docs-start caret-mixins\n@mixin caret-down($width: $caret-width) {\n  border-top: $width solid;\n  border-right: $width solid transparent;\n  border-bottom: 0;\n  border-left: $width solid transparent;\n}\n\n@mixin caret-up($width: $caret-width) {\n  border-top: 0;\n  border-right: $width solid transparent;\n  border-bottom: $width solid;\n  border-left: $width solid transparent;\n}\n\n@mixin caret-end($width: $caret-width) {\n  border-top: $width solid transparent;\n  border-right: 0;\n  border-bottom: $width solid transparent;\n  border-left: $width solid;\n}\n\n@mixin caret-start($width: $caret-width) {\n  border-top: $width solid transparent;\n  border-right: $width solid;\n  border-bottom: $width solid transparent;\n}\n\n@mixin caret(\n  $direction: down,\n  $width: $caret-width,\n  $spacing: $caret-spacing,\n  $vertical-align: $caret-vertical-align\n) {\n  @if $enable-caret {\n    &::after {\n      display: inline-block;\n      margin-left: $spacing;\n      vertical-align: $vertical-align;\n      content: \"\";\n      @if $direction == down {\n        @include caret-down($width);\n      } @else if $direction == up {\n        @include caret-up($width);\n      } @else if $direction == end {\n        @include caret-end($width);\n      }\n    }\n\n    @if $direction == start {\n      &::after {\n        display: none;\n      }\n\n      &::before {\n        display: inline-block;\n        margin-right: $spacing;\n        vertical-align: $vertical-align;\n        content: \"\";\n        @include caret-start($width);\n      }\n    }\n\n    &:empty::after {\n      margin-left: 0;\n    }\n  }\n}\n// scss-docs-end caret-mixins\n", "// Make the div behave like a button\n.btn-group,\n.btn-group-vertical {\n  position: relative;\n  display: inline-flex;\n  vertical-align: middle; // match .btn alignment given font-size hack above\n\n  > .btn {\n    position: relative;\n    flex: 1 1 auto;\n  }\n\n  // Bring the hover, focused, and \"active\" buttons to the front to overlay\n  // the borders properly\n  > .btn-check:checked + .btn,\n  > .btn-check:focus + .btn,\n  > .btn:hover,\n  > .btn:focus,\n  > .btn:active,\n  > .btn.active {\n    z-index: 1;\n  }\n}\n\n// Optional: Group multiple button groups together for a toolbar\n.btn-toolbar {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n\n  .input-group {\n    width: auto;\n  }\n}\n\n.btn-group {\n  @include border-radius($btn-border-radius);\n\n  // Prevent double borders when buttons are next to each other\n  > :not(.btn-check:first-child) + .btn,\n  > .btn-group:not(:first-child) {\n    margin-left: calc(#{$btn-border-width} * -1); // stylelint-disable-line function-disallowed-list\n  }\n\n  // Reset rounded corners\n  > .btn:not(:last-child):not(.dropdown-toggle),\n  > .btn.dropdown-toggle-split:first-child,\n  > .btn-group:not(:last-child) > .btn {\n    @include border-end-radius(0);\n  }\n\n  // The left radius should be 0 if the button is:\n  // - the \"third or more\" child\n  // - the second child and the previous element isn't `.btn-check` (making it the first child visually)\n  // - part of a btn-group which isn't the first child\n  > .btn:nth-child(n + 3),\n  > :not(.btn-check) + .btn,\n  > .btn-group:not(:first-child) > .btn {\n    @include border-start-radius(0);\n  }\n}\n\n// Sizing\n//\n// Remix the default button sizing classes into new ones for easier manipulation.\n\n.btn-group-sm > .btn { @extend .btn-sm; }\n.btn-group-lg > .btn { @extend .btn-lg; }\n\n\n//\n// Split button dropdowns\n//\n\n.dropdown-toggle-split {\n  padding-right: $btn-padding-x * .75;\n  padding-left: $btn-padding-x * .75;\n\n  &::after,\n  .dropup &::after,\n  .dropend &::after {\n    margin-left: 0;\n  }\n\n  .dropstart &::before {\n    margin-right: 0;\n  }\n}\n\n.btn-sm + .dropdown-toggle-split {\n  padding-right: $btn-padding-x-sm * .75;\n  padding-left: $btn-padding-x-sm * .75;\n}\n\n.btn-lg + .dropdown-toggle-split {\n  padding-right: $btn-padding-x-lg * .75;\n  padding-left: $btn-padding-x-lg * .75;\n}\n\n\n// The clickable button for toggling the menu\n// Set the same inset shadow as the :active state\n.btn-group.show .dropdown-toggle {\n  @include box-shadow($btn-active-box-shadow);\n\n  // Show no shadow for `.btn-link` since it has no other button styles.\n  &.btn-link {\n    @include box-shadow(none);\n  }\n}\n\n\n//\n// Vertical button groups\n//\n\n.btn-group-vertical {\n  flex-direction: column;\n  align-items: flex-start;\n  justify-content: center;\n\n  > .btn,\n  > .btn-group {\n    width: 100%;\n  }\n\n  > .btn:not(:first-child),\n  > .btn-group:not(:first-child) {\n    margin-top: calc(#{$btn-border-width} * -1); // stylelint-disable-line function-disallowed-list\n  }\n\n  // Reset rounded corners\n  > .btn:not(:last-child):not(.dropdown-toggle),\n  > .btn-group:not(:last-child) > .btn {\n    @include border-bottom-radius(0);\n  }\n\n  > .btn ~ .btn,\n  > .btn-group:not(:first-child) > .btn {\n    @include border-top-radius(0);\n  }\n}\n", "// Base class\n//\n// Kickstart any navigation component with a set of style resets. Works with\n// `<nav>`s, `<ul>`s or `<ol>`s.\n\n.nav {\n  // scss-docs-start nav-css-vars\n  --#{$prefix}nav-link-padding-x: #{$nav-link-padding-x};\n  --#{$prefix}nav-link-padding-y: #{$nav-link-padding-y};\n  @include rfs($nav-link-font-size, --#{$prefix}nav-link-font-size);\n  --#{$prefix}nav-link-font-weight: #{$nav-link-font-weight};\n  --#{$prefix}nav-link-color: #{$nav-link-color};\n  --#{$prefix}nav-link-hover-color: #{$nav-link-hover-color};\n  --#{$prefix}nav-link-disabled-color: #{$nav-link-disabled-color};\n  // scss-docs-end nav-css-vars\n\n  display: flex;\n  flex-wrap: wrap;\n  padding-left: 0;\n  margin-bottom: 0;\n  list-style: none;\n}\n\n.nav-link {\n  display: block;\n  padding: var(--#{$prefix}nav-link-padding-y) var(--#{$prefix}nav-link-padding-x);\n  @include font-size(var(--#{$prefix}nav-link-font-size));\n  font-weight: var(--#{$prefix}nav-link-font-weight);\n  color: var(--#{$prefix}nav-link-color);\n  text-decoration: if($link-decoration == none, null, none);\n  background: none;\n  border: 0;\n  @include transition($nav-link-transition);\n\n  &:hover,\n  &:focus {\n    color: var(--#{$prefix}nav-link-hover-color);\n    text-decoration: if($link-hover-decoration == underline, none, null);\n  }\n\n  &:focus-visible {\n    outline: 0;\n    box-shadow: $nav-link-focus-box-shadow;\n  }\n\n  // Disabled state lightens text\n  &.disabled,\n  &:disabled {\n    color: var(--#{$prefix}nav-link-disabled-color);\n    pointer-events: none;\n    cursor: default;\n  }\n}\n\n//\n// Tabs\n//\n\n.nav-tabs {\n  // scss-docs-start nav-tabs-css-vars\n  --#{$prefix}nav-tabs-border-width: #{$nav-tabs-border-width};\n  --#{$prefix}nav-tabs-border-color: #{$nav-tabs-border-color};\n  --#{$prefix}nav-tabs-border-radius: #{$nav-tabs-border-radius};\n  --#{$prefix}nav-tabs-link-hover-border-color: #{$nav-tabs-link-hover-border-color};\n  --#{$prefix}nav-tabs-link-active-color: #{$nav-tabs-link-active-color};\n  --#{$prefix}nav-tabs-link-active-bg: #{$nav-tabs-link-active-bg};\n  --#{$prefix}nav-tabs-link-active-border-color: #{$nav-tabs-link-active-border-color};\n  // scss-docs-end nav-tabs-css-vars\n\n  border-bottom: var(--#{$prefix}nav-tabs-border-width) solid var(--#{$prefix}nav-tabs-border-color);\n\n  .nav-link {\n    margin-bottom: calc(-1 * var(--#{$prefix}nav-tabs-border-width)); // stylelint-disable-line function-disallowed-list\n    border: var(--#{$prefix}nav-tabs-border-width) solid transparent;\n    @include border-top-radius(var(--#{$prefix}nav-tabs-border-radius));\n\n    &:hover,\n    &:focus {\n      // Prevents active .nav-link tab overlapping focus outline of previous/next .nav-link\n      isolation: isolate;\n      border-color: var(--#{$prefix}nav-tabs-link-hover-border-color);\n    }\n  }\n\n  .nav-link.active,\n  .nav-item.show .nav-link {\n    color: var(--#{$prefix}nav-tabs-link-active-color);\n    background-color: var(--#{$prefix}nav-tabs-link-active-bg);\n    border-color: var(--#{$prefix}nav-tabs-link-active-border-color);\n  }\n\n  .dropdown-menu {\n    // Make dropdown border overlap tab border\n    margin-top: calc(-1 * var(--#{$prefix}nav-tabs-border-width)); // stylelint-disable-line function-disallowed-list\n    // Remove the top rounded corners here since there is a hard edge above the menu\n    @include border-top-radius(0);\n  }\n}\n\n\n//\n// Pills\n//\n\n.nav-pills {\n  // scss-docs-start nav-pills-css-vars\n  --#{$prefix}nav-pills-border-radius: #{$nav-pills-border-radius};\n  --#{$prefix}nav-pills-link-active-color: #{$nav-pills-link-active-color};\n  --#{$prefix}nav-pills-link-active-bg: #{$nav-pills-link-active-bg};\n  // scss-docs-end nav-pills-css-vars\n\n  .nav-link {\n    @include border-radius(var(--#{$prefix}nav-pills-border-radius));\n  }\n\n  .nav-link.active,\n  .show > .nav-link {\n    color: var(--#{$prefix}nav-pills-link-active-color);\n    @include gradient-bg(var(--#{$prefix}nav-pills-link-active-bg));\n  }\n}\n\n\n//\n// Underline\n//\n\n.nav-underline {\n  // scss-docs-start nav-underline-css-vars\n  --#{$prefix}nav-underline-gap: #{$nav-underline-gap};\n  --#{$prefix}nav-underline-border-width: #{$nav-underline-border-width};\n  --#{$prefix}nav-underline-link-active-color: #{$nav-underline-link-active-color};\n  // scss-docs-end nav-underline-css-vars\n\n  gap: var(--#{$prefix}nav-underline-gap);\n\n  .nav-link {\n    padding-right: 0;\n    padding-left: 0;\n    border-bottom: var(--#{$prefix}nav-underline-border-width) solid transparent;\n\n    &:hover,\n    &:focus {\n      border-bottom-color: currentcolor;\n    }\n  }\n\n  .nav-link.active,\n  .show > .nav-link {\n    font-weight: $font-weight-bold;\n    color: var(--#{$prefix}nav-underline-link-active-color);\n    border-bottom-color: currentcolor;\n  }\n}\n\n\n//\n// Justified variants\n//\n\n.nav-fill {\n  > .nav-link,\n  .nav-item {\n    flex: 1 1 auto;\n    text-align: center;\n  }\n}\n\n.nav-justified {\n  > .nav-link,\n  .nav-item {\n    flex-basis: 0;\n    flex-grow: 1;\n    text-align: center;\n  }\n}\n\n.nav-fill,\n.nav-justified {\n  .nav-item .nav-link {\n    width: 100%; // Make sure button will grow\n  }\n}\n\n\n// Tabbable tabs\n//\n// Hide tabbable panes to start, show them when `.active`\n\n.tab-content {\n  > .tab-pane {\n    display: none;\n  }\n  > .active {\n    display: block;\n  }\n}\n", "// Navbar\n//\n// Provide a static navbar from which we expand to create full-width, fixed, and\n// other navbar variations.\n\n.navbar {\n  // scss-docs-start navbar-css-vars\n  --#{$prefix}navbar-padding-x: #{if($navbar-padding-x == null, 0, $navbar-padding-x)};\n  --#{$prefix}navbar-padding-y: #{$navbar-padding-y};\n  --#{$prefix}navbar-color: #{$navbar-light-color};\n  --#{$prefix}navbar-hover-color: #{$navbar-light-hover-color};\n  --#{$prefix}navbar-disabled-color: #{$navbar-light-disabled-color};\n  --#{$prefix}navbar-active-color: #{$navbar-light-active-color};\n  --#{$prefix}navbar-brand-padding-y: #{$navbar-brand-padding-y};\n  --#{$prefix}navbar-brand-margin-end: #{$navbar-brand-margin-end};\n  --#{$prefix}navbar-brand-font-size: #{$navbar-brand-font-size};\n  --#{$prefix}navbar-brand-color: #{$navbar-light-brand-color};\n  --#{$prefix}navbar-brand-hover-color: #{$navbar-light-brand-hover-color};\n  --#{$prefix}navbar-nav-link-padding-x: #{$navbar-nav-link-padding-x};\n  --#{$prefix}navbar-toggler-padding-y: #{$navbar-toggler-padding-y};\n  --#{$prefix}navbar-toggler-padding-x: #{$navbar-toggler-padding-x};\n  --#{$prefix}navbar-toggler-font-size: #{$navbar-toggler-font-size};\n  --#{$prefix}navbar-toggler-icon-bg: #{escape-svg($navbar-light-toggler-icon-bg)};\n  --#{$prefix}navbar-toggler-border-color: #{$navbar-light-toggler-border-color};\n  --#{$prefix}navbar-toggler-border-radius: #{$navbar-toggler-border-radius};\n  --#{$prefix}navbar-toggler-focus-width: #{$navbar-toggler-focus-width};\n  --#{$prefix}navbar-toggler-transition: #{$navbar-toggler-transition};\n  // scss-docs-end navbar-css-vars\n\n  position: relative;\n  display: flex;\n  flex-wrap: wrap; // allow us to do the line break for collapsing content\n  align-items: center;\n  justify-content: space-between; // space out brand from logo\n  padding: var(--#{$prefix}navbar-padding-y) var(--#{$prefix}navbar-padding-x);\n  @include gradient-bg();\n\n  // Because flex properties aren't inherited, we need to redeclare these first\n  // few properties so that content nested within behave properly.\n  // The `flex-wrap` property is inherited to simplify the expanded navbars\n  %container-flex-properties {\n    display: flex;\n    flex-wrap: inherit;\n    align-items: center;\n    justify-content: space-between;\n  }\n\n  > .container,\n  > .container-fluid {\n    @extend %container-flex-properties;\n  }\n\n  @each $breakpoint, $container-max-width in $container-max-widths {\n    > .container#{breakpoint-infix($breakpoint, $container-max-widths)} {\n      @extend %container-flex-properties;\n    }\n  }\n}\n\n\n// Navbar brand\n//\n// Used for brand, project, or site names.\n\n.navbar-brand {\n  padding-top: var(--#{$prefix}navbar-brand-padding-y);\n  padding-bottom: var(--#{$prefix}navbar-brand-padding-y);\n  margin-right: var(--#{$prefix}navbar-brand-margin-end);\n  @include font-size(var(--#{$prefix}navbar-brand-font-size));\n  color: var(--#{$prefix}navbar-brand-color);\n  text-decoration: if($link-decoration == none, null, none);\n  white-space: nowrap;\n\n  &:hover,\n  &:focus {\n    color: var(--#{$prefix}navbar-brand-hover-color);\n    text-decoration: if($link-hover-decoration == underline, none, null);\n  }\n}\n\n\n// Navbar nav\n//\n// Custom navbar navigation (doesn't require `.nav`, but does make use of `.nav-link`).\n\n.navbar-nav {\n  // scss-docs-start navbar-nav-css-vars\n  --#{$prefix}nav-link-padding-x: 0;\n  --#{$prefix}nav-link-padding-y: #{$nav-link-padding-y};\n  @include rfs($nav-link-font-size, --#{$prefix}nav-link-font-size);\n  --#{$prefix}nav-link-font-weight: #{$nav-link-font-weight};\n  --#{$prefix}nav-link-color: var(--#{$prefix}navbar-color);\n  --#{$prefix}nav-link-hover-color: var(--#{$prefix}navbar-hover-color);\n  --#{$prefix}nav-link-disabled-color: var(--#{$prefix}navbar-disabled-color);\n  // scss-docs-end navbar-nav-css-vars\n\n  display: flex;\n  flex-direction: column; // cannot use `inherit` to get the `.navbar`s value\n  padding-left: 0;\n  margin-bottom: 0;\n  list-style: none;\n\n  .nav-link {\n    &.active,\n    &.show {\n      color: var(--#{$prefix}navbar-active-color);\n    }\n  }\n\n  .dropdown-menu {\n    position: static;\n  }\n}\n\n\n// Navbar text\n//\n//\n\n.navbar-text {\n  padding-top: $nav-link-padding-y;\n  padding-bottom: $nav-link-padding-y;\n  color: var(--#{$prefix}navbar-color);\n\n  a,\n  a:hover,\n  a:focus  {\n    color: var(--#{$prefix}navbar-active-color);\n  }\n}\n\n\n// Responsive navbar\n//\n// Custom styles for responsive collapsing and toggling of navbar contents.\n// Powered by the collapse Bootstrap JavaScript plugin.\n\n// When collapsed, prevent the toggleable navbar contents from appearing in\n// the default flexbox row orientation. Requires the use of `flex-wrap: wrap`\n// on the `.navbar` parent.\n.navbar-collapse {\n  flex-basis: 100%;\n  flex-grow: 1;\n  // For always expanded or extra full navbars, ensure content aligns itself\n  // properly vertically. Can be easily overridden with flex utilities.\n  align-items: center;\n}\n\n// Button for toggling the navbar when in its collapsed state\n.navbar-toggler {\n  padding: var(--#{$prefix}navbar-toggler-padding-y) var(--#{$prefix}navbar-toggler-padding-x);\n  @include font-size(var(--#{$prefix}navbar-toggler-font-size));\n  line-height: 1;\n  color: var(--#{$prefix}navbar-color);\n  background-color: transparent; // remove default button style\n  border: var(--#{$prefix}border-width) solid var(--#{$prefix}navbar-toggler-border-color); // remove default button style\n  @include border-radius(var(--#{$prefix}navbar-toggler-border-radius));\n  @include transition(var(--#{$prefix}navbar-toggler-transition));\n\n  &:hover {\n    text-decoration: none;\n  }\n\n  &:focus {\n    text-decoration: none;\n    outline: 0;\n    box-shadow: 0 0 0 var(--#{$prefix}navbar-toggler-focus-width);\n  }\n}\n\n// Keep as a separate element so folks can easily override it with another icon\n// or image file as needed.\n.navbar-toggler-icon {\n  display: inline-block;\n  width: 1.5em;\n  height: 1.5em;\n  vertical-align: middle;\n  background-image: var(--#{$prefix}navbar-toggler-icon-bg);\n  background-repeat: no-repeat;\n  background-position: center;\n  background-size: 100%;\n}\n\n.navbar-nav-scroll {\n  max-height: var(--#{$prefix}scroll-height, 75vh);\n  overflow-y: auto;\n}\n\n// scss-docs-start navbar-expand-loop\n// Generate series of `.navbar-expand-*` responsive classes for configuring\n// where your navbar collapses.\n.navbar-expand {\n  @each $breakpoint in map-keys($grid-breakpoints) {\n    $next: breakpoint-next($breakpoint, $grid-breakpoints);\n    $infix: breakpoint-infix($next, $grid-breakpoints);\n\n    // stylelint-disable-next-line scss/selector-no-union-class-name\n    &#{$infix} {\n      @include media-breakpoint-up($next) {\n        flex-wrap: nowrap;\n        justify-content: flex-start;\n\n        .navbar-nav {\n          flex-direction: row;\n\n          .dropdown-menu {\n            position: absolute;\n          }\n\n          .nav-link {\n            padding-right: var(--#{$prefix}navbar-nav-link-padding-x);\n            padding-left: var(--#{$prefix}navbar-nav-link-padding-x);\n          }\n        }\n\n        .navbar-nav-scroll {\n          overflow: visible;\n        }\n\n        .navbar-collapse {\n          display: flex !important; // stylelint-disable-line declaration-no-important\n          flex-basis: auto;\n        }\n\n        .navbar-toggler {\n          display: none;\n        }\n\n        .offcanvas {\n          // stylelint-disable declaration-no-important\n          position: static;\n          z-index: auto;\n          flex-grow: 1;\n          width: auto !important;\n          height: auto !important;\n          visibility: visible !important;\n          background-color: transparent !important;\n          border: 0 !important;\n          transform: none !important;\n          @include box-shadow(none);\n          @include transition(none);\n          // stylelint-enable declaration-no-important\n\n          .offcanvas-header {\n            display: none;\n          }\n\n          .offcanvas-body {\n            display: flex;\n            flex-grow: 0;\n            padding: 0;\n            overflow-y: visible;\n          }\n        }\n      }\n    }\n  }\n}\n// scss-docs-end navbar-expand-loop\n\n// Navbar themes\n//\n// Styles for switching between navbars with light or dark background.\n\n.navbar-light {\n  @include deprecate(\"`.navbar-light`\", \"v5.2.0\", \"v6.0.0\", true);\n}\n\n.navbar-dark,\n.navbar[data-bs-theme=\"dark\"] {\n  // scss-docs-start navbar-dark-css-vars\n  --#{$prefix}navbar-color: #{$navbar-dark-color};\n  --#{$prefix}navbar-hover-color: #{$navbar-dark-hover-color};\n  --#{$prefix}navbar-disabled-color: #{$navbar-dark-disabled-color};\n  --#{$prefix}navbar-active-color: #{$navbar-dark-active-color};\n  --#{$prefix}navbar-brand-color: #{$navbar-dark-brand-color};\n  --#{$prefix}navbar-brand-hover-color: #{$navbar-dark-brand-hover-color};\n  --#{$prefix}navbar-toggler-border-color: #{$navbar-dark-toggler-border-color};\n  --#{$prefix}navbar-toggler-icon-bg: #{escape-svg($navbar-dark-toggler-icon-bg)};\n  // scss-docs-end navbar-dark-css-vars\n}\n\n@if $enable-dark-mode {\n  @include color-mode(dark) {\n    .navbar-toggler-icon {\n      --#{$prefix}navbar-toggler-icon-bg: #{escape-svg($navbar-dark-toggler-icon-bg)};\n    }\n  }\n}\n", "//\n// Base styles\n//\n\n.card {\n  // scss-docs-start card-css-vars\n  --#{$prefix}card-spacer-y: #{$card-spacer-y};\n  --#{$prefix}card-spacer-x: #{$card-spacer-x};\n  --#{$prefix}card-title-spacer-y: #{$card-title-spacer-y};\n  --#{$prefix}card-title-color: #{$card-title-color};\n  --#{$prefix}card-subtitle-color: #{$card-subtitle-color};\n  --#{$prefix}card-border-width: #{$card-border-width};\n  --#{$prefix}card-border-color: #{$card-border-color};\n  --#{$prefix}card-border-radius: #{$card-border-radius};\n  --#{$prefix}card-box-shadow: #{$card-box-shadow};\n  --#{$prefix}card-inner-border-radius: #{$card-inner-border-radius};\n  --#{$prefix}card-cap-padding-y: #{$card-cap-padding-y};\n  --#{$prefix}card-cap-padding-x: #{$card-cap-padding-x};\n  --#{$prefix}card-cap-bg: #{$card-cap-bg};\n  --#{$prefix}card-cap-color: #{$card-cap-color};\n  --#{$prefix}card-height: #{$card-height};\n  --#{$prefix}card-color: #{$card-color};\n  --#{$prefix}card-bg: #{$card-bg};\n  --#{$prefix}card-img-overlay-padding: #{$card-img-overlay-padding};\n  --#{$prefix}card-group-margin: #{$card-group-margin};\n  // scss-docs-end card-css-vars\n\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  min-width: 0; // See https://github.com/twbs/bootstrap/pull/22740#issuecomment-305868106\n  height: var(--#{$prefix}card-height);\n  color: var(--#{$prefix}body-color);\n  word-wrap: break-word;\n  background-color: var(--#{$prefix}card-bg);\n  background-clip: border-box;\n  border: var(--#{$prefix}card-border-width) solid var(--#{$prefix}card-border-color);\n  @include border-radius(var(--#{$prefix}card-border-radius));\n  @include box-shadow(var(--#{$prefix}card-box-shadow));\n\n  > hr {\n    margin-right: 0;\n    margin-left: 0;\n  }\n\n  > .list-group {\n    border-top: inherit;\n    border-bottom: inherit;\n\n    &:first-child {\n      border-top-width: 0;\n      @include border-top-radius(var(--#{$prefix}card-inner-border-radius));\n    }\n\n    &:last-child  {\n      border-bottom-width: 0;\n      @include border-bottom-radius(var(--#{$prefix}card-inner-border-radius));\n    }\n  }\n\n  // Due to specificity of the above selector (`.card > .list-group`), we must\n  // use a child selector here to prevent double borders.\n  > .card-header + .list-group,\n  > .list-group + .card-footer {\n    border-top: 0;\n  }\n}\n\n.card-body {\n  // Enable `flex-grow: 1` for decks and groups so that card blocks take up\n  // as much space as possible, ensuring footers are aligned to the bottom.\n  flex: 1 1 auto;\n  padding: var(--#{$prefix}card-spacer-y) var(--#{$prefix}card-spacer-x);\n  color: var(--#{$prefix}card-color);\n}\n\n.card-title {\n  margin-bottom: var(--#{$prefix}card-title-spacer-y);\n  color: var(--#{$prefix}card-title-color);\n}\n\n.card-subtitle {\n  margin-top: calc(-.5 * var(--#{$prefix}card-title-spacer-y)); // stylelint-disable-line function-disallowed-list\n  margin-bottom: 0;\n  color: var(--#{$prefix}card-subtitle-color);\n}\n\n.card-text:last-child {\n  margin-bottom: 0;\n}\n\n.card-link {\n  &:hover {\n    text-decoration: if($link-hover-decoration == underline, none, null);\n  }\n\n  + .card-link {\n    margin-left: var(--#{$prefix}card-spacer-x);\n  }\n}\n\n//\n// Optional textual caps\n//\n\n.card-header {\n  padding: var(--#{$prefix}card-cap-padding-y) var(--#{$prefix}card-cap-padding-x);\n  margin-bottom: 0; // Removes the default margin-bottom of <hN>\n  color: var(--#{$prefix}card-cap-color);\n  background-color: var(--#{$prefix}card-cap-bg);\n  border-bottom: var(--#{$prefix}card-border-width) solid var(--#{$prefix}card-border-color);\n\n  &:first-child {\n    @include border-radius(var(--#{$prefix}card-inner-border-radius) var(--#{$prefix}card-inner-border-radius) 0 0);\n  }\n}\n\n.card-footer {\n  padding: var(--#{$prefix}card-cap-padding-y) var(--#{$prefix}card-cap-padding-x);\n  color: var(--#{$prefix}card-cap-color);\n  background-color: var(--#{$prefix}card-cap-bg);\n  border-top: var(--#{$prefix}card-border-width) solid var(--#{$prefix}card-border-color);\n\n  &:last-child {\n    @include border-radius(0 0 var(--#{$prefix}card-inner-border-radius) var(--#{$prefix}card-inner-border-radius));\n  }\n}\n\n\n//\n// Header navs\n//\n\n.card-header-tabs {\n  margin-right: calc(-.5 * var(--#{$prefix}card-cap-padding-x)); // stylelint-disable-line function-disallowed-list\n  margin-bottom: calc(-1 * var(--#{$prefix}card-cap-padding-y)); // stylelint-disable-line function-disallowed-list\n  margin-left: calc(-.5 * var(--#{$prefix}card-cap-padding-x)); // stylelint-disable-line function-disallowed-list\n  border-bottom: 0;\n\n  .nav-link.active {\n    background-color: var(--#{$prefix}card-bg);\n    border-bottom-color: var(--#{$prefix}card-bg);\n  }\n}\n\n.card-header-pills {\n  margin-right: calc(-.5 * var(--#{$prefix}card-cap-padding-x)); // stylelint-disable-line function-disallowed-list\n  margin-left: calc(-.5 * var(--#{$prefix}card-cap-padding-x)); // stylelint-disable-line function-disallowed-list\n}\n\n// Card image\n.card-img-overlay {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  padding: var(--#{$prefix}card-img-overlay-padding);\n  @include border-radius(var(--#{$prefix}card-inner-border-radius));\n}\n\n.card-img,\n.card-img-top,\n.card-img-bottom {\n  width: 100%; // Required because we use flexbox and this inherently applies align-self: stretch\n}\n\n.card-img,\n.card-img-top {\n  @include border-top-radius(var(--#{$prefix}card-inner-border-radius));\n}\n\n.card-img,\n.card-img-bottom {\n  @include border-bottom-radius(var(--#{$prefix}card-inner-border-radius));\n}\n\n\n//\n// Card groups\n//\n\n.card-group {\n  // The child selector allows nested `.card` within `.card-group`\n  // to display properly.\n  > .card {\n    margin-bottom: var(--#{$prefix}card-group-margin);\n  }\n\n  @include media-breakpoint-up(sm) {\n    display: flex;\n    flex-flow: row wrap;\n    // The child selector allows nested `.card` within `.card-group`\n    // to display properly.\n    > .card {\n      // Flexbugs #4: https://github.com/philipwalton/flexbugs#flexbug-4\n      flex: 1 0 0%;\n      margin-bottom: 0;\n\n      + .card {\n        margin-left: 0;\n        border-left: 0;\n      }\n\n      // Handle rounded corners\n      @if $enable-rounded {\n        &:not(:last-child) {\n          @include border-end-radius(0);\n\n          .card-img-top,\n          .card-header {\n            // stylelint-disable-next-line property-disallowed-list\n            border-top-right-radius: 0;\n          }\n          .card-img-bottom,\n          .card-footer {\n            // stylelint-disable-next-line property-disallowed-list\n            border-bottom-right-radius: 0;\n          }\n        }\n\n        &:not(:first-child) {\n          @include border-start-radius(0);\n\n          .card-img-top,\n          .card-header {\n            // stylelint-disable-next-line property-disallowed-list\n            border-top-left-radius: 0;\n          }\n          .card-img-bottom,\n          .card-footer {\n            // stylelint-disable-next-line property-disallowed-list\n            border-bottom-left-radius: 0;\n          }\n        }\n      }\n    }\n  }\n}\n", "//\n// Base styles\n//\n\n.accordion {\n  // scss-docs-start accordion-css-vars\n  --#{$prefix}accordion-color: #{$accordion-color};\n  --#{$prefix}accordion-bg: #{$accordion-bg};\n  --#{$prefix}accordion-transition: #{$accordion-transition};\n  --#{$prefix}accordion-border-color: #{$accordion-border-color};\n  --#{$prefix}accordion-border-width: #{$accordion-border-width};\n  --#{$prefix}accordion-border-radius: #{$accordion-border-radius};\n  --#{$prefix}accordion-inner-border-radius: #{$accordion-inner-border-radius};\n  --#{$prefix}accordion-btn-padding-x: #{$accordion-button-padding-x};\n  --#{$prefix}accordion-btn-padding-y: #{$accordion-button-padding-y};\n  --#{$prefix}accordion-btn-color: #{$accordion-button-color};\n  --#{$prefix}accordion-btn-bg: #{$accordion-button-bg};\n  --#{$prefix}accordion-btn-icon: #{escape-svg($accordion-button-icon)};\n  --#{$prefix}accordion-btn-icon-width: #{$accordion-icon-width};\n  --#{$prefix}accordion-btn-icon-transform: #{$accordion-icon-transform};\n  --#{$prefix}accordion-btn-icon-transition: #{$accordion-icon-transition};\n  --#{$prefix}accordion-btn-active-icon: #{escape-svg($accordion-button-active-icon)};\n  --#{$prefix}accordion-btn-focus-box-shadow: #{$accordion-button-focus-box-shadow};\n  --#{$prefix}accordion-body-padding-x: #{$accordion-body-padding-x};\n  --#{$prefix}accordion-body-padding-y: #{$accordion-body-padding-y};\n  --#{$prefix}accordion-active-color: #{$accordion-button-active-color};\n  --#{$prefix}accordion-active-bg: #{$accordion-button-active-bg};\n  // scss-docs-end accordion-css-vars\n}\n\n.accordion-button {\n  position: relative;\n  display: flex;\n  align-items: center;\n  width: 100%;\n  padding: var(--#{$prefix}accordion-btn-padding-y) var(--#{$prefix}accordion-btn-padding-x);\n  @include font-size($font-size-base);\n  color: var(--#{$prefix}accordion-btn-color);\n  text-align: left; // Reset button style\n  background-color: var(--#{$prefix}accordion-btn-bg);\n  border: 0;\n  @include border-radius(0);\n  overflow-anchor: none;\n  @include transition(var(--#{$prefix}accordion-transition));\n\n  &:not(.collapsed) {\n    color: var(--#{$prefix}accordion-active-color);\n    background-color: var(--#{$prefix}accordion-active-bg);\n    box-shadow: inset 0 calc(-1 * var(--#{$prefix}accordion-border-width)) 0 var(--#{$prefix}accordion-border-color); // stylelint-disable-line function-disallowed-list\n\n    &::after {\n      background-image: var(--#{$prefix}accordion-btn-active-icon);\n      transform: var(--#{$prefix}accordion-btn-icon-transform);\n    }\n  }\n\n  // Accordion icon\n  &::after {\n    flex-shrink: 0;\n    width: var(--#{$prefix}accordion-btn-icon-width);\n    height: var(--#{$prefix}accordion-btn-icon-width);\n    margin-left: auto;\n    content: \"\";\n    background-image: var(--#{$prefix}accordion-btn-icon);\n    background-repeat: no-repeat;\n    background-size: var(--#{$prefix}accordion-btn-icon-width);\n    @include transition(var(--#{$prefix}accordion-btn-icon-transition));\n  }\n\n  &:hover {\n    z-index: 2;\n  }\n\n  &:focus {\n    z-index: 3;\n    outline: 0;\n    box-shadow: var(--#{$prefix}accordion-btn-focus-box-shadow);\n  }\n}\n\n.accordion-header {\n  margin-bottom: 0;\n}\n\n.accordion-item {\n  color: var(--#{$prefix}accordion-color);\n  background-color: var(--#{$prefix}accordion-bg);\n  border: var(--#{$prefix}accordion-border-width) solid var(--#{$prefix}accordion-border-color);\n\n  &:first-of-type {\n    @include border-top-radius(var(--#{$prefix}accordion-border-radius));\n\n    > .accordion-header .accordion-button {\n      @include border-top-radius(var(--#{$prefix}accordion-inner-border-radius));\n    }\n  }\n\n  &:not(:first-of-type) {\n    border-top: 0;\n  }\n\n  // Only set a border-radius on the last item if the accordion is collapsed\n  &:last-of-type {\n    @include border-bottom-radius(var(--#{$prefix}accordion-border-radius));\n\n    > .accordion-header .accordion-button {\n      &.collapsed {\n        @include border-bottom-radius(var(--#{$prefix}accordion-inner-border-radius));\n      }\n    }\n\n    > .accordion-collapse {\n      @include border-bottom-radius(var(--#{$prefix}accordion-border-radius));\n    }\n  }\n}\n\n.accordion-body {\n  padding: var(--#{$prefix}accordion-body-padding-y) var(--#{$prefix}accordion-body-padding-x);\n}\n\n\n// Flush accordion items\n//\n// Remove borders and border-radius to keep accordion items edge-to-edge.\n\n.accordion-flush {\n  > .accordion-item {\n    border-right: 0;\n    border-left: 0;\n    @include border-radius(0);\n\n    &:first-child { border-top: 0; }\n    &:last-child { border-bottom: 0; }\n\n    // stylelint-disable selector-max-class\n    > .accordion-header .accordion-button {\n      &,\n      &.collapsed {\n        @include border-radius(0);\n      }\n    }\n    // stylelint-enable selector-max-class\n\n    > .accordion-collapse {\n      @include border-radius(0);\n    }\n  }\n}\n\n@if $enable-dark-mode {\n  @include color-mode(dark) {\n    .accordion-button::after {\n      --#{$prefix}accordion-btn-icon: #{escape-svg($accordion-button-icon-dark)};\n      --#{$prefix}accordion-btn-active-icon: #{escape-svg($accordion-button-active-icon-dark)};\n    }\n  }\n}\n", ".breadcrumb {\n  // scss-docs-start breadcrumb-css-vars\n  --#{$prefix}breadcrumb-padding-x: #{$breadcrumb-padding-x};\n  --#{$prefix}breadcrumb-padding-y: #{$breadcrumb-padding-y};\n  --#{$prefix}breadcrumb-margin-bottom: #{$breadcrumb-margin-bottom};\n  @include rfs($breadcrumb-font-size, --#{$prefix}breadcrumb-font-size);\n  --#{$prefix}breadcrumb-bg: #{$breadcrumb-bg};\n  --#{$prefix}breadcrumb-border-radius: #{$breadcrumb-border-radius};\n  --#{$prefix}breadcrumb-divider-color: #{$breadcrumb-divider-color};\n  --#{$prefix}breadcrumb-item-padding-x: #{$breadcrumb-item-padding-x};\n  --#{$prefix}breadcrumb-item-active-color: #{$breadcrumb-active-color};\n  // scss-docs-end breadcrumb-css-vars\n\n  display: flex;\n  flex-wrap: wrap;\n  padding: var(--#{$prefix}breadcrumb-padding-y) var(--#{$prefix}breadcrumb-padding-x);\n  margin-bottom: var(--#{$prefix}breadcrumb-margin-bottom);\n  @include font-size(var(--#{$prefix}breadcrumb-font-size));\n  list-style: none;\n  background-color: var(--#{$prefix}breadcrumb-bg);\n  @include border-radius(var(--#{$prefix}breadcrumb-border-radius));\n}\n\n.breadcrumb-item {\n  // The separator between breadcrumbs (by default, a forward-slash: \"/\")\n  + .breadcrumb-item {\n    padding-left: var(--#{$prefix}breadcrumb-item-padding-x);\n\n    &::before {\n      float: left; // Suppress inline spacings and underlining of the separator\n      padding-right: var(--#{$prefix}breadcrumb-item-padding-x);\n      color: var(--#{$prefix}breadcrumb-divider-color);\n      content: var(--#{$prefix}breadcrumb-divider, escape-svg($breadcrumb-divider)) #{\"/* rtl:\"} var(--#{$prefix}breadcrumb-divider, escape-svg($breadcrumb-divider-flipped)) #{\"*/\"};\n    }\n  }\n\n  &.active {\n    color: var(--#{$prefix}breadcrumb-item-active-color);\n  }\n}\n", ".pagination {\n  // scss-docs-start pagination-css-vars\n  --#{$prefix}pagination-padding-x: #{$pagination-padding-x};\n  --#{$prefix}pagination-padding-y: #{$pagination-padding-y};\n  @include rfs($pagination-font-size, --#{$prefix}pagination-font-size);\n  --#{$prefix}pagination-color: #{$pagination-color};\n  --#{$prefix}pagination-bg: #{$pagination-bg};\n  --#{$prefix}pagination-border-width: #{$pagination-border-width};\n  --#{$prefix}pagination-border-color: #{$pagination-border-color};\n  --#{$prefix}pagination-border-radius: #{$pagination-border-radius};\n  --#{$prefix}pagination-hover-color: #{$pagination-hover-color};\n  --#{$prefix}pagination-hover-bg: #{$pagination-hover-bg};\n  --#{$prefix}pagination-hover-border-color: #{$pagination-hover-border-color};\n  --#{$prefix}pagination-focus-color: #{$pagination-focus-color};\n  --#{$prefix}pagination-focus-bg: #{$pagination-focus-bg};\n  --#{$prefix}pagination-focus-box-shadow: #{$pagination-focus-box-shadow};\n  --#{$prefix}pagination-active-color: #{$pagination-active-color};\n  --#{$prefix}pagination-active-bg: #{$pagination-active-bg};\n  --#{$prefix}pagination-active-border-color: #{$pagination-active-border-color};\n  --#{$prefix}pagination-disabled-color: #{$pagination-disabled-color};\n  --#{$prefix}pagination-disabled-bg: #{$pagination-disabled-bg};\n  --#{$prefix}pagination-disabled-border-color: #{$pagination-disabled-border-color};\n  // scss-docs-end pagination-css-vars\n\n  display: flex;\n  @include list-unstyled();\n}\n\n.page-link {\n  position: relative;\n  display: block;\n  padding: var(--#{$prefix}pagination-padding-y) var(--#{$prefix}pagination-padding-x);\n  @include font-size(var(--#{$prefix}pagination-font-size));\n  color: var(--#{$prefix}pagination-color);\n  text-decoration: if($link-decoration == none, null, none);\n  background-color: var(--#{$prefix}pagination-bg);\n  border: var(--#{$prefix}pagination-border-width) solid var(--#{$prefix}pagination-border-color);\n  @include transition($pagination-transition);\n\n  &:hover {\n    z-index: 2;\n    color: var(--#{$prefix}pagination-hover-color);\n    text-decoration: if($link-hover-decoration == underline, none, null);\n    background-color: var(--#{$prefix}pagination-hover-bg);\n    border-color: var(--#{$prefix}pagination-hover-border-color);\n  }\n\n  &:focus {\n    z-index: 3;\n    color: var(--#{$prefix}pagination-focus-color);\n    background-color: var(--#{$prefix}pagination-focus-bg);\n    outline: $pagination-focus-outline;\n    box-shadow: var(--#{$prefix}pagination-focus-box-shadow);\n  }\n\n  &.active,\n  .active > & {\n    z-index: 3;\n    color: var(--#{$prefix}pagination-active-color);\n    @include gradient-bg(var(--#{$prefix}pagination-active-bg));\n    border-color: var(--#{$prefix}pagination-active-border-color);\n  }\n\n  &.disabled,\n  .disabled > & {\n    color: var(--#{$prefix}pagination-disabled-color);\n    pointer-events: none;\n    background-color: var(--#{$prefix}pagination-disabled-bg);\n    border-color: var(--#{$prefix}pagination-disabled-border-color);\n  }\n}\n\n.page-item {\n  &:not(:first-child) .page-link {\n    margin-left: $pagination-margin-start;\n  }\n\n  @if $pagination-margin-start == calc(#{$pagination-border-width} * -1) {\n    &:first-child {\n      .page-link {\n        @include border-start-radius(var(--#{$prefix}pagination-border-radius));\n      }\n    }\n\n    &:last-child {\n      .page-link {\n        @include border-end-radius(var(--#{$prefix}pagination-border-radius));\n      }\n    }\n  } @else {\n    // Add border-radius to all pageLinks in case they have left margin\n    .page-link {\n      @include border-radius(var(--#{$prefix}pagination-border-radius));\n    }\n  }\n}\n\n\n//\n// Sizing\n//\n\n.pagination-lg {\n  @include pagination-size($pagination-padding-y-lg, $pagination-padding-x-lg, $font-size-lg, $pagination-border-radius-lg);\n}\n\n.pagination-sm {\n  @include pagination-size($pagination-padding-y-sm, $pagination-padding-x-sm, $font-size-sm, $pagination-border-radius-sm);\n}\n", "// Pagination\n\n// scss-docs-start pagination-mixin\n@mixin pagination-size($padding-y, $padding-x, $font-size, $border-radius) {\n  --#{$prefix}pagination-padding-x: #{$padding-x};\n  --#{$prefix}pagination-padding-y: #{$padding-y};\n  @include rfs($font-size, --#{$prefix}pagination-font-size);\n  --#{$prefix}pagination-border-radius: #{$border-radius};\n}\n// scss-docs-end pagination-mixin\n", "// Base class\n//\n// Requires one of the contextual, color modifier classes for `color` and\n// `background-color`.\n\n.badge {\n  // scss-docs-start badge-css-vars\n  --#{$prefix}badge-padding-x: #{$badge-padding-x};\n  --#{$prefix}badge-padding-y: #{$badge-padding-y};\n  @include rfs($badge-font-size, --#{$prefix}badge-font-size);\n  --#{$prefix}badge-font-weight: #{$badge-font-weight};\n  --#{$prefix}badge-color: #{$badge-color};\n  --#{$prefix}badge-border-radius: #{$badge-border-radius};\n  // scss-docs-end badge-css-vars\n\n  display: inline-block;\n  padding: var(--#{$prefix}badge-padding-y) var(--#{$prefix}badge-padding-x);\n  @include font-size(var(--#{$prefix}badge-font-size));\n  font-weight: var(--#{$prefix}badge-font-weight);\n  line-height: 1;\n  color: var(--#{$prefix}badge-color);\n  text-align: center;\n  white-space: nowrap;\n  vertical-align: baseline;\n  @include border-radius(var(--#{$prefix}badge-border-radius));\n  @include gradient-bg();\n\n  // Empty badges collapse automatically\n  &:empty {\n    display: none;\n  }\n}\n\n// Quick fix for badges in buttons\n.btn .badge {\n  position: relative;\n  top: -1px;\n}\n", "//\n// Base styles\n//\n\n.alert {\n  // scss-docs-start alert-css-vars\n  --#{$prefix}alert-bg: transparent;\n  --#{$prefix}alert-padding-x: #{$alert-padding-x};\n  --#{$prefix}alert-padding-y: #{$alert-padding-y};\n  --#{$prefix}alert-margin-bottom: #{$alert-margin-bottom};\n  --#{$prefix}alert-color: inherit;\n  --#{$prefix}alert-border-color: transparent;\n  --#{$prefix}alert-border: #{$alert-border-width} solid var(--#{$prefix}alert-border-color);\n  --#{$prefix}alert-border-radius: #{$alert-border-radius};\n  --#{$prefix}alert-link-color: inherit;\n  // scss-docs-end alert-css-vars\n\n  position: relative;\n  padding: var(--#{$prefix}alert-padding-y) var(--#{$prefix}alert-padding-x);\n  margin-bottom: var(--#{$prefix}alert-margin-bottom);\n  color: var(--#{$prefix}alert-color);\n  background-color: var(--#{$prefix}alert-bg);\n  border: var(--#{$prefix}alert-border);\n  @include border-radius(var(--#{$prefix}alert-border-radius));\n}\n\n// Headings for larger alerts\n.alert-heading {\n  // Specified to prevent conflicts of changing $headings-color\n  color: inherit;\n}\n\n// Provide class for links that match alerts\n.alert-link {\n  font-weight: $alert-link-font-weight;\n  color: var(--#{$prefix}alert-link-color);\n}\n\n\n// Dismissible alerts\n//\n// Expand the right padding and account for the close button's positioning.\n\n.alert-dismissible {\n  padding-right: $alert-dismissible-padding-r;\n\n  // Adjust close link position\n  .btn-close {\n    position: absolute;\n    top: 0;\n    right: 0;\n    z-index: $stretched-link-z-index + 1;\n    padding: $alert-padding-y * 1.25 $alert-padding-x;\n  }\n}\n\n\n// scss-docs-start alert-modifiers\n// Generate contextual modifier classes for colorizing the alert\n@each $state in map-keys($theme-colors) {\n  .alert-#{$state} {\n    --#{$prefix}alert-color: var(--#{$prefix}#{$state}-text-emphasis);\n    --#{$prefix}alert-bg: var(--#{$prefix}#{$state}-bg-subtle);\n    --#{$prefix}alert-border-color: var(--#{$prefix}#{$state}-border-subtle);\n    --#{$prefix}alert-link-color: var(--#{$prefix}#{$state}-text-emphasis);\n  }\n}\n// scss-docs-end alert-modifiers\n", "// Disable animation if transitions are disabled\n\n// scss-docs-start progress-keyframes\n@if $enable-transitions {\n  @keyframes progress-bar-stripes {\n    0% { background-position-x: $progress-height; }\n  }\n}\n// scss-docs-end progress-keyframes\n\n.progress,\n.progress-stacked {\n  // scss-docs-start progress-css-vars\n  --#{$prefix}progress-height: #{$progress-height};\n  @include rfs($progress-font-size, --#{$prefix}progress-font-size);\n  --#{$prefix}progress-bg: #{$progress-bg};\n  --#{$prefix}progress-border-radius: #{$progress-border-radius};\n  --#{$prefix}progress-box-shadow: #{$progress-box-shadow};\n  --#{$prefix}progress-bar-color: #{$progress-bar-color};\n  --#{$prefix}progress-bar-bg: #{$progress-bar-bg};\n  --#{$prefix}progress-bar-transition: #{$progress-bar-transition};\n  // scss-docs-end progress-css-vars\n\n  display: flex;\n  height: var(--#{$prefix}progress-height);\n  overflow: hidden; // force rounded corners by cropping it\n  @include font-size(var(--#{$prefix}progress-font-size));\n  background-color: var(--#{$prefix}progress-bg);\n  @include border-radius(var(--#{$prefix}progress-border-radius));\n  @include box-shadow(var(--#{$prefix}progress-box-shadow));\n}\n\n.progress-bar {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  overflow: hidden;\n  color: var(--#{$prefix}progress-bar-color);\n  text-align: center;\n  white-space: nowrap;\n  background-color: var(--#{$prefix}progress-bar-bg);\n  @include transition(var(--#{$prefix}progress-bar-transition));\n}\n\n.progress-bar-striped {\n  @include gradient-striped();\n  background-size: var(--#{$prefix}progress-height) var(--#{$prefix}progress-height);\n}\n\n.progress-stacked > .progress {\n  overflow: visible;\n}\n\n.progress-stacked > .progress > .progress-bar {\n  width: 100%;\n}\n\n@if $enable-transitions {\n  .progress-bar-animated {\n    animation: $progress-bar-animation-timing progress-bar-stripes;\n\n    @if $enable-reduced-motion {\n      @media (prefers-reduced-motion: reduce) {\n        animation: none;\n      }\n    }\n  }\n}\n", "// Base class\n//\n// Easily usable on <ul>, <ol>, or <div>.\n\n.list-group {\n  // scss-docs-start list-group-css-vars\n  --#{$prefix}list-group-color: #{$list-group-color};\n  --#{$prefix}list-group-bg: #{$list-group-bg};\n  --#{$prefix}list-group-border-color: #{$list-group-border-color};\n  --#{$prefix}list-group-border-width: #{$list-group-border-width};\n  --#{$prefix}list-group-border-radius: #{$list-group-border-radius};\n  --#{$prefix}list-group-item-padding-x: #{$list-group-item-padding-x};\n  --#{$prefix}list-group-item-padding-y: #{$list-group-item-padding-y};\n  --#{$prefix}list-group-action-color: #{$list-group-action-color};\n  --#{$prefix}list-group-action-hover-color: #{$list-group-action-hover-color};\n  --#{$prefix}list-group-action-hover-bg: #{$list-group-hover-bg};\n  --#{$prefix}list-group-action-active-color: #{$list-group-action-active-color};\n  --#{$prefix}list-group-action-active-bg: #{$list-group-action-active-bg};\n  --#{$prefix}list-group-disabled-color: #{$list-group-disabled-color};\n  --#{$prefix}list-group-disabled-bg: #{$list-group-disabled-bg};\n  --#{$prefix}list-group-active-color: #{$list-group-active-color};\n  --#{$prefix}list-group-active-bg: #{$list-group-active-bg};\n  --#{$prefix}list-group-active-border-color: #{$list-group-active-border-color};\n  // scss-docs-end list-group-css-vars\n\n  display: flex;\n  flex-direction: column;\n\n  // No need to set list-style: none; since .list-group-item is block level\n  padding-left: 0; // reset padding because ul and ol\n  margin-bottom: 0;\n  @include border-radius(var(--#{$prefix}list-group-border-radius));\n}\n\n.list-group-numbered {\n  list-style-type: none;\n  counter-reset: section;\n\n  > .list-group-item::before {\n    // Increments only this instance of the section counter\n    content: counters(section, \".\") \". \";\n    counter-increment: section;\n  }\n}\n\n// Interactive list items\n//\n// Use anchor or button elements instead of `li`s or `div`s to create interactive\n// list items. Includes an extra `.active` modifier class for selected items.\n\n.list-group-item-action {\n  width: 100%; // For `<button>`s (anchors become 100% by default though)\n  color: var(--#{$prefix}list-group-action-color);\n  text-align: inherit; // For `<button>`s (anchors inherit)\n\n  // Hover state\n  &:hover,\n  &:focus {\n    z-index: 1; // Place hover/focus items above their siblings for proper border styling\n    color: var(--#{$prefix}list-group-action-hover-color);\n    text-decoration: none;\n    background-color: var(--#{$prefix}list-group-action-hover-bg);\n  }\n\n  &:active {\n    color: var(--#{$prefix}list-group-action-active-color);\n    background-color: var(--#{$prefix}list-group-action-active-bg);\n  }\n}\n\n// Individual list items\n//\n// Use on `li`s or `div`s within the `.list-group` parent.\n\n.list-group-item {\n  position: relative;\n  display: block;\n  padding: var(--#{$prefix}list-group-item-padding-y) var(--#{$prefix}list-group-item-padding-x);\n  color: var(--#{$prefix}list-group-color);\n  text-decoration: if($link-decoration == none, null, none);\n  background-color: var(--#{$prefix}list-group-bg);\n  border: var(--#{$prefix}list-group-border-width) solid var(--#{$prefix}list-group-border-color);\n\n  &:first-child {\n    @include border-top-radius(inherit);\n  }\n\n  &:last-child {\n    @include border-bottom-radius(inherit);\n  }\n\n  &.disabled,\n  &:disabled {\n    color: var(--#{$prefix}list-group-disabled-color);\n    pointer-events: none;\n    background-color: var(--#{$prefix}list-group-disabled-bg);\n  }\n\n  // Include both here for `<a>`s and `<button>`s\n  &.active {\n    z-index: 2; // Place active items above their siblings for proper border styling\n    color: var(--#{$prefix}list-group-active-color);\n    background-color: var(--#{$prefix}list-group-active-bg);\n    border-color: var(--#{$prefix}list-group-active-border-color);\n  }\n\n  // stylelint-disable-next-line scss/selector-no-redundant-nesting-selector\n  & + .list-group-item {\n    border-top-width: 0;\n\n    &.active {\n      margin-top: calc(-1 * var(--#{$prefix}list-group-border-width)); // stylelint-disable-line function-disallowed-list\n      border-top-width: var(--#{$prefix}list-group-border-width);\n    }\n  }\n}\n\n// Horizontal\n//\n// Change the layout of list group items from vertical (default) to horizontal.\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .list-group-horizontal#{$infix} {\n      flex-direction: row;\n\n      > .list-group-item {\n        &:first-child:not(:last-child) {\n          @include border-bottom-start-radius(var(--#{$prefix}list-group-border-radius));\n          @include border-top-end-radius(0);\n        }\n\n        &:last-child:not(:first-child) {\n          @include border-top-end-radius(var(--#{$prefix}list-group-border-radius));\n          @include border-bottom-start-radius(0);\n        }\n\n        &.active {\n          margin-top: 0;\n        }\n\n        + .list-group-item {\n          border-top-width: var(--#{$prefix}list-group-border-width);\n          border-left-width: 0;\n\n          &.active {\n            margin-left: calc(-1 * var(--#{$prefix}list-group-border-width)); // stylelint-disable-line function-disallowed-list\n            border-left-width: var(--#{$prefix}list-group-border-width);\n          }\n        }\n      }\n    }\n  }\n}\n\n\n// Flush list items\n//\n// Remove borders and border-radius to keep list group items edge-to-edge. Most\n// useful within other components (e.g., cards).\n\n.list-group-flush {\n  @include border-radius(0);\n\n  > .list-group-item {\n    border-width: 0 0 var(--#{$prefix}list-group-border-width);\n\n    &:last-child {\n      border-bottom-width: 0;\n    }\n  }\n}\n\n\n// scss-docs-start list-group-modifiers\n// List group contextual variants\n//\n// Add modifier classes to change text and background color on individual items.\n// Organizationally, this must come after the `:hover` states.\n\n@each $state in map-keys($theme-colors) {\n  .list-group-item-#{$state} {\n    --#{$prefix}list-group-color: var(--#{$prefix}#{$state}-text-emphasis);\n    --#{$prefix}list-group-bg: var(--#{$prefix}#{$state}-bg-subtle);\n    --#{$prefix}list-group-border-color: var(--#{$prefix}#{$state}-border-subtle);\n    --#{$prefix}list-group-action-hover-color: var(--#{$prefix}emphasis-color);\n    --#{$prefix}list-group-action-hover-bg: var(--#{$prefix}#{$state}-border-subtle);\n    --#{$prefix}list-group-action-active-color: var(--#{$prefix}emphasis-color);\n    --#{$prefix}list-group-action-active-bg: var(--#{$prefix}#{$state}-border-subtle);\n    --#{$prefix}list-group-active-color: var(--#{$prefix}#{$state}-bg-subtle);\n    --#{$prefix}list-group-active-bg: var(--#{$prefix}#{$state}-text-emphasis);\n    --#{$prefix}list-group-active-border-color: var(--#{$prefix}#{$state}-text-emphasis);\n  }\n}\n// scss-docs-end list-group-modifiers\n", "// Transparent background and border properties included for button version.\n// iOS requires the button element instead of an anchor tag.\n// If you want the anchor version, it requires `href=\"#\"`.\n// See https://developer.mozilla.org/en-US/docs/Web/Events/click#Safari_Mobile\n\n.btn-close {\n  // scss-docs-start close-css-vars\n  --#{$prefix}btn-close-color: #{$btn-close-color};\n  --#{$prefix}btn-close-bg: #{ escape-svg($btn-close-bg) };\n  --#{$prefix}btn-close-opacity: #{$btn-close-opacity};\n  --#{$prefix}btn-close-hover-opacity: #{$btn-close-hover-opacity};\n  --#{$prefix}btn-close-focus-shadow: #{$btn-close-focus-shadow};\n  --#{$prefix}btn-close-focus-opacity: #{$btn-close-focus-opacity};\n  --#{$prefix}btn-close-disabled-opacity: #{$btn-close-disabled-opacity};\n  --#{$prefix}btn-close-white-filter: #{$btn-close-white-filter};\n  // scss-docs-end close-css-vars\n\n  box-sizing: content-box;\n  width: $btn-close-width;\n  height: $btn-close-height;\n  padding: $btn-close-padding-y $btn-close-padding-x;\n  color: var(--#{$prefix}btn-close-color);\n  background: transparent var(--#{$prefix}btn-close-bg) center / $btn-close-width auto no-repeat; // include transparent for button elements\n  border: 0; // for button elements\n  @include border-radius();\n  opacity: var(--#{$prefix}btn-close-opacity);\n\n  // Override <a>'s hover style\n  &:hover {\n    color: var(--#{$prefix}btn-close-color);\n    text-decoration: none;\n    opacity: var(--#{$prefix}btn-close-hover-opacity);\n  }\n\n  &:focus {\n    outline: 0;\n    box-shadow: var(--#{$prefix}btn-close-focus-shadow);\n    opacity: var(--#{$prefix}btn-close-focus-opacity);\n  }\n\n  &:disabled,\n  &.disabled {\n    pointer-events: none;\n    user-select: none;\n    opacity: var(--#{$prefix}btn-close-disabled-opacity);\n  }\n}\n\n@mixin btn-close-white() {\n  filter: var(--#{$prefix}btn-close-white-filter);\n}\n\n.btn-close-white {\n  @include btn-close-white();\n}\n\n@if $enable-dark-mode {\n  @include color-mode(dark) {\n    .btn-close {\n      @include btn-close-white();\n    }\n  }\n}\n", ".toast {\n  // scss-docs-start toast-css-vars\n  --#{$prefix}toast-zindex: #{$zindex-toast};\n  --#{$prefix}toast-padding-x: #{$toast-padding-x};\n  --#{$prefix}toast-padding-y: #{$toast-padding-y};\n  --#{$prefix}toast-spacing: #{$toast-spacing};\n  --#{$prefix}toast-max-width: #{$toast-max-width};\n  @include rfs($toast-font-size, --#{$prefix}toast-font-size);\n  --#{$prefix}toast-color: #{$toast-color};\n  --#{$prefix}toast-bg: #{$toast-background-color};\n  --#{$prefix}toast-border-width: #{$toast-border-width};\n  --#{$prefix}toast-border-color: #{$toast-border-color};\n  --#{$prefix}toast-border-radius: #{$toast-border-radius};\n  --#{$prefix}toast-box-shadow: #{$toast-box-shadow};\n  --#{$prefix}toast-header-color: #{$toast-header-color};\n  --#{$prefix}toast-header-bg: #{$toast-header-background-color};\n  --#{$prefix}toast-header-border-color: #{$toast-header-border-color};\n  // scss-docs-end toast-css-vars\n\n  width: var(--#{$prefix}toast-max-width);\n  max-width: 100%;\n  @include font-size(var(--#{$prefix}toast-font-size));\n  color: var(--#{$prefix}toast-color);\n  pointer-events: auto;\n  background-color: var(--#{$prefix}toast-bg);\n  background-clip: padding-box;\n  border: var(--#{$prefix}toast-border-width) solid var(--#{$prefix}toast-border-color);\n  box-shadow: var(--#{$prefix}toast-box-shadow);\n  @include border-radius(var(--#{$prefix}toast-border-radius));\n\n  &.showing {\n    opacity: 0;\n  }\n\n  &:not(.show) {\n    display: none;\n  }\n}\n\n.toast-container {\n  --#{$prefix}toast-zindex: #{$zindex-toast};\n\n  position: absolute;\n  z-index: var(--#{$prefix}toast-zindex);\n  width: max-content;\n  max-width: 100%;\n  pointer-events: none;\n\n  > :not(:last-child) {\n    margin-bottom: var(--#{$prefix}toast-spacing);\n  }\n}\n\n.toast-header {\n  display: flex;\n  align-items: center;\n  padding: var(--#{$prefix}toast-padding-y) var(--#{$prefix}toast-padding-x);\n  color: var(--#{$prefix}toast-header-color);\n  background-color: var(--#{$prefix}toast-header-bg);\n  background-clip: padding-box;\n  border-bottom: var(--#{$prefix}toast-border-width) solid var(--#{$prefix}toast-header-border-color);\n  @include border-top-radius(calc(var(--#{$prefix}toast-border-radius) - var(--#{$prefix}toast-border-width)));\n\n  .btn-close {\n    margin-right: calc(-.5 * var(--#{$prefix}toast-padding-x)); // stylelint-disable-line function-disallowed-list\n    margin-left: var(--#{$prefix}toast-padding-x);\n  }\n}\n\n.toast-body {\n  padding: var(--#{$prefix}toast-padding-x);\n  word-wrap: break-word;\n}\n", "// stylelint-disable function-disallowed-list\n\n// .modal-open      - body class for killing the scroll\n// .modal           - container to scroll within\n// .modal-dialog    - positioning shell for the actual modal\n// .modal-content   - actual modal w/ bg and corners and stuff\n\n\n// Container that the modal scrolls within\n.modal {\n  // scss-docs-start modal-css-vars\n  --#{$prefix}modal-zindex: #{$zindex-modal};\n  --#{$prefix}modal-width: #{$modal-md};\n  --#{$prefix}modal-padding: #{$modal-inner-padding};\n  --#{$prefix}modal-margin: #{$modal-dialog-margin};\n  --#{$prefix}modal-color: #{$modal-content-color};\n  --#{$prefix}modal-bg: #{$modal-content-bg};\n  --#{$prefix}modal-border-color: #{$modal-content-border-color};\n  --#{$prefix}modal-border-width: #{$modal-content-border-width};\n  --#{$prefix}modal-border-radius: #{$modal-content-border-radius};\n  --#{$prefix}modal-box-shadow: #{$modal-content-box-shadow-xs};\n  --#{$prefix}modal-inner-border-radius: #{$modal-content-inner-border-radius};\n  --#{$prefix}modal-header-padding-x: #{$modal-header-padding-x};\n  --#{$prefix}modal-header-padding-y: #{$modal-header-padding-y};\n  --#{$prefix}modal-header-padding: #{$modal-header-padding}; // Todo in v6: Split this padding into x and y\n  --#{$prefix}modal-header-border-color: #{$modal-header-border-color};\n  --#{$prefix}modal-header-border-width: #{$modal-header-border-width};\n  --#{$prefix}modal-title-line-height: #{$modal-title-line-height};\n  --#{$prefix}modal-footer-gap: #{$modal-footer-margin-between};\n  --#{$prefix}modal-footer-bg: #{$modal-footer-bg};\n  --#{$prefix}modal-footer-border-color: #{$modal-footer-border-color};\n  --#{$prefix}modal-footer-border-width: #{$modal-footer-border-width};\n  // scss-docs-end modal-css-vars\n\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: var(--#{$prefix}modal-zindex);\n  display: none;\n  width: 100%;\n  height: 100%;\n  overflow-x: hidden;\n  overflow-y: auto;\n  // Prevent Chrome on Windows from adding a focus outline. For details, see\n  // https://github.com/twbs/bootstrap/pull/10951.\n  outline: 0;\n  // We deliberately don't use `-webkit-overflow-scrolling: touch;` due to a\n  // gnarly iOS Safari bug: https://bugs.webkit.org/show_bug.cgi?id=158342\n  // See also https://github.com/twbs/bootstrap/issues/17695\n}\n\n// Shell div to position the modal with bottom padding\n.modal-dialog {\n  position: relative;\n  width: auto;\n  margin: var(--#{$prefix}modal-margin);\n  // allow clicks to pass through for custom click handling to close modal\n  pointer-events: none;\n\n  // When fading in the modal, animate it to slide down\n  .modal.fade & {\n    @include transition($modal-transition);\n    transform: $modal-fade-transform;\n  }\n  .modal.show & {\n    transform: $modal-show-transform;\n  }\n\n  // When trying to close, animate focus to scale\n  .modal.modal-static & {\n    transform: $modal-scale-transform;\n  }\n}\n\n.modal-dialog-scrollable {\n  height: calc(100% - var(--#{$prefix}modal-margin) * 2);\n\n  .modal-content {\n    max-height: 100%;\n    overflow: hidden;\n  }\n\n  .modal-body {\n    overflow-y: auto;\n  }\n}\n\n.modal-dialog-centered {\n  display: flex;\n  align-items: center;\n  min-height: calc(100% - var(--#{$prefix}modal-margin) * 2);\n}\n\n// Actual modal\n.modal-content {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  width: 100%; // Ensure `.modal-content` extends the full width of the parent `.modal-dialog`\n  // counteract the pointer-events: none; in the .modal-dialog\n  color: var(--#{$prefix}modal-color);\n  pointer-events: auto;\n  background-color: var(--#{$prefix}modal-bg);\n  background-clip: padding-box;\n  border: var(--#{$prefix}modal-border-width) solid var(--#{$prefix}modal-border-color);\n  @include border-radius(var(--#{$prefix}modal-border-radius));\n  @include box-shadow(var(--#{$prefix}modal-box-shadow));\n  // Remove focus outline from opened modal\n  outline: 0;\n}\n\n// Modal background\n.modal-backdrop {\n  // scss-docs-start modal-backdrop-css-vars\n  --#{$prefix}backdrop-zindex: #{$zindex-modal-backdrop};\n  --#{$prefix}backdrop-bg: #{$modal-backdrop-bg};\n  --#{$prefix}backdrop-opacity: #{$modal-backdrop-opacity};\n  // scss-docs-end modal-backdrop-css-vars\n\n  @include overlay-backdrop(var(--#{$prefix}backdrop-zindex), var(--#{$prefix}backdrop-bg), var(--#{$prefix}backdrop-opacity));\n}\n\n// Modal header\n// Top section of the modal w/ title and dismiss\n.modal-header {\n  display: flex;\n  flex-shrink: 0;\n  align-items: center;\n  padding: var(--#{$prefix}modal-header-padding);\n  border-bottom: var(--#{$prefix}modal-header-border-width) solid var(--#{$prefix}modal-header-border-color);\n  @include border-top-radius(var(--#{$prefix}modal-inner-border-radius));\n\n  .btn-close {\n    padding: calc(var(--#{$prefix}modal-header-padding-y) * .5) calc(var(--#{$prefix}modal-header-padding-x) * .5);\n    margin: calc(-.5 * var(--#{$prefix}modal-header-padding-y)) calc(-.5 * var(--#{$prefix}modal-header-padding-x)) calc(-.5 * var(--#{$prefix}modal-header-padding-y)) auto;\n  }\n}\n\n// Title text within header\n.modal-title {\n  margin-bottom: 0;\n  line-height: var(--#{$prefix}modal-title-line-height);\n}\n\n// Modal body\n// Where all modal content resides (sibling of .modal-header and .modal-footer)\n.modal-body {\n  position: relative;\n  // Enable `flex-grow: 1` so that the body take up as much space as possible\n  // when there should be a fixed height on `.modal-dialog`.\n  flex: 1 1 auto;\n  padding: var(--#{$prefix}modal-padding);\n}\n\n// Footer (for actions)\n.modal-footer {\n  display: flex;\n  flex-shrink: 0;\n  flex-wrap: wrap;\n  align-items: center; // vertically center\n  justify-content: flex-end; // Right align buttons with flex property because text-align doesn't work on flex items\n  padding: calc(var(--#{$prefix}modal-padding) - var(--#{$prefix}modal-footer-gap) * .5);\n  background-color: var(--#{$prefix}modal-footer-bg);\n  border-top: var(--#{$prefix}modal-footer-border-width) solid var(--#{$prefix}modal-footer-border-color);\n  @include border-bottom-radius(var(--#{$prefix}modal-inner-border-radius));\n\n  // Place margin between footer elements\n  // This solution is far from ideal because of the universal selector usage,\n  // but is needed to fix https://github.com/twbs/bootstrap/issues/24800\n  > * {\n    margin: calc(var(--#{$prefix}modal-footer-gap) * .5); // Todo in v6: replace with gap on parent class\n  }\n}\n\n// Scale up the modal\n@include media-breakpoint-up(sm) {\n  .modal {\n    --#{$prefix}modal-margin: #{$modal-dialog-margin-y-sm-up};\n    --#{$prefix}modal-box-shadow: #{$modal-content-box-shadow-sm-up};\n  }\n\n  // Automatically set modal's width for larger viewports\n  .modal-dialog {\n    max-width: var(--#{$prefix}modal-width);\n    margin-right: auto;\n    margin-left: auto;\n  }\n\n  .modal-sm {\n    --#{$prefix}modal-width: #{$modal-sm};\n  }\n}\n\n@include media-breakpoint-up(lg) {\n  .modal-lg,\n  .modal-xl {\n    --#{$prefix}modal-width: #{$modal-lg};\n  }\n}\n\n@include media-breakpoint-up(xl) {\n  .modal-xl {\n    --#{$prefix}modal-width: #{$modal-xl};\n  }\n}\n\n// scss-docs-start modal-fullscreen-loop\n@each $breakpoint in map-keys($grid-breakpoints) {\n  $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n  $postfix: if($infix != \"\", $infix + \"-down\", \"\");\n\n  @include media-breakpoint-down($breakpoint) {\n    .modal-fullscreen#{$postfix} {\n      width: 100vw;\n      max-width: none;\n      height: 100%;\n      margin: 0;\n\n      .modal-content {\n        height: 100%;\n        border: 0;\n        @include border-radius(0);\n      }\n\n      .modal-header,\n      .modal-footer {\n        @include border-radius(0);\n      }\n\n      .modal-body {\n        overflow-y: auto;\n      }\n    }\n  }\n}\n// scss-docs-end modal-fullscreen-loop\n", "// Shared between modals and offcanvases\n@mixin overlay-backdrop($zindex, $backdrop-bg, $backdrop-opacity) {\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: $zindex;\n  width: 100vw;\n  height: 100vh;\n  background-color: $backdrop-bg;\n\n  // Fade for backdrop\n  &.fade { opacity: 0; }\n  &.show { opacity: $backdrop-opacity; }\n}\n", "// Base class\n.tooltip {\n  // scss-docs-start tooltip-css-vars\n  --#{$prefix}tooltip-zindex: #{$zindex-tooltip};\n  --#{$prefix}tooltip-max-width: #{$tooltip-max-width};\n  --#{$prefix}tooltip-padding-x: #{$tooltip-padding-x};\n  --#{$prefix}tooltip-padding-y: #{$tooltip-padding-y};\n  --#{$prefix}tooltip-margin: #{$tooltip-margin};\n  @include rfs($tooltip-font-size, --#{$prefix}tooltip-font-size);\n  --#{$prefix}tooltip-color: #{$tooltip-color};\n  --#{$prefix}tooltip-bg: #{$tooltip-bg};\n  --#{$prefix}tooltip-border-radius: #{$tooltip-border-radius};\n  --#{$prefix}tooltip-opacity: #{$tooltip-opacity};\n  --#{$prefix}tooltip-arrow-width: #{$tooltip-arrow-width};\n  --#{$prefix}tooltip-arrow-height: #{$tooltip-arrow-height};\n  // scss-docs-end tooltip-css-vars\n\n  z-index: var(--#{$prefix}tooltip-zindex);\n  display: block;\n  margin: var(--#{$prefix}tooltip-margin);\n  @include deprecate(\"`$tooltip-margin`\", \"v5\", \"v5.x\", true);\n  // Our parent element can be arbitrary since tooltips are by default inserted as a sibling of their target element.\n  // So reset our font and text properties to avoid inheriting weird values.\n  @include reset-text();\n  @include font-size(var(--#{$prefix}tooltip-font-size));\n  // Allow breaking very long words so they don't overflow the tooltip's bounds\n  word-wrap: break-word;\n  opacity: 0;\n\n  &.show { opacity: var(--#{$prefix}tooltip-opacity); }\n\n  .tooltip-arrow {\n    display: block;\n    width: var(--#{$prefix}tooltip-arrow-width);\n    height: var(--#{$prefix}tooltip-arrow-height);\n\n    &::before {\n      position: absolute;\n      content: \"\";\n      border-color: transparent;\n      border-style: solid;\n    }\n  }\n}\n\n.bs-tooltip-top .tooltip-arrow {\n  bottom: calc(-1 * var(--#{$prefix}tooltip-arrow-height)); // stylelint-disable-line function-disallowed-list\n\n  &::before {\n    top: -1px;\n    border-width: var(--#{$prefix}tooltip-arrow-height) calc(var(--#{$prefix}tooltip-arrow-width) * .5) 0; // stylelint-disable-line function-disallowed-list\n    border-top-color: var(--#{$prefix}tooltip-bg);\n  }\n}\n\n/* rtl:begin:ignore */\n.bs-tooltip-end .tooltip-arrow {\n  left: calc(-1 * var(--#{$prefix}tooltip-arrow-height)); // stylelint-disable-line function-disallowed-list\n  width: var(--#{$prefix}tooltip-arrow-height);\n  height: var(--#{$prefix}tooltip-arrow-width);\n\n  &::before {\n    right: -1px;\n    border-width: calc(var(--#{$prefix}tooltip-arrow-width) * .5) var(--#{$prefix}tooltip-arrow-height) calc(var(--#{$prefix}tooltip-arrow-width) * .5) 0; // stylelint-disable-line function-disallowed-list\n    border-right-color: var(--#{$prefix}tooltip-bg);\n  }\n}\n\n/* rtl:end:ignore */\n\n.bs-tooltip-bottom .tooltip-arrow {\n  top: calc(-1 * var(--#{$prefix}tooltip-arrow-height)); // stylelint-disable-line function-disallowed-list\n\n  &::before {\n    bottom: -1px;\n    border-width: 0 calc(var(--#{$prefix}tooltip-arrow-width) * .5) var(--#{$prefix}tooltip-arrow-height); // stylelint-disable-line function-disallowed-list\n    border-bottom-color: var(--#{$prefix}tooltip-bg);\n  }\n}\n\n/* rtl:begin:ignore */\n.bs-tooltip-start .tooltip-arrow {\n  right: calc(-1 * var(--#{$prefix}tooltip-arrow-height)); // stylelint-disable-line function-disallowed-list\n  width: var(--#{$prefix}tooltip-arrow-height);\n  height: var(--#{$prefix}tooltip-arrow-width);\n\n  &::before {\n    left: -1px;\n    border-width: calc(var(--#{$prefix}tooltip-arrow-width) * .5) 0 calc(var(--#{$prefix}tooltip-arrow-width) * .5) var(--#{$prefix}tooltip-arrow-height); // stylelint-disable-line function-disallowed-list\n    border-left-color: var(--#{$prefix}tooltip-bg);\n  }\n}\n\n/* rtl:end:ignore */\n\n.bs-tooltip-auto {\n  &[data-popper-placement^=\"top\"] {\n    @extend .bs-tooltip-top;\n  }\n  &[data-popper-placement^=\"right\"] {\n    @extend .bs-tooltip-end;\n  }\n  &[data-popper-placement^=\"bottom\"] {\n    @extend .bs-tooltip-bottom;\n  }\n  &[data-popper-placement^=\"left\"] {\n    @extend .bs-tooltip-start;\n  }\n}\n\n// Wrapper for the tooltip content\n.tooltip-inner {\n  max-width: var(--#{$prefix}tooltip-max-width);\n  padding: var(--#{$prefix}tooltip-padding-y) var(--#{$prefix}tooltip-padding-x);\n  color: var(--#{$prefix}tooltip-color);\n  text-align: center;\n  background-color: var(--#{$prefix}tooltip-bg);\n  @include border-radius(var(--#{$prefix}tooltip-border-radius));\n}\n", "@mixin reset-text {\n  font-family: $font-family-base;\n  // We deliberately do NOT reset font-size or overflow-wrap / word-wrap.\n  font-style: normal;\n  font-weight: $font-weight-normal;\n  line-height: $line-height-base;\n  text-align: left; // Fallback for where `start` is not supported\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  letter-spacing: normal;\n  word-break: normal;\n  white-space: normal;\n  word-spacing: normal;\n  line-break: auto;\n}\n", ".popover {\n  // scss-docs-start popover-css-vars\n  --#{$prefix}popover-zindex: #{$zindex-popover};\n  --#{$prefix}popover-max-width: #{$popover-max-width};\n  @include rfs($popover-font-size, --#{$prefix}popover-font-size);\n  --#{$prefix}popover-bg: #{$popover-bg};\n  --#{$prefix}popover-border-width: #{$popover-border-width};\n  --#{$prefix}popover-border-color: #{$popover-border-color};\n  --#{$prefix}popover-border-radius: #{$popover-border-radius};\n  --#{$prefix}popover-inner-border-radius: #{$popover-inner-border-radius};\n  --#{$prefix}popover-box-shadow: #{$popover-box-shadow};\n  --#{$prefix}popover-header-padding-x: #{$popover-header-padding-x};\n  --#{$prefix}popover-header-padding-y: #{$popover-header-padding-y};\n  @include rfs($popover-header-font-size, --#{$prefix}popover-header-font-size);\n  --#{$prefix}popover-header-color: #{$popover-header-color};\n  --#{$prefix}popover-header-bg: #{$popover-header-bg};\n  --#{$prefix}popover-body-padding-x: #{$popover-body-padding-x};\n  --#{$prefix}popover-body-padding-y: #{$popover-body-padding-y};\n  --#{$prefix}popover-body-color: #{$popover-body-color};\n  --#{$prefix}popover-arrow-width: #{$popover-arrow-width};\n  --#{$prefix}popover-arrow-height: #{$popover-arrow-height};\n  --#{$prefix}popover-arrow-border: var(--#{$prefix}popover-border-color);\n  // scss-docs-end popover-css-vars\n\n  z-index: var(--#{$prefix}popover-zindex);\n  display: block;\n  max-width: var(--#{$prefix}popover-max-width);\n  // Our parent element can be arbitrary since tooltips are by default inserted as a sibling of their target element.\n  // So reset our font and text properties to avoid inheriting weird values.\n  @include reset-text();\n  @include font-size(var(--#{$prefix}popover-font-size));\n  // Allow breaking very long words so they don't overflow the popover's bounds\n  word-wrap: break-word;\n  background-color: var(--#{$prefix}popover-bg);\n  background-clip: padding-box;\n  border: var(--#{$prefix}popover-border-width) solid var(--#{$prefix}popover-border-color);\n  @include border-radius(var(--#{$prefix}popover-border-radius));\n  @include box-shadow(var(--#{$prefix}popover-box-shadow));\n\n  .popover-arrow {\n    display: block;\n    width: var(--#{$prefix}popover-arrow-width);\n    height: var(--#{$prefix}popover-arrow-height);\n\n    &::before,\n    &::after {\n      position: absolute;\n      display: block;\n      content: \"\";\n      border-color: transparent;\n      border-style: solid;\n      border-width: 0;\n    }\n  }\n}\n\n.bs-popover-top {\n  > .popover-arrow {\n    bottom: calc(-1 * (var(--#{$prefix}popover-arrow-height)) - var(--#{$prefix}popover-border-width)); // stylelint-disable-line function-disallowed-list\n\n    &::before,\n    &::after {\n      border-width: var(--#{$prefix}popover-arrow-height) calc(var(--#{$prefix}popover-arrow-width) * .5) 0; // stylelint-disable-line function-disallowed-list\n    }\n\n    &::before {\n      bottom: 0;\n      border-top-color: var(--#{$prefix}popover-arrow-border);\n    }\n\n    &::after {\n      bottom: var(--#{$prefix}popover-border-width);\n      border-top-color: var(--#{$prefix}popover-bg);\n    }\n  }\n}\n\n/* rtl:begin:ignore */\n.bs-popover-end {\n  > .popover-arrow {\n    left: calc(-1 * (var(--#{$prefix}popover-arrow-height)) - var(--#{$prefix}popover-border-width)); // stylelint-disable-line function-disallowed-list\n    width: var(--#{$prefix}popover-arrow-height);\n    height: var(--#{$prefix}popover-arrow-width);\n\n    &::before,\n    &::after {\n      border-width: calc(var(--#{$prefix}popover-arrow-width) * .5) var(--#{$prefix}popover-arrow-height) calc(var(--#{$prefix}popover-arrow-width) * .5) 0; // stylelint-disable-line function-disallowed-list\n    }\n\n    &::before {\n      left: 0;\n      border-right-color: var(--#{$prefix}popover-arrow-border);\n    }\n\n    &::after {\n      left: var(--#{$prefix}popover-border-width);\n      border-right-color: var(--#{$prefix}popover-bg);\n    }\n  }\n}\n\n/* rtl:end:ignore */\n\n.bs-popover-bottom {\n  > .popover-arrow {\n    top: calc(-1 * (var(--#{$prefix}popover-arrow-height)) - var(--#{$prefix}popover-border-width)); // stylelint-disable-line function-disallowed-list\n\n    &::before,\n    &::after {\n      border-width: 0 calc(var(--#{$prefix}popover-arrow-width) * .5) var(--#{$prefix}popover-arrow-height); // stylelint-disable-line function-disallowed-list\n    }\n\n    &::before {\n      top: 0;\n      border-bottom-color: var(--#{$prefix}popover-arrow-border);\n    }\n\n    &::after {\n      top: var(--#{$prefix}popover-border-width);\n      border-bottom-color: var(--#{$prefix}popover-bg);\n    }\n  }\n\n  // This will remove the popover-header's border just below the arrow\n  .popover-header::before {\n    position: absolute;\n    top: 0;\n    left: 50%;\n    display: block;\n    width: var(--#{$prefix}popover-arrow-width);\n    margin-left: calc(-.5 * var(--#{$prefix}popover-arrow-width)); // stylelint-disable-line function-disallowed-list\n    content: \"\";\n    border-bottom: var(--#{$prefix}popover-border-width) solid var(--#{$prefix}popover-header-bg);\n  }\n}\n\n/* rtl:begin:ignore */\n.bs-popover-start {\n  > .popover-arrow {\n    right: calc(-1 * (var(--#{$prefix}popover-arrow-height)) - var(--#{$prefix}popover-border-width)); // stylelint-disable-line function-disallowed-list\n    width: var(--#{$prefix}popover-arrow-height);\n    height: var(--#{$prefix}popover-arrow-width);\n\n    &::before,\n    &::after {\n      border-width: calc(var(--#{$prefix}popover-arrow-width) * .5) 0 calc(var(--#{$prefix}popover-arrow-width) * .5) var(--#{$prefix}popover-arrow-height); // stylelint-disable-line function-disallowed-list\n    }\n\n    &::before {\n      right: 0;\n      border-left-color: var(--#{$prefix}popover-arrow-border);\n    }\n\n    &::after {\n      right: var(--#{$prefix}popover-border-width);\n      border-left-color: var(--#{$prefix}popover-bg);\n    }\n  }\n}\n\n/* rtl:end:ignore */\n\n.bs-popover-auto {\n  &[data-popper-placement^=\"top\"] {\n    @extend .bs-popover-top;\n  }\n  &[data-popper-placement^=\"right\"] {\n    @extend .bs-popover-end;\n  }\n  &[data-popper-placement^=\"bottom\"] {\n    @extend .bs-popover-bottom;\n  }\n  &[data-popper-placement^=\"left\"] {\n    @extend .bs-popover-start;\n  }\n}\n\n// Offset the popover to account for the popover arrow\n.popover-header {\n  padding: var(--#{$prefix}popover-header-padding-y) var(--#{$prefix}popover-header-padding-x);\n  margin-bottom: 0; // Reset the default from Reboot\n  @include font-size(var(--#{$prefix}popover-header-font-size));\n  color: var(--#{$prefix}popover-header-color);\n  background-color: var(--#{$prefix}popover-header-bg);\n  border-bottom: var(--#{$prefix}popover-border-width) solid var(--#{$prefix}popover-border-color);\n  @include border-top-radius(var(--#{$prefix}popover-inner-border-radius));\n\n  &:empty {\n    display: none;\n  }\n}\n\n.popover-body {\n  padding: var(--#{$prefix}popover-body-padding-y) var(--#{$prefix}popover-body-padding-x);\n  color: var(--#{$prefix}popover-body-color);\n}\n", "// Notes on the classes:\n//\n// 1. .carousel.pointer-event should ideally be pan-y (to allow for users to scroll vertically)\n//    even when their scroll action started on a carousel, but for compatibility (with Firefox)\n//    we're preventing all actions instead\n// 2. The .carousel-item-start and .carousel-item-end is used to indicate where\n//    the active slide is heading.\n// 3. .active.carousel-item is the current slide.\n// 4. .active.carousel-item-start and .active.carousel-item-end is the current\n//    slide in its in-transition state. Only one of these occurs at a time.\n// 5. .carousel-item-next.carousel-item-start and .carousel-item-prev.carousel-item-end\n//    is the upcoming slide in transition.\n\n.carousel {\n  position: relative;\n}\n\n.carousel.pointer-event {\n  touch-action: pan-y;\n}\n\n.carousel-inner {\n  position: relative;\n  width: 100%;\n  overflow: hidden;\n  @include clearfix();\n}\n\n.carousel-item {\n  position: relative;\n  display: none;\n  float: left;\n  width: 100%;\n  margin-right: -100%;\n  backface-visibility: hidden;\n  @include transition($carousel-transition);\n}\n\n.carousel-item.active,\n.carousel-item-next,\n.carousel-item-prev {\n  display: block;\n}\n\n.carousel-item-next:not(.carousel-item-start),\n.active.carousel-item-end {\n  transform: translateX(100%);\n}\n\n.carousel-item-prev:not(.carousel-item-end),\n.active.carousel-item-start {\n  transform: translateX(-100%);\n}\n\n\n//\n// Alternate transitions\n//\n\n.carousel-fade {\n  .carousel-item {\n    opacity: 0;\n    transition-property: opacity;\n    transform: none;\n  }\n\n  .carousel-item.active,\n  .carousel-item-next.carousel-item-start,\n  .carousel-item-prev.carousel-item-end {\n    z-index: 1;\n    opacity: 1;\n  }\n\n  .active.carousel-item-start,\n  .active.carousel-item-end {\n    z-index: 0;\n    opacity: 0;\n    @include transition(opacity 0s $carousel-transition-duration);\n  }\n}\n\n\n//\n// Left/right controls for nav\n//\n\n.carousel-control-prev,\n.carousel-control-next {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  z-index: 1;\n  // Use flex for alignment (1-3)\n  display: flex; // 1. allow flex styles\n  align-items: center; // 2. vertically center contents\n  justify-content: center; // 3. horizontally center contents\n  width: $carousel-control-width;\n  padding: 0;\n  color: $carousel-control-color;\n  text-align: center;\n  background: none;\n  border: 0;\n  opacity: $carousel-control-opacity;\n  @include transition($carousel-control-transition);\n\n  // Hover/focus state\n  &:hover,\n  &:focus {\n    color: $carousel-control-color;\n    text-decoration: none;\n    outline: 0;\n    opacity: $carousel-control-hover-opacity;\n  }\n}\n.carousel-control-prev {\n  left: 0;\n  background-image: if($enable-gradients, linear-gradient(90deg, rgba($black, .25), rgba($black, .001)), null);\n}\n.carousel-control-next {\n  right: 0;\n  background-image: if($enable-gradients, linear-gradient(270deg, rgba($black, .25), rgba($black, .001)), null);\n}\n\n// Icons for within\n.carousel-control-prev-icon,\n.carousel-control-next-icon {\n  display: inline-block;\n  width: $carousel-control-icon-width;\n  height: $carousel-control-icon-width;\n  background-repeat: no-repeat;\n  background-position: 50%;\n  background-size: 100% 100%;\n}\n\n.carousel-control-prev-icon {\n  background-image: escape-svg($carousel-control-prev-icon-bg) #{\"/*rtl:\" + escape-svg($carousel-control-next-icon-bg) + \"*/\"};\n}\n.carousel-control-next-icon {\n  background-image: escape-svg($carousel-control-next-icon-bg) #{\"/*rtl:\" + escape-svg($carousel-control-prev-icon-bg) + \"*/\"};\n}\n\n// Optional indicator pips/controls\n//\n// Add a container (such as a list) with the following class and add an item (ideally a focusable control,\n// like a button) with data-bs-target for each slide your carousel holds.\n\n.carousel-indicators {\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 2;\n  display: flex;\n  justify-content: center;\n  padding: 0;\n  // Use the .carousel-control's width as margin so we don't overlay those\n  margin-right: $carousel-control-width;\n  margin-bottom: 1rem;\n  margin-left: $carousel-control-width;\n\n  [data-bs-target] {\n    box-sizing: content-box;\n    flex: 0 1 auto;\n    width: $carousel-indicator-width;\n    height: $carousel-indicator-height;\n    padding: 0;\n    margin-right: $carousel-indicator-spacer;\n    margin-left: $carousel-indicator-spacer;\n    text-indent: -999px;\n    cursor: pointer;\n    background-color: $carousel-indicator-active-bg;\n    background-clip: padding-box;\n    border: 0;\n    // Use transparent borders to increase the hit area by 10px on top and bottom.\n    border-top: $carousel-indicator-hit-area-height solid transparent;\n    border-bottom: $carousel-indicator-hit-area-height solid transparent;\n    opacity: $carousel-indicator-opacity;\n    @include transition($carousel-indicator-transition);\n  }\n\n  .active {\n    opacity: $carousel-indicator-active-opacity;\n  }\n}\n\n\n// Optional captions\n//\n//\n\n.carousel-caption {\n  position: absolute;\n  right: (100% - $carousel-caption-width) * .5;\n  bottom: $carousel-caption-spacer;\n  left: (100% - $carousel-caption-width) * .5;\n  padding-top: $carousel-caption-padding-y;\n  padding-bottom: $carousel-caption-padding-y;\n  color: $carousel-caption-color;\n  text-align: center;\n}\n\n// Dark mode carousel\n\n@mixin carousel-dark() {\n  .carousel-control-prev-icon,\n  .carousel-control-next-icon {\n    filter: $carousel-dark-control-icon-filter;\n  }\n\n  .carousel-indicators [data-bs-target] {\n    background-color: $carousel-dark-indicator-active-bg;\n  }\n\n  .carousel-caption {\n    color: $carousel-dark-caption-color;\n  }\n}\n\n.carousel-dark {\n  @include carousel-dark();\n}\n\n@if $enable-dark-mode {\n  @include color-mode(dark) {\n    @if $color-mode-type == \"media-query\" {\n      .carousel {\n        @include carousel-dark();\n      }\n    } @else {\n      .carousel,\n      &.carousel {\n        @include carousel-dark();\n      }\n    }\n  }\n}\n", "// scss-docs-start clearfix\n@mixin clearfix() {\n  &::after {\n    display: block;\n    clear: both;\n    content: \"\";\n  }\n}\n// scss-docs-end clearfix\n", "//\n// Rotating border\n//\n\n.spinner-grow,\n.spinner-border {\n  display: inline-block;\n  width: var(--#{$prefix}spinner-width);\n  height: var(--#{$prefix}spinner-height);\n  vertical-align: var(--#{$prefix}spinner-vertical-align);\n  // stylelint-disable-next-line property-disallowed-list\n  border-radius: 50%;\n  animation: var(--#{$prefix}spinner-animation-speed) linear infinite var(--#{$prefix}spinner-animation-name);\n}\n\n// scss-docs-start spinner-border-keyframes\n@keyframes spinner-border {\n  to { transform: rotate(360deg) #{\"/* rtl:ignore */\"}; }\n}\n// scss-docs-end spinner-border-keyframes\n\n.spinner-border {\n  // scss-docs-start spinner-border-css-vars\n  --#{$prefix}spinner-width: #{$spinner-width};\n  --#{$prefix}spinner-height: #{$spinner-height};\n  --#{$prefix}spinner-vertical-align: #{$spinner-vertical-align};\n  --#{$prefix}spinner-border-width: #{$spinner-border-width};\n  --#{$prefix}spinner-animation-speed: #{$spinner-animation-speed};\n  --#{$prefix}spinner-animation-name: spinner-border;\n  // scss-docs-end spinner-border-css-vars\n\n  border: var(--#{$prefix}spinner-border-width) solid currentcolor;\n  border-right-color: transparent;\n}\n\n.spinner-border-sm {\n  // scss-docs-start spinner-border-sm-css-vars\n  --#{$prefix}spinner-width: #{$spinner-width-sm};\n  --#{$prefix}spinner-height: #{$spinner-height-sm};\n  --#{$prefix}spinner-border-width: #{$spinner-border-width-sm};\n  // scss-docs-end spinner-border-sm-css-vars\n}\n\n//\n// Growing circle\n//\n\n// scss-docs-start spinner-grow-keyframes\n@keyframes spinner-grow {\n  0% {\n    transform: scale(0);\n  }\n  50% {\n    opacity: 1;\n    transform: none;\n  }\n}\n// scss-docs-end spinner-grow-keyframes\n\n.spinner-grow {\n  // scss-docs-start spinner-grow-css-vars\n  --#{$prefix}spinner-width: #{$spinner-width};\n  --#{$prefix}spinner-height: #{$spinner-height};\n  --#{$prefix}spinner-vertical-align: #{$spinner-vertical-align};\n  --#{$prefix}spinner-animation-speed: #{$spinner-animation-speed};\n  --#{$prefix}spinner-animation-name: spinner-grow;\n  // scss-docs-end spinner-grow-css-vars\n\n  background-color: currentcolor;\n  opacity: 0;\n}\n\n.spinner-grow-sm {\n  --#{$prefix}spinner-width: #{$spinner-width-sm};\n  --#{$prefix}spinner-height: #{$spinner-height-sm};\n}\n\n@if $enable-reduced-motion {\n  @media (prefers-reduced-motion: reduce) {\n    .spinner-border,\n    .spinner-grow {\n      --#{$prefix}spinner-animation-speed: #{$spinner-animation-speed * 2};\n    }\n  }\n}\n", "// stylelint-disable function-disallowed-list\n\n%offcanvas-css-vars {\n  // scss-docs-start offcanvas-css-vars\n  --#{$prefix}offcanvas-zindex: #{$zindex-offcanvas};\n  --#{$prefix}offcanvas-width: #{$offcanvas-horizontal-width};\n  --#{$prefix}offcanvas-height: #{$offcanvas-vertical-height};\n  --#{$prefix}offcanvas-padding-x: #{$offcanvas-padding-x};\n  --#{$prefix}offcanvas-padding-y: #{$offcanvas-padding-y};\n  --#{$prefix}offcanvas-color: #{$offcanvas-color};\n  --#{$prefix}offcanvas-bg: #{$offcanvas-bg-color};\n  --#{$prefix}offcanvas-border-width: #{$offcanvas-border-width};\n  --#{$prefix}offcanvas-border-color: #{$offcanvas-border-color};\n  --#{$prefix}offcanvas-box-shadow: #{$offcanvas-box-shadow};\n  --#{$prefix}offcanvas-transition: #{transform $offcanvas-transition-duration ease-in-out};\n  --#{$prefix}offcanvas-title-line-height: #{$offcanvas-title-line-height};\n  // scss-docs-end offcanvas-css-vars\n}\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  $next: breakpoint-next($breakpoint, $grid-breakpoints);\n  $infix: breakpoint-infix($next, $grid-breakpoints);\n\n  .offcanvas#{$infix} {\n    @extend %offcanvas-css-vars;\n  }\n}\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  $next: breakpoint-next($breakpoint, $grid-breakpoints);\n  $infix: breakpoint-infix($next, $grid-breakpoints);\n\n  .offcanvas#{$infix} {\n    @include media-breakpoint-down($next) {\n      position: fixed;\n      bottom: 0;\n      z-index: var(--#{$prefix}offcanvas-zindex);\n      display: flex;\n      flex-direction: column;\n      max-width: 100%;\n      color: var(--#{$prefix}offcanvas-color);\n      visibility: hidden;\n      background-color: var(--#{$prefix}offcanvas-bg);\n      background-clip: padding-box;\n      outline: 0;\n      @include box-shadow(var(--#{$prefix}offcanvas-box-shadow));\n      @include transition(var(--#{$prefix}offcanvas-transition));\n\n      &.offcanvas-start {\n        top: 0;\n        left: 0;\n        width: var(--#{$prefix}offcanvas-width);\n        border-right: var(--#{$prefix}offcanvas-border-width) solid var(--#{$prefix}offcanvas-border-color);\n        transform: translateX(-100%);\n      }\n\n      &.offcanvas-end {\n        top: 0;\n        right: 0;\n        width: var(--#{$prefix}offcanvas-width);\n        border-left: var(--#{$prefix}offcanvas-border-width) solid var(--#{$prefix}offcanvas-border-color);\n        transform: translateX(100%);\n      }\n\n      &.offcanvas-top {\n        top: 0;\n        right: 0;\n        left: 0;\n        height: var(--#{$prefix}offcanvas-height);\n        max-height: 100%;\n        border-bottom: var(--#{$prefix}offcanvas-border-width) solid var(--#{$prefix}offcanvas-border-color);\n        transform: translateY(-100%);\n      }\n\n      &.offcanvas-bottom {\n        right: 0;\n        left: 0;\n        height: var(--#{$prefix}offcanvas-height);\n        max-height: 100%;\n        border-top: var(--#{$prefix}offcanvas-border-width) solid var(--#{$prefix}offcanvas-border-color);\n        transform: translateY(100%);\n      }\n\n      &.showing,\n      &.show:not(.hiding) {\n        transform: none;\n      }\n\n      &.showing,\n      &.hiding,\n      &.show {\n        visibility: visible;\n      }\n    }\n\n    @if not ($infix == \"\") {\n      @include media-breakpoint-up($next) {\n        --#{$prefix}offcanvas-height: auto;\n        --#{$prefix}offcanvas-border-width: 0;\n        background-color: transparent !important; // stylelint-disable-line declaration-no-important\n\n        .offcanvas-header {\n          display: none;\n        }\n\n        .offcanvas-body {\n          display: flex;\n          flex-grow: 0;\n          padding: 0;\n          overflow-y: visible;\n          // Reset `background-color` in case `.bg-*` classes are used in offcanvas\n          background-color: transparent !important; // stylelint-disable-line declaration-no-important\n        }\n      }\n    }\n  }\n}\n\n.offcanvas-backdrop {\n  @include overlay-backdrop($zindex-offcanvas-backdrop, $offcanvas-backdrop-bg, $offcanvas-backdrop-opacity);\n}\n\n.offcanvas-header {\n  display: flex;\n  align-items: center;\n  padding: var(--#{$prefix}offcanvas-padding-y) var(--#{$prefix}offcanvas-padding-x);\n\n  .btn-close {\n    padding: calc(var(--#{$prefix}offcanvas-padding-y) * .5) calc(var(--#{$prefix}offcanvas-padding-x) * .5);\n    margin: calc(-.5 * var(--#{$prefix}offcanvas-padding-y)) calc(-.5 * var(--#{$prefix}offcanvas-padding-x)) calc(-.5 * var(--#{$prefix}offcanvas-padding-y)) auto;\n  }\n}\n\n.offcanvas-title {\n  margin-bottom: 0;\n  line-height: var(--#{$prefix}offcanvas-title-line-height);\n}\n\n.offcanvas-body {\n  flex-grow: 1;\n  padding: var(--#{$prefix}offcanvas-padding-y) var(--#{$prefix}offcanvas-padding-x);\n  overflow-y: auto;\n}\n", ".placeholder {\n  display: inline-block;\n  min-height: 1em;\n  vertical-align: middle;\n  cursor: wait;\n  background-color: currentcolor;\n  opacity: $placeholder-opacity-max;\n\n  &.btn::before {\n    display: inline-block;\n    content: \"\";\n  }\n}\n\n// Sizing\n.placeholder-xs {\n  min-height: .6em;\n}\n\n.placeholder-sm {\n  min-height: .8em;\n}\n\n.placeholder-lg {\n  min-height: 1.2em;\n}\n\n// Animation\n.placeholder-glow {\n  .placeholder {\n    animation: placeholder-glow 2s ease-in-out infinite;\n  }\n}\n\n@keyframes placeholder-glow {\n  50% {\n    opacity: $placeholder-opacity-min;\n  }\n}\n\n.placeholder-wave {\n  mask-image: linear-gradient(130deg, $black 55%, rgba(0, 0, 0, (1 - $placeholder-opacity-min)) 75%, $black 95%);\n  mask-size: 200% 100%;\n  animation: placeholder-wave 2s linear infinite;\n}\n\n@keyframes placeholder-wave {\n  100% {\n    mask-position: -200% 0%;\n  }\n}\n", "// All-caps `RGBA()` function used because of this Sass bug: https://github.com/sass/node-sass/issues/2251\n@each $color, $value in $theme-colors {\n  .text-bg-#{$color} {\n    color: color-contrast($value) if($enable-important-utilities, !important, null);\n    background-color: RGBA(var(--#{$prefix}#{$color}-rgb), var(--#{$prefix}bg-opacity, 1)) if($enable-important-utilities, !important, null);\n  }\n}\n", "// All-caps `RGBA()` function used because of this Sass bug: https://github.com/sass/node-sass/issues/2251\n@each $color, $value in $theme-colors {\n  .link-#{$color} {\n    color: RGBA(var(--#{$prefix}#{$color}-rgb), var(--#{$prefix}link-opacity, 1)) if($enable-important-utilities, !important, null);\n    text-decoration-color: RGBA(var(--#{$prefix}#{$color}-rgb), var(--#{$prefix}link-underline-opacity, 1)) if($enable-important-utilities, !important, null);\n\n    @if $link-shade-percentage != 0 {\n      &:hover,\n      &:focus {\n        $hover-color: if(color-contrast($value) == $color-contrast-light, shade-color($value, $link-shade-percentage), tint-color($value, $link-shade-percentage));\n        color: RGBA(#{to-rgb($hover-color)}, var(--#{$prefix}link-opacity, 1)) if($enable-important-utilities, !important, null);\n        text-decoration-color: RGBA(to-rgb($hover-color), var(--#{$prefix}link-underline-opacity, 1)) if($enable-important-utilities, !important, null);\n      }\n    }\n  }\n}\n\n// One-off special link helper as a bridge until v6\n.link-body-emphasis {\n  color: RGBA(var(--#{$prefix}emphasis-color-rgb), var(--#{$prefix}link-opacity, 1)) if($enable-important-utilities, !important, null);\n  text-decoration-color: RGBA(var(--#{$prefix}emphasis-color-rgb), var(--#{$prefix}link-underline-opacity, 1)) if($enable-important-utilities, !important, null);\n\n  @if $link-shade-percentage != 0 {\n    &:hover,\n    &:focus {\n      color: RGBA(var(--#{$prefix}emphasis-color-rgb), var(--#{$prefix}link-opacity, .75)) if($enable-important-utilities, !important, null);\n      text-decoration-color: RGBA(var(--#{$prefix}emphasis-color-rgb), var(--#{$prefix}link-underline-opacity, .75)) if($enable-important-utilities, !important, null);\n    }\n  }\n}\n", ".focus-ring:focus {\n  outline: 0;\n  // By default, there is no `--bs-focus-ring-x`, `--bs-focus-ring-y`, or `--bs-focus-ring-blur`, but we provide CSS variables with fallbacks to initial `0` values\n  box-shadow: var(--#{$prefix}focus-ring-x, 0) var(--#{$prefix}focus-ring-y, 0) var(--#{$prefix}focus-ring-blur, 0) var(--#{$prefix}focus-ring-width) var(--#{$prefix}focus-ring-color);\n}\n", ".icon-link {\n  display: inline-flex;\n  gap: $icon-link-gap;\n  align-items: center;\n  text-decoration-color: rgba(var(--#{$prefix}link-color-rgb), var(--#{$prefix}link-opacity, .5));\n  text-underline-offset: $icon-link-underline-offset;\n  backface-visibility: hidden;\n\n  > .bi {\n    flex-shrink: 0;\n    width: $icon-link-icon-size;\n    height: $icon-link-icon-size;\n    fill: currentcolor;\n    @include transition($icon-link-icon-transition);\n  }\n}\n\n.icon-link-hover {\n  &:hover,\n  &:focus-visible {\n    > .bi {\n      transform: var(--#{$prefix}icon-link-transform, $icon-link-icon-transform);\n    }\n  }\n}\n", "// Credit: <PERSON> and <PERSON><PERSON><PERSON> CSS.\n\n.ratio {\n  position: relative;\n  width: 100%;\n\n  &::before {\n    display: block;\n    padding-top: var(--#{$prefix}aspect-ratio);\n    content: \"\";\n  }\n\n  > * {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n  }\n}\n\n@each $key, $ratio in $aspect-ratios {\n  .ratio-#{$key} {\n    --#{$prefix}aspect-ratio: #{$ratio};\n  }\n}\n", "// Shorthand\n\n.fixed-top {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n.fixed-bottom {\n  position: fixed;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n// Responsive sticky top and bottom\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .sticky#{$infix}-top {\n      position: sticky;\n      top: 0;\n      z-index: $zindex-sticky;\n    }\n\n    .sticky#{$infix}-bottom {\n      position: sticky;\n      bottom: 0;\n      z-index: $zindex-sticky;\n    }\n  }\n}\n", "// scss-docs-start stacks\n.hstack {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  align-self: stretch;\n}\n\n.vstack {\n  display: flex;\n  flex: 1 1 auto;\n  flex-direction: column;\n  align-self: stretch;\n}\n// scss-docs-end stacks\n", "//\n// Visually hidden\n//\n\n.visually-hidden,\n.visually-hidden-focusable:not(:focus):not(:focus-within) {\n  @include visually-hidden();\n}\n", "// stylelint-disable declaration-no-important\n\n// Hide content visually while keeping it accessible to assistive technologies\n//\n// See: https://www.a11yproject.com/posts/2013-01-11-how-to-hide-content/\n// See: https://kittygiraudel.com/2016/10/13/css-hide-and-seek/\n\n@mixin visually-hidden() {\n  width: 1px !important;\n  height: 1px !important;\n  padding: 0 !important;\n  margin: -1px !important; // Fix for https://github.com/twbs/bootstrap/issues/25686\n  overflow: hidden !important;\n  clip: rect(0, 0, 0, 0) !important;\n  white-space: nowrap !important;\n  border: 0 !important;\n\n  // Fix for positioned table caption that could become anonymous cells\n  &:not(caption) {\n    position: absolute !important;\n  }\n}\n\n// Use to only display content when it's focused, or one of its child elements is focused\n// (i.e. when focus is within the element/container that the class was applied to)\n//\n// Useful for \"Skip to main content\" links; see https://www.w3.org/TR/2013/NOTE-WCAG20-TECHS-20130905/G1\n\n@mixin visually-hidden-focusable() {\n  &:not(:focus):not(:focus-within) {\n    @include visually-hidden();\n  }\n}\n", "//\n// Stretched link\n//\n\n.stretched-link {\n  &::#{$stretched-link-pseudo-element} {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    z-index: $stretched-link-z-index;\n    content: \"\";\n  }\n}\n", "//\n// Text truncation\n//\n\n.text-truncate {\n  @include text-truncate();\n}\n", "// Text truncate\n// Requires inline-block or block for proper styling\n\n@mixin text-truncate() {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n", ".vr {\n  display: inline-block;\n  align-self: stretch;\n  width: $vr-border-width;\n  min-height: 1em;\n  background-color: currentcolor;\n  opacity: $hr-opacity;\n}\n", "// Utility generator\n// Used to generate utilities & print utilities\n@mixin generate-utility($utility, $infix: \"\", $is-rfs-media-query: false) {\n  $values: map-get($utility, values);\n\n  // If the values are a list or string, convert it into a map\n  @if type-of($values) == \"string\" or type-of(nth($values, 1)) != \"list\" {\n    $values: zip($values, $values);\n  }\n\n  @each $key, $value in $values {\n    $properties: map-get($utility, property);\n\n    // Multiple properties are possible, for example with vertical or horizontal margins or paddings\n    @if type-of($properties) == \"string\" {\n      $properties: append((), $properties);\n    }\n\n    // Use custom class if present\n    $property-class: if(map-has-key($utility, class), map-get($utility, class), nth($properties, 1));\n    $property-class: if($property-class == null, \"\", $property-class);\n\n    // Use custom CSS variable name if present, otherwise default to `class`\n    $css-variable-name: if(map-has-key($utility, css-variable-name), map-get($utility, css-variable-name), map-get($utility, class));\n\n    // State params to generate pseudo-classes\n    $state: if(map-has-key($utility, state), map-get($utility, state), ());\n\n    $infix: if($property-class == \"\" and str-slice($infix, 1, 1) == \"-\", str-slice($infix, 2), $infix);\n\n    // Don't prefix if value key is null (e.g. with shadow class)\n    $property-class-modifier: if($key, if($property-class == \"\" and $infix == \"\", \"\", \"-\") + $key, \"\");\n\n    @if map-get($utility, rfs) {\n      // Inside the media query\n      @if $is-rfs-media-query {\n        $val: rfs-value($value);\n\n        // Do not render anything if fluid and non fluid values are the same\n        $value: if($val == rfs-fluid-value($value), null, $val);\n      }\n      @else {\n        $value: rfs-fluid-value($value);\n      }\n    }\n\n    $is-css-var: map-get($utility, css-var);\n    $is-local-vars: map-get($utility, local-vars);\n    $is-rtl: map-get($utility, rtl);\n\n    @if $value != null {\n      @if $is-rtl == false {\n        /* rtl:begin:remove */\n      }\n\n      @if $is-css-var {\n        .#{$property-class + $infix + $property-class-modifier} {\n          --#{$prefix}#{$css-variable-name}: #{$value};\n        }\n\n        @each $pseudo in $state {\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\n            --#{$prefix}#{$css-variable-name}: #{$value};\n          }\n        }\n      } @else {\n        .#{$property-class + $infix + $property-class-modifier} {\n          @each $property in $properties {\n            @if $is-local-vars {\n              @each $local-var, $variable in $is-local-vars {\n                --#{$prefix}#{$local-var}: #{$variable};\n              }\n            }\n            #{$property}: $value if($enable-important-utilities, !important, null);\n          }\n        }\n\n        @each $pseudo in $state {\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\n            @each $property in $properties {\n              @if $is-local-vars {\n                @each $local-var, $variable in $is-local-vars {\n                  --#{$prefix}#{$local-var}: #{$variable};\n                }\n              }\n              #{$property}: $value if($enable-important-utilities, !important, null);\n            }\n          }\n        }\n      }\n\n      @if $is-rtl == false {\n        /* rtl:end:remove */\n      }\n    }\n  }\n}\n", "// Loop over each breakpoint\n@each $breakpoint in map-keys($grid-breakpoints) {\n\n  // Generate media query if needed\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    // Loop over each utility property\n    @each $key, $utility in $utilities {\n      // The utility can be disabled with `false`, thus check if the utility is a map first\n      // Only proceed if responsive media queries are enabled or if it's the base media query\n      @if type-of($utility) == \"map\" and (map-get($utility, responsive) or $infix == \"\") {\n        @include generate-utility($utility, $infix);\n      }\n    }\n  }\n}\n\n// RFS rescaling\n@media (min-width: $rfs-mq-value) {\n  @each $breakpoint in map-keys($grid-breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @if (map-get($grid-breakpoints, $breakpoint) < $rfs-breakpoint) {\n      // Loop over each utility property\n      @each $key, $utility in $utilities {\n        // The utility can be disabled with `false`, thus check if the utility is a map first\n        // Only proceed if responsive media queries are enabled or if it's the base media query\n        @if type-of($utility) == \"map\" and map-get($utility, rfs) and (map-get($utility, responsive) or $infix == \"\") {\n          @include generate-utility($utility, $infix, true);\n        }\n      }\n    }\n  }\n}\n\n\n// Print utilities\n@media print {\n  @each $key, $utility in $utilities {\n    // The utility can be disabled with `false`, thus check if the utility is a map first\n    // Then check if the utility needs print styles\n    @if type-of($utility) == \"map\" and map-get($utility, print) == true {\n      @include generate-utility($utility, \"-print\");\n    }\n  }\n}\n"], "names": [], "sourceRoot": ""}